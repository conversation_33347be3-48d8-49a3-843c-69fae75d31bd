package com.example.coloringproject

import android.content.Context
import android.content.res.AssetManager
import com.example.coloringproject.utils.EnhancedAssetManager
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner
import java.io.ByteArrayInputStream
import java.io.IOException

/**
 * 增强资源管理器测试
 * 验证JSON和PNG文件匹配逻辑
 */
@RunWith(MockitoJUnitRunner::class)
class EnhancedAssetManagerTest {

    @Mock
    private lateinit var mockContext: Context

    @Mock
    private lateinit var mockAssetManager: AssetManager

    private lateinit var enhancedAssetManager: EnhancedAssetManager

    @Before
    fun setup() {
        `when`(mockContext.assets).thenReturn(mockAssetManager)
        enhancedAssetManager = EnhancedAssetManager(mockContext)
    }

    @Test
    fun testValidProjectDetection() = runBlocking {
        // 模拟assets文件列表 - 有效的成对文件
        val assetFiles = arrayOf(
            "project1.json",
            "project1_outline.png",
            "project2_android.json", 
            "project2_android_outline.png",
            "project3_compressed.json",
            "project3_outline.png",
            "project3_regions.png"
        )

        `when`(mockAssetManager.list("")).thenReturn(assetFiles)

        // 模拟JSON文件内容
        val validJsonContent = """
        {
            "regions": [
                {"id": 1, "color": [255, 0, 0], "pixels": [[0, 0], [1, 0]]}
            ],
            "colorPalette": [
                {"id": 1, "color": [255, 0, 0]}
            ],
            "metadata": {
                "totalRegions": 1,
                "totalColors": 1,
                "difficulty": "easy"
            }
        }
        """.trimIndent()

        // 模拟文件存在检查
        `when`(mockAssetManager.open(anyString())).thenReturn(
            ByteArrayInputStream(validJsonContent.toByteArray())
        )

        val result = enhancedAssetManager.getValidatedProjects()
        
        assert(result.isSuccess) { "应该成功获取项目列表" }
        
        val projects = result.getOrNull()!!
        assert(projects.isNotEmpty()) { "应该找到项目" }
        
        val validProjects = projects.filter { it.isValid }
        assert(validProjects.isNotEmpty()) { "应该有有效项目" }
        

    }

    @Test
    fun testInvalidProjectDetection() = runBlocking {
        // 模拟assets文件列表 - 缺少匹配文件
        val assetFiles = arrayOf(
            "orphan1.json",           // 只有JSON，没有PNG
            "orphan2_outline.png",    // 只有PNG，没有JSON
            "mismatch.json",          // JSON存在但PNG不匹配
            "different_outline.png"   // PNG存在但JSON不匹配
        )

        `when`(mockAssetManager.list("")).thenReturn(assetFiles)

        // 模拟JSON文件内容
        val validJsonContent = """
        {
            "regions": [{"id": 1, "color": [255, 0, 0], "pixels": [[0, 0]]}],
            "colorPalette": [{"id": 1, "color": [255, 0, 0]}],
            "metadata": {"totalRegions": 1, "totalColors": 1}
        }
        """.trimIndent()

        `when`(mockAssetManager.open(contains(".json"))).thenReturn(
            ByteArrayInputStream(validJsonContent.toByteArray())
        )

        // 模拟PNG文件不存在
        `when`(mockAssetManager.open(contains("_outline.png"))).thenThrow(IOException("File not found"))

        val result = enhancedAssetManager.getValidatedProjects()
        
        assert(result.isSuccess) { "应该成功获取项目列表（即使项目无效）" }
        
        val projects = result.getOrNull()!!
        val invalidProjects = projects.filter { !it.isValid }
        
        assert(invalidProjects.isNotEmpty()) { "应该检测到无效项目" }
        

    }

    @Test
    fun testFileMatching() {
        // 测试文件名匹配逻辑
        val testCases = listOf(
            // 基础名称 to 期望的匹配文件
            "project1" to listOf("project1_outline.png", "project1.png"),
            "project2_android" to listOf("project2_android_outline.png", "project2_outline.png"),
            "project3_compressed" to listOf("project3_compressed_outline.png", "project3_outline.png"),
            "project4_ultra" to listOf("project4_ultra_outline.png", "project4_outline.png")
        )

        testCases.forEach { (basename, expectedFiles) ->
            println("测试基础名称: $basename")
            println("期望匹配文件: ${expectedFiles.joinToString()}")
            
            // 这里可以测试内部的文件匹配逻辑
            // 由于方法是私有的，我们通过公共方法间接测试
        }
    }

    @Test
    fun testProjectValidation() = runBlocking {
        // 测试项目验证逻辑
        val validAssets = arrayOf(
            "test_project.json",
            "test_project_outline.png"
        )

        `when`(mockAssetManager.list("")).thenReturn(validAssets)

        // 测试不同的JSON内容
        val testCases = listOf(
            // 有效的JSON
            """
            {
                "regions": [{"id": 1, "color": [255, 0, 0], "pixels": [[0, 0]]}],
                "colorPalette": [{"id": 1, "color": [255, 0, 0]}],
                "metadata": {"totalRegions": 1, "totalColors": 1}
            }
            """ to true,
            
            // 无效的JSON - 缺少regions
            """
            {
                "colorPalette": [{"id": 1, "color": [255, 0, 0]}],
                "metadata": {"totalRegions": 1, "totalColors": 1}
            }
            """ to false,
            
            // 无效的JSON - 空的regions
            """
            {
                "regions": [],
                "colorPalette": [{"id": 1, "color": [255, 0, 0]}],
                "metadata": {"totalRegions": 1, "totalColors": 1}
            }
            """ to false
        )

        testCases.forEachIndexed { index, (jsonContent, shouldBeValid) ->
            println("测试用例 ${index + 1}: 期望${if (shouldBeValid) "有效" else "无效"}")
            
            `when`(mockAssetManager.open("test_project.json")).thenReturn(
                ByteArrayInputStream(jsonContent.trimIndent().toByteArray())
            )

            val result = enhancedAssetManager.getValidatedProjects()
            
            if (result.isSuccess) {
                val projects = result.getOrNull()!!
                val project = projects.firstOrNull()
                
                if (project != null) {
                    val isActuallyValid = project.isValid
                    println("实际结果: ${if (isActuallyValid) "有效" else "无效"}")
                    
                    if (!isActuallyValid) {
                        println("验证错误: ${project.validationErrors.joinToString()}")
                    }
                    
                    assert(isActuallyValid == shouldBeValid) {
                        "测试用例 ${index + 1} 失败: 期望${if (shouldBeValid) "有效" else "无效"}，实际${if (isActuallyValid) "有效" else "无效"}"
                    }
                }
            }
        }
    }

    @Test
    fun testValidationReport() = runBlocking {
        // 测试验证报告生成
        val assetFiles = arrayOf(
            "valid1.json", "valid1_outline.png",
            "valid2.json", "valid2_outline.png", 
            "invalid1.json"  // 缺少PNG
        )

        `when`(mockAssetManager.list("")).thenReturn(assetFiles)

        val validJsonContent = """
        {
            "regions": [{"id": 1, "color": [255, 0, 0], "pixels": [[0, 0]]}],
            "colorPalette": [{"id": 1, "color": [255, 0, 0]}],
            "metadata": {"totalRegions": 1, "totalColors": 1}
        }
        """.trimIndent()

        `when`(mockAssetManager.open(anyString())).thenReturn(
            ByteArrayInputStream(validJsonContent.toByteArray())
        )

        val result = enhancedAssetManager.getValidatedProjects()
        
        if (result.isSuccess) {
            val projects = result.getOrNull()!!
            val report = enhancedAssetManager.getValidationReport(projects)
            
            println("验证报告:")
            println(report)
            
            assert(report.contains("总项目数")) { "报告应包含总项目数" }
            assert(report.contains("有效项目")) { "报告应包含有效项目数" }
            assert(report.contains("无效项目")) { "报告应包含无效项目数" }
        }
    }
}
