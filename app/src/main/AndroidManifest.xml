<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 保存图片到相册的权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/ic_logo"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_logo"
        android:supportsRtl="true"
        android:theme="@style/Theme.ColoringProject"
        android:networkSecurityConfig="@xml/network_security_config"
        android:largeHeap="true"
        android:hardwareAccelerated="true"
        tools:targetApi="31">
        <!-- Splash启动页面 -->
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.ColoringProject.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 主页面 -->
        <activity
            android:name=".EnhancedMainActivity"
            android:exported="false" />
        <activity
            android:name=".RefactoredSimpleMainActivity"
            android:exported="true"
            android:theme="@style/Theme.ColoringProject">

<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
        </activity>

        <!-- 测试相关Activity -->
        <activity
            android:name=".TestLauncherActivity"
            android:exported="true"
            android:label="测试中心"
            android:theme="@style/Theme.ColoringProject">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>