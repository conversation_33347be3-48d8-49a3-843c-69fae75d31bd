<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/card_background" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/overlay_light" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <layer-list>
            <item android:top="2dp" android:left="1dp" android:right="1dp">
                <shape android:shape="rectangle">
                    <solid android:color="@color/card_shadow" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <item android:bottom="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="@color/card_background" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>
