<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape
            android:innerRadiusRatio="3"
            android:shape="ring"
            android:thicknessRatio="8"
            android:useLevel="false">
            <solid android:color="@color/divider_color" />
        </shape>
    </item>
    <item>
        <rotate
            android:fromDegrees="270"
            android:toDegrees="270">
            <shape
                android:innerRadiusRatio="3"
                android:shape="ring"
                android:thicknessRatio="8"
                android:useLevel="true">
                <gradient
                    android:endColor="@color/accent_color"
                    android:startColor="@color/progress_color"
                    android:type="sweep" />
            </shape>
        </rotate>
    </item>
</layer-list>
