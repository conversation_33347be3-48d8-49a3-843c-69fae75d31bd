<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- 主题颜色 -->
    <color name="primary">#FF3B82F6</color>

    <!-- 混合资源管理相关颜色 -->
    <color name="difficulty_easy">#4CAF50</color>
    <color name="difficulty_medium">#FF9800</color>
    <color name="difficulty_hard">#F44336</color>
    <color name="difficulty_unknown">#9E9E9E</color>

    <color name="source_builtin">#2196F3</color>
    <color name="source_downloaded">#4CAF50</color>
    <color name="source_remote">#FF9800</color>

    <color name="gray_light">#F5F5F5</color>
    <color name="black_transparent_50">#80000000</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_hint">#BDBDBD</color>

    <!-- 应用主题颜色 - 参考设计图优化 -->
    <color name="background_color">#FFF8F9FA</color>
    <color name="toolbar_background">#FFFFFFFF</color>
    <color name="canvas_background">#FFFFFFFF</color>
    <color name="palette_background">#FF424242</color>

    <!-- 边框颜色 -->
    <color name="color_border">#FFE5E7EB</color>
    <color name="selected_color_border">#FF3B82F6</color>

    <!-- 进度显示颜色 -->
    <color name="completed_color">#FF10B981</color>
    <color name="progress_color">#FF3B82F6</color>
    <color name="default_text_color">#FF6B7280</color>

    <!-- UI组件颜色 -->
    <color name="text_tertiary">#FF9CA3AF</color>
    <color name="divider_color">#FFF3F4F6</color>
    <color name="icon_tint">#FF6B7280</color>
    <color name="image_placeholder_background">#FFF9FAFB</color>

    <!-- 沉浸式UI颜色 -->
    <color name="card_background">#FFFFFFFF</color>
    <color name="card_shadow">#0D000000</color>
    <color name="accent_color">#FF3B82F6</color>
    <color name="success_color">#FF10B981</color>
    <color name="warning_color">#FFF59E0B</color>
    <color name="error_color">#FFEF4444</color>

    <!-- 分类标签颜色 -->
    <color name="category_selected">#FF3B82F6</color>
    <color name="category_unselected">#FFF3F4F6</color>
    <color name="category_text_selected">#FFFFFFFF</color>
    <color name="category_text_unselected">#FF6B7280</color>

    <!-- 底部导航颜色 -->
    <color name="bottom_nav_selected">#FF3B82F6</color>
    <color name="bottom_nav_unselected">#FF9CA3AF</color>
    <color name="bottom_nav_background">#FFFFFFFF</color>

    <!-- 背景颜色 -->
    <color name="background_light">#F8F9FA</color>

    <!-- 渐变颜色 -->
    <color name="gradient_start">#FF3B82F6</color>
    <color name="gradient_end">#FF8B5CF6</color>

    <!-- 半透明颜色 -->
    <color name="overlay_dark">#80000000</color>
    <color name="overlay_light">#80FFFFFF</color>
    <color name="scrim_background">#B3000000</color>
</resources>