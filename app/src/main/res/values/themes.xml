<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.ColoringProject" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/accent_color</item>
        <item name="colorPrimaryVariant">@color/gradient_end</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/success_color</item>
        <item name="colorSecondaryVariant">@color/warning_color</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/card_background</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:statusBarColor">@color/background_color</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@color/bottom_nav_background</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>

    <style name="Theme.ColoringProject" parent="Base.Theme.ColoringProject" />

    <!-- Splash页面主题 -->
    <style name="Theme.ColoringProject.Splash" parent="Base.Theme.ColoringProject">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>

    <!-- 分类标签样式 -->
    <style name="CategoryChip">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">36dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="android:paddingHorizontal">20dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/category_text_unselected</item>
        <item name="android:background">@drawable/category_chip_background</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:elevation">2dp</item>
    </style>

    <!-- 项目卡片样式 -->
    <style name="ProjectCard">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:background">@drawable/project_card_background</item>
        <item name="android:elevation">4dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:foreground">?attr/selectableItemBackground</item>
    </style>

    <!-- 标题文本样式 -->
    <style name="TitleText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:letterSpacing">-0.02</item>
    </style>

    <!-- 副标题文本样式 -->
    <style name="SubtitleText">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:letterSpacing">0.01</item>
    </style>

    <!-- 正文文本样式 -->
    <style name="BodyText">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>

    <!-- 说明文本样式 -->
    <style name="CaptionText">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_tertiary</item>
    </style>

    <!-- 底部导航活动指示器样式 -->
    <style name="BottomNavActiveIndicator">
        <item name="android:color">@color/bottom_nav_selected</item>
        <item name="android:width">24dp</item>
        <item name="android:height">24dp</item>
    </style>

</resources>