<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardProject"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 预览图片 -->
        <ImageView
            android:id="@+id/ivProjectPreview"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            android:background="@color/image_placeholder_background"
            tools:src="@drawable/ic_image_placeholder" />

        <!-- 项目信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- 项目名称 -->
            <TextView
                android:id="@+id/tvProjectName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="项目名称"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- 难度标签 -->
            <TextView
                android:id="@+id/tvDifficulty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="中等"
                android:textSize="12sp"
                android:textStyle="bold"
                android:background="@drawable/difficulty_badge_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp" />

            <!-- 项目详情 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <!-- 区域数量 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_regions"
                        android:tint="@color/icon_tint" />

                    <TextView
                        android:id="@+id/tvRegions"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="120区域"
                        android:textSize="10sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <!-- 颜色数量 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_colors"
                        android:tint="@color/icon_tint" />

                    <TextView
                        android:id="@+id/tvColors"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="15颜色"
                        android:textSize="10sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

            <!-- 文件大小 -->
            <TextView
                android:id="@+id/tvSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="1.2MB"
                android:textSize="10sp"
                android:textColor="@color/text_hint"
                android:layout_gravity="end" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
