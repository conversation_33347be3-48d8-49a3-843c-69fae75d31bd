<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 页面标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="每日挑战"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- 今日挑战卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardTodayChallenge"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 挑战图片 -->
                <ImageView
                    android:id="@+id/ivTodayChallengeImage"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:scaleType="centerCrop"
                    android:background="@color/image_placeholder_background"
                    android:layout_marginBottom="16dp"
                    tools:src="@drawable/ic_image_placeholder" />

                <!-- 挑战标题 -->
                <TextView
                    android:id="@+id/tvTodayChallengeTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="今日挑战"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp" />

                <!-- 挑战描述 -->
                <TextView
                    android:id="@+id/tvTodayChallengeDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="完成今日的特殊挑战，获得额外奖励！"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="16dp" />

                <!-- 进度条 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <ProgressBar
                        android:id="@+id/progressTodayChallenge"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="0dp"
                        android:layout_height="8dp"
                        android:layout_weight="1"
                        android:progress="0"
                        android:max="100"
                        android:progressTint="@color/progress_color" />

                    <TextView
                        android:id="@+id/tvTodayChallengeProgress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0%"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginStart="12dp" />

                </LinearLayout>

                <!-- 开始挑战按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnStartTodayChallenge"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="开始挑战"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 统计信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp">

                <!-- 连续天数 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvStreakCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/progress_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="连续天数"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <!-- 分隔线 -->
                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/divider_color"
                    android:layout_marginHorizontal="16dp" />

                <!-- 完成挑战数 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvCompletedChallenges"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/completed_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="完成挑战"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <!-- 分隔线 -->
                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/divider_color"
                    android:layout_marginHorizontal="16dp" />

                <!-- 总积分 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tvTotalPoints"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/toolbar_background" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="总积分"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 挑战历史标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="挑战历史"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="12dp" />

        <!-- 挑战历史列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewChallengeHistory"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            tools:listitem="@layout/item_daily_challenge" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
