<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardProject"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="3dp"
    app:cardBackgroundColor="@color/card_background"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 项目图片容器 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="180dp">

            <!-- 项目预览图片 -->
            <ImageView
                android:id="@+id/ivProjectImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/image_placeholder_background"
                tools:src="@drawable/ic_image_placeholder" />

            <!-- 渐变遮罩 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_gravity="bottom"
                android:background="@drawable/gradient_overlay" />

        </FrameLayout>

        <!-- 项目信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 项目标题 -->
            <TextView
                android:id="@+id/tvProjectTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="项目名称"
                style="@style/BodyText"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginBottom="6dp" />

            <!-- 项目统计信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- 区域数 -->
                <TextView
                    android:id="@+id/tvProjectRegions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="50 区域"
                    style="@style/CaptionText"
                    android:drawableStart="@drawable/ic_regions"
                    android:drawablePadding="4dp"
                    android:drawableTint="@color/text_tertiary"
                    android:layout_marginEnd="12dp" />

                <!-- 预计时间 -->
                <TextView
                    android:id="@+id/tvProjectTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="30 分钟"
                    style="@style/CaptionText"
                    android:drawableStart="@drawable/ic_time"
                    android:drawablePadding="4dp"
                    android:drawableTint="@color/text_tertiary" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <!-- 难度标签 -->
                <TextView
                    android:id="@+id/tvProjectDifficulty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="简单"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    android:textColor="@color/category_text_selected"
                    android:background="@drawable/category_chip_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="3dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
