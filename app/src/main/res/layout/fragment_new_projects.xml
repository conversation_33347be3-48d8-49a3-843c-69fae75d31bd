<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 主要内容区域 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshNewProjects"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 顶部分类标签栏 -->
                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none"
                    android:layout_marginTop="16dp"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="8dp">

                    <LinearLayout
                        android:id="@+id/categoryTabsContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="8dp">

                        <!-- 分类标签将动态创建 -->

                    </LinearLayout>

                </HorizontalScrollView>

                <!-- 搜索栏 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    app:cardCornerRadius="12dp"
                    android:visibility="gone"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@color/card_background">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <EditText
                            android:id="@+id/etSearchNewProjects"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="搜索填色项目..."
                            android:drawableStart="@drawable/ic_search"
                            android:drawablePadding="12dp"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:textSize="16sp"
                            android:textColorHint="@color/text_hint" />

                        <ImageButton
                            android:id="@+id/btnFilterNewProjects"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:src="@drawable/ic_filter"
                            android:contentDescription="筛选"
                            android:layout_marginStart="8dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- 项目统计信息 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="12dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tvNewProjectCount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="共0个项目"
                        style="@style/SubtitleText" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="最新更新"
                        style="@style/CaptionText"
                        android:drawableStart="@drawable/ic_new_projects"
                        android:drawablePadding="4dp"
                        android:drawableTint="@color/text_tertiary" />

                </LinearLayout>

                <!-- 项目网格 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewNewProjects"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:paddingHorizontal="12dp"
                    android:paddingBottom="16dp"
                    android:clipToPadding="false"
                    tools:listitem="@layout/item_enhanced_project"
                    tools:spanCount="2" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 随机项目浮动按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabRandomProject"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_random"
        android:contentDescription="随机项目"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
