<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardProject"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 预览图片区域 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="160dp">

            <ImageView
                android:id="@+id/imagePreview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/gray_light"
                tools:src="@drawable/ic_image_placeholder" />

            <!-- 下载进度覆盖层 -->
            <LinearLayout
                android:id="@+id/layoutProgress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black_transparent_50"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ProgressBar
                    android:id="@+id/progressDownload"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="120dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:progressTint="@color/white" />

                <TextView
                    android:id="@+id/textProgress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="45%" />

            </LinearLayout>

            <!-- 操作按钮 -->
            <ImageButton
                android:id="@+id/buttonAction"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:background="@drawable/bg_circle_white"
                android:elevation="4dp"
                android:padding="12dp"
                android:src="@drawable/ic_download"
                android:tint="@color/primary" />

        </FrameLayout>

        <!-- 项目信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 标题和标签 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:id="@+id/textTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="美丽花园" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipSource"
                    style="@style/Widget.Material3.Chip.Assist"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textSize="10sp"
                    app:chipMinHeight="24dp"
                    tools:text="内置" />

            </LinearLayout>

            <!-- 描述 -->
            <TextView
                android:id="@+id/textDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="一个充满色彩的花园场景，适合放松心情" />

            <!-- 难度和统计信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipDifficulty"
                    style="@style/Widget.Material3.Chip.Assist"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="10sp"
                    app:chipMinHeight="24dp"
                    tools:text="简单" />

                <TextView
                    android:id="@+id/textStats"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="end"
                    tools:text="120区域 · 15颜色 · 30分钟" />

            </LinearLayout>

            <!-- 文件大小 -->
            <TextView
                android:id="@+id/textFileSize"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                android:gravity="end"
                tools:text="2.5MB" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
