<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardChallenge"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左侧日期和状态 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginEnd="16dp">

            <!-- 日期 -->
            <TextView
                android:id="@+id/tvChallengeDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="12/25"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <!-- 状态图标 -->
            <ImageView
                android:id="@+id/ivChallengeStatus"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_play_circle"
                android:tint="@color/progress_color" />

        </LinearLayout>

        <!-- 中间内容 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 挑战标题 -->
            <TextView
                android:id="@+id/tvChallengeTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="速度挑战"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="4dp" />

            <!-- 挑战描述 -->
            <TextView
                android:id="@+id/tvChallengeDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="在15分钟内完成一个中等难度的项目"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="8dp" />

            <!-- 进度条 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ProgressBar
                    android:id="@+id/progressChallenge"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="0dp"
                    android:layout_height="6dp"
                    android:layout_weight="1"
                    android:progress="0"
                    android:max="100"
                    android:progressTint="@color/progress_color" />

                <TextView
                    android:id="@+id/tvChallengeProgress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 右侧奖励 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginStart="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_star"
                android:tint="@color/toolbar_background"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tvChallengeReward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+100 积分"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/toolbar_background"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
