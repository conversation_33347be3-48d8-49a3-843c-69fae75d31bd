<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="@color/background_color">

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_colors"
        android:tint="@color/toolbar_background"
        android:layout_marginBottom="24dp" />

    <TextView
        android:id="@+id/tvColoringTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="填色功能"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvColoringMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="填色功能正在开发中...\n即将为您提供沉浸式的填色体验！"
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:lineSpacingExtra="4dp" />

</LinearLayout>
