<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="API接口测试"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:visibility="gone"
        android:layout_marginBottom="16dp" />

    <!-- 测试按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btnTestProjects"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="测试项目列表"
                android:layout_marginEnd="4dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <Button
                android:id="@+id/btnTestCategories"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="测试分类列表"
                android:layout_marginStart="4dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btnTestDaily"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="测试每日推荐"
                android:layout_marginEnd="4dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <Button
                android:id="@+id/btnTestUpdates"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="测试更新检查"
                android:layout_marginStart="4dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btnTestHybridManager"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="测试混合管理器"
                android:layout_marginEnd="4dp"
                style="@style/Widget.Material3.Button" />

            <Button
                android:id="@+id/btnTestCategorySystem"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="测试分类系统"
                android:layout_marginStart="4dp"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnClearLog"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="清空日志"
                android:layout_marginEnd="4dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <Button
                android:id="@+id/btnAssetsDebug"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Assets调试"
                android:layout_marginStart="4dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </LinearLayout>

    <!-- 日志显示区域 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="测试日志："
        android:textStyle="bold"
        android:textSize="16sp"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/bg_log_area"
        android:padding="12dp">

        <TextView
            android:id="@+id/tvLog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:textColor="@color/text_primary"
            android:text="点击上方按钮开始测试API接口..." />

    </ScrollView>

</LinearLayout>
