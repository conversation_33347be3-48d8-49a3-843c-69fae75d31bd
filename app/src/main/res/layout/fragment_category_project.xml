<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 主要内容区域 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshProjects"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 项目统计信息 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="12dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tvProjectCount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="共0个项目"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="分类项目"
                        android:textSize="12sp"
                        android:textColor="@color/text_tertiary"
                        android:drawableStart="@drawable/ic_category"
                        android:drawablePadding="4dp"
                        android:drawableTint="@color/text_tertiary" />

                </LinearLayout>

                <!-- 项目网格 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewProjects"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:paddingHorizontal="12dp"
                    android:paddingBottom="16dp"
                    android:clipToPadding="false"
                    tools:listitem="@layout/item_enhanced_project"
                    tools:spanCount="2" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <!-- 随机项目浮动按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabRandomProject"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_random"
        android:contentDescription="随机项目"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
