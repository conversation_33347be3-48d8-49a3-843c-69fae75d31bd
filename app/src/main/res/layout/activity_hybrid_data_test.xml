<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="混合数据加载测试"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试功能"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_test_gallery_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🔍 Gallery筛选器测试"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_test_all"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🔄 完整混合数据测试"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_test_categories"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🏷️ 分类映射"
                        android:layout_marginEnd="4dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_test_server_categories"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🌐 服务器分类"
                        android:layout_marginStart="4dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_test_hybrid_loading"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🔄 混合项目加载"
                    android:layout_marginTop="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="8dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_test_downloaded_files"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="📁 检查下载"
                        android:layout_marginEnd="4dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_clean_downloads"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="🗑️ 清理下载"
                        android:layout_marginStart="4dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试结果"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_results"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="点击测试按钮开始...\n\n功能说明:\n• 分类映射: 测试服务器分类名称到assets分类的映射\n• 服务器分类: 测试从服务器获取分类列表并合并\n• 混合加载: 测试优先加载assets数据，然后补充服务器数据"
                    android:textSize="14sp"
                    android:fontFamily="monospace"
                    android:minHeight="400dp"
                    android:gravity="top" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
