<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".MainActivity">

    <!-- 顶部工具栏 -->
    <LinearLayout
        android:id="@+id/topToolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/toolbar_background"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 项目选择按钮 -->
        <Button
            android:id="@+id/btnSelectProject"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="1dp"
            android:paddingRight="1dp"
            android:text="pro"
            style="@style/Widget.Material3.Button.OutlinedButton" />



        <!-- 定位按钮 -->
        <Button
            android:id="@+id/btnToggleHints"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="1dp"
            android:paddingRight="1dp"
            android:text="定位"
            android:textSize="12sp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <!-- 自动演示按钮 -->
        <Button
            android:id="@+id/btnAutoDemo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="演示"
            android:textSize="12sp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <!-- 重置按钮 -->
        <Button
            android:id="@+id/btnReset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="重置"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <!-- 撤销按钮 -->
        <Button
            android:id="@+id/btnUndo"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="撤销"
            android:textSize="12sp"
            android:enabled="false" />

        <!-- 重做按钮 -->
        <Button
            android:id="@+id/btnRedo"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="重做"
            android:textSize="12sp"
            android:enabled="false" />

        <!-- 保存按钮 -->
        <Button
            android:id="@+id/btnSave"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="保存"
            android:textSize="12sp" />

        <!-- 图库按钮 -->
        <Button
            android:id="@+id/btnGallery"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="图库"
            android:textSize="12sp" />

    </LinearLayout>


    <com.example.coloringproject.view.ColoringView
        android:id="@+id/coloringView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@android:color/white"
        app:layout_constraintTop_toBottomOf="@id/topToolbar"
        app:layout_constraintBottom_toTopOf="@id/bottomPanel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 进度显示 -->
    <LinearLayout
        app:layout_constraintTop_toBottomOf="@+id/topToolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="horizontal"
        android:gravity="center">

        <TextView
            android:id="@+id/tvProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0 / 0"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="4dp"
            android:max="100"
            android:progress="0" />

    </LinearLayout>
    <!-- 缩放控制按钮 -->
    <LinearLayout
        android:id="@+id/zoomControls"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="200dp"
        android:background="@drawable/rounded_background"
        android:padding="8dp"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <Button
            android:id="@+id/btnZoomIn"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="+"
            android:textSize="20sp"
            android:textStyle="bold"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btnZoomOut"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="4dp"
            android:text="−"
            android:textSize="20sp"
            android:textStyle="bold"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btnZoomFit"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="4dp"
            android:text="⌂"
            android:textSize="16sp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

    <!-- 填色画布 -->


    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/loadingIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/coloringView"
        app:layout_constraintBottom_toBottomOf="@id/coloringView"
        app:layout_constraintStart_toStartOf="@id/coloringView"
        app:layout_constraintEnd_toEndOf="@id/coloringView" />

    <!-- 状态文本 -->
    <TextView
        android:id="@+id/tvStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="请选择一个填色项目"
        android:textSize="18sp"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="@id/coloringView"
        app:layout_constraintBottom_toBottomOf="@id/coloringView"
        app:layout_constraintStart_toStartOf="@id/coloringView"
        app:layout_constraintEnd_toEndOf="@id/coloringView" />

    <!-- 底部调色板面板 -->
    <LinearLayout
        android:id="@+id/bottomPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/darker_gray"
        android:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 当前颜色显示 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="当前颜色："
                android:textSize="14sp"
                android:textColor="@android:color/white" />

            <View
                android:id="@+id/currentColorView"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="@android:color/white" />

            <TextView
                android:id="@+id/tvCurrentColorName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="未选择"
                android:textSize="14sp"
                android:textColor="@android:color/white" />

            <!-- 下一个颜色按钮 -->
            <Button
                android:id="@+id/btnNextColor"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="下一个"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- 调色板 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewColors"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:paddingHorizontal="8dp"
            android:paddingVertical="8dp"
            android:clipToPadding="false" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>