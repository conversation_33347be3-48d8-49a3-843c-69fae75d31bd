<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 顶部搜索栏 -->
    <LinearLayout
        android:id="@+id/searchLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/toolbar_background"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <EditText
            android:id="@+id/etSearch"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:hint="搜索填色项目..."
            android:background="@drawable/search_background"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:drawableStart="@drawable/ic_search"
            android:drawablePadding="8dp"
            android:textSize="14sp" />

        <ImageButton
            android:id="@+id/btnFilter"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/button_background"
            android:src="@drawable/ic_filter"
            android:contentDescription="筛选" />

    </LinearLayout>

    <!-- 分类标签 -->
    <HorizontalScrollView
        android:id="@+id/categoryScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        app:layout_constraintTop_toBottomOf="@id/searchLayout">

        <LinearLayout
            android:id="@+id/categoryLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp">

            <TextView
                android:id="@+id/categoryAll"
                style="@style/CategoryChip"
                android:text="全部" />

            <TextView
                android:id="@+id/categoryEasy"
                style="@style/CategoryChip"
                android:text="简单" />

            <TextView
                android:id="@+id/categoryMedium"
                style="@style/CategoryChip"
                android:text="中等" />

            <TextView
                android:id="@+id/categoryHard"
                style="@style/CategoryChip"
                android:text="困难" />

            <TextView
                android:id="@+id/categoryAnimals"
                style="@style/CategoryChip"
                android:text="动物" />

            <TextView
                android:id="@+id/categoryNature"
                style="@style/CategoryChip"
                android:text="自然" />

        </LinearLayout>

    </HorizontalScrollView>

    <!-- 项目统计信息 -->
    <LinearLayout
        android:id="@+id/statsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/categoryScrollView">

        <TextView
            android:id="@+id/tvProjectCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="共0个项目"
            android:textSize="14sp"
            android:textColor="@color/text_secondary" />

        <View
            android:layout_width="1dp"
            android:layout_height="16dp"
            android:layout_marginHorizontal="16dp"
            android:background="@color/divider_color" />

        <TextView
            android:id="@+id/tvDifficultyDistribution"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="简单: 0 | 中等: 0 | 困难: 0"
            android:textSize="12sp"
            android:textColor="@color/text_secondary" />

    </LinearLayout>

    <!-- 项目网格 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewProjects"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="8dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/statsLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:listitem="@layout/item_project_gallery" />

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/recyclerViewProjects"
        app:layout_constraintBottom_toBottomOf="@id/recyclerViewProjects"
        app:layout_constraintStart_toStartOf="@id/recyclerViewProjects"
        app:layout_constraintEnd_toEndOf="@id/recyclerViewProjects" />

    <!-- 空状态提示 -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/recyclerViewProjects"
        app:layout_constraintBottom_toBottomOf="@id/recyclerViewProjects"
        app:layout_constraintStart_toStartOf="@id/recyclerViewProjects"
        app:layout_constraintEnd_toEndOf="@id/recyclerViewProjects">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@drawable/ic_empty_state"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="没有找到匹配的项目"
            android:textSize="16sp"
            android:textColor="@color/text_secondary" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
