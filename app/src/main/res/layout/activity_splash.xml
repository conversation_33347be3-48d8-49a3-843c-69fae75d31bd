<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_background">

    <!-- 主要内容容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp">

        <!-- Logo区域 -->
        <ImageView
            android:id="@+id/logoImageView"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="24dp"
            android:src="@drawable/ic_launcher_foreground"
            android:scaleType="centerInside"
            android:alpha="0" />

        <!-- 应用名称 -->
        <TextView
            android:id="@+id/appNameTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="涂色大师"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp"
            android:alpha="0" />

        <!-- 副标题 -->
        <TextView
            android:id="@+id/subtitleTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="释放你的创造力"
            android:textSize="16sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="48dp"
            android:alpha="0" />

        <!-- 加载进度区域 -->
        <LinearLayout
            android:id="@+id/loadingContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:alpha="0">

            <!-- 自定义进度条 -->
            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="200dp"
                android:layout_height="6dp"
                android:layout_marginBottom="16dp"
                android:progressDrawable="@drawable/custom_progress_bar"
                android:max="100"
                android:progress="0" />

            <!-- 加载状态文字 -->
            <TextView
                android:id="@+id/loadingTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正在准备画布..."
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

    <!-- 底部版本信息 -->
    <TextView
        android:id="@+id/versionTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="32dp"
        android:text="版本 1.0.0"
        android:textSize="12sp"
        android:textColor="@color/text_hint"
        android:alpha="0" />

    <!-- 装饰性画笔动画 -->
    <ImageView
        android:id="@+id/brushImageView"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="64dp"
        android:layout_marginEnd="32dp"
        android:src="@drawable/ic_colors"
        android:alpha="0"
        android:rotation="15" />

    <!-- 装饰性调色板动画 -->
    <ImageView
        android:id="@+id/paletteImageView"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"
        android:layout_marginBottom="120dp"
        android:layout_marginStart="32dp"
        android:src="@drawable/ic_colors"
        android:alpha="0"
        android:rotation="-20" />

</RelativeLayout>
