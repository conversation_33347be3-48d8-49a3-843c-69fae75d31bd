<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="8dp">

    <!-- 颜色圆圈背景 -->
    <View
        android:id="@+id/colorBackground"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:background="@drawable/color_circle_with_progress" />

    <!-- 进度圆圈 -->

    <!-- 颜色内容 -->
    <ProgressBar
        android:id="@+id/progressCircle"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_gravity="center"
        android:indeterminate="false"
        android:progressDrawable="@drawable/circular_progress_drawable" />

    <View
        android:id="@+id/colorView"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_gravity="center"
        android:background="@drawable/inner_color_circle" />

    <!-- 选中状态指示器 -->
    <ImageView
        android:id="@+id/selectedIndicator"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center"
        android:src="@drawable/ic_check"
        android:visibility="gone" />

</FrameLayout>
