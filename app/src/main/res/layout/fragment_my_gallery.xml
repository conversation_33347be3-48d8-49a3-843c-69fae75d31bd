<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 主要内容区域 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshMyGallery"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 页面标题 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="我的图库"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_my_gallery"
                        android:tint="@color/toolbar_background" />

                </LinearLayout>

                <!-- 筛选芯片 -->
                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="8dp">

                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chipGroupFilter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:singleSelection="true"
                        app:chipSpacingHorizontal="8dp" />

                </HorizontalScrollView>

                <!-- 统计信息 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="作品统计"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tvGalleryStats"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="总计 0 个作品 | 已完成 0 | 进行中 0"
                            android:textSize="14sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- 作品网格 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewMyGallery"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:padding="8dp"
                    android:clipToPadding="false"
                    tools:listitem="@layout/item_my_gallery" />

                <!-- 空状态 -->
                <LinearLayout
                    android:id="@+id/layoutEmptyState"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="32dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:src="@drawable/ic_empty_state"
                        android:tint="@color/text_hint"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:id="@+id/tvEmptyState"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="还没有保存的作品\n开始你的第一个填色项目吧！"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 导出所有作品浮动按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabExportAll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_export"
        android:contentDescription="导出所有作品"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
