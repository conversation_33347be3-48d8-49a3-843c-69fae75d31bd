<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- 分类标签 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginHorizontal="8dp"
        android:background="@color/background_color"
        app:tabMode="scrollable"
        app:tabGravity="start"
        app:tabIndicatorColor="@color/primary"
        app:tabSelectedTextColor="@color/primary"
        app:tabTextColor="@color/text_secondary"
        app:tabIndicatorHeight="3dp"
        app:tabPaddingStart="16dp"
        app:tabPaddingEnd="16dp" />

    <!-- ViewPager2 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>
