<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardGalleryProject"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 项目图片容器 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="160dp">

            <!-- 项目预览图片 -->
            <ImageView
                android:id="@+id/ivGalleryProjectImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/image_placeholder_background"
                tools:src="@drawable/ic_image_placeholder" />

            <!-- 完成徽章 -->
            <View
                android:id="@+id/viewCompletedBadge"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_gravity="top"
                android:background="@color/completed_color"
                android:visibility="gone" />

            <!-- 状态图标 -->
            <ImageView
                android:id="@+id/ivGalleryProjectStatus"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:src="@drawable/ic_play_circle"
                android:background="@drawable/rounded_background"
                android:padding="4dp"
                android:tint="@color/progress_color" />

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|end"
                android:layout_margin="8dp"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/btnShareGalleryProject"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="@drawable/rounded_background"
                    android:src="@drawable/ic_share"
                    android:contentDescription="分享"
                    android:layout_marginEnd="4dp" />

                <ImageButton
                    android:id="@+id/btnDeleteGalleryProject"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="@drawable/rounded_background"
                    android:src="@drawable/ic_delete"
                    android:contentDescription="删除" />

            </LinearLayout>

        </FrameLayout>

        <!-- 项目信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- 项目标题 -->
            <TextView
                android:id="@+id/tvGalleryProjectTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="项目名称"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginBottom="8dp" />

            <!-- 进度条 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <ProgressBar
                    android:id="@+id/progressGalleryProject"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="0dp"
                    android:layout_height="6dp"
                    android:layout_weight="1"
                    android:progress="50"
                    android:max="100"
                    android:progressTint="@color/progress_color" />

                <TextView
                    android:id="@+id/tvGalleryProjectProgress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="50%"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- 最后修改时间 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_time"
                    android:tint="@color/icon_tint" />

                <TextView
                    android:id="@+id/tvGalleryProjectDate"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="2024-01-15 14:30"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginStart="4dp" />

                <!-- 项目类型标识 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="我的作品"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    android:textColor="@color/toolbar_background"
                    android:background="@drawable/category_chip_background"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
