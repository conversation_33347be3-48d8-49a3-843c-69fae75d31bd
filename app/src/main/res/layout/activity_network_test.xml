<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="网络功能测试"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="网络状态"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_network_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="检查中..."
                    android:textSize="14sp"
                    android:fontFamily="monospace" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试按钮"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_test_connection"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="完整网络测试"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_test_categories"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试分类API"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_test_projects"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试项目列表API"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_test_download"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试下载功能"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试结果"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_test_results"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="点击测试按钮开始..."
                    android:textSize="14sp"
                    android:fontFamily="monospace"
                    android:minHeight="200dp"
                    android:gravity="top" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
