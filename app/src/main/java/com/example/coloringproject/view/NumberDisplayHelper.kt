package com.example.coloringproject.view

import android.graphics.*
import com.example.coloringproject.data.Region
import com.example.coloringproject.utils.NumberDisplayInfo
import com.example.coloringproject.utils.NumberDisplayManager
import com.example.coloringproject.utils.SimpleNumberDisplayManager

/**
 * 数字显示辅助类
 * 专门用于优化ColoringView中的数字显示功能
 */
object NumberDisplayHelper {
    
    private val numberDisplayManager = SimpleNumberDisplayManager()
    
    /**
     * 替换ColoringView中的数字绘制逻辑
     * 在图像坐标系下绘制优化的数字
     */
    fun drawOptimizedNumbers(
        canvas: Canvas,
        regions: List<Region>,
        filledRegions: Set<Int>,
        scaleFactor: Float,
        currentColorHex: String?,
        visibleRect: RectF
    ) {
        val numbersToDisplay = numberDisplayManager.calculateNumbersToDisplay(
            regions = regions,
            filledRegions = filledRegions,
            currentScale = scaleFactor,
            selectedColorHex = null, // 显示所有未填色区域，不限制颜色
            visibleRect = visibleRect
        )

        android.util.Log.d("NumberDisplayHelper", "图像坐标系计算出${numbersToDisplay.size}个数字")
        
        if (numbersToDisplay.isNotEmpty()) {
            numberDisplayManager.drawNumbers(canvas, numbersToDisplay)
            
            android.util.Log.d("NumberDisplayHelper", 
                "绘制了${numbersToDisplay.size}个数字，缩放级别: $scaleFactor")
        }
    }
    
    /**
     * 在屏幕坐标系下绘制优化的数字
     * 用于替换ColoringView中的屏幕坐标绘制方法
     */
    fun drawOptimizedNumbersInScreenCoords(
        canvas: Canvas,
        regions: List<Region>,
        filledRegions: Set<Int>,
        scaleFactor: Float,
        currentColorHex: String?,
        matrix: Matrix,
        viewWidth: Int,
        viewHeight: Int
    ) {
        // 计算图像坐标系的可见区域
        val screenRect = RectF(0f, 0f, viewWidth.toFloat(), viewHeight.toFloat())
        val inverseMatrix = Matrix()
        
        if (matrix.invert(inverseMatrix)) {
            inverseMatrix.mapRect(screenRect)
        }
        
        // 获取需要显示的数字 - 显示所有未填色区域的数字
        val numbersToDisplay = numberDisplayManager.calculateNumbersToDisplay(
            regions = regions,
            filledRegions = filledRegions,
            currentScale = scaleFactor,
            selectedColorHex = null, // 显示所有未填色区域，不限制颜色
            visibleRect = screenRect
        )

        android.util.Log.d("NumberDisplayHelper", "屏幕坐标系计算出${numbersToDisplay.size}个数字")
        
        // 在屏幕坐标系下绘制
//        drawNumbersInScreenCoords(canvas, numbersToDisplay, matrix, viewWidth, viewHeight)
    }
    
    /**
     * 在屏幕坐标系下绘制数字
     */
    private fun drawNumbersInScreenCoords(
        canvas: Canvas,
        numbers: List<NumberDisplayInfo>,
        matrix: Matrix,
        viewWidth: Int,
        viewHeight: Int
    ) {
        // 白色描边画笔
        val strokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.WHITE
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
            style = Paint.Style.STROKE
            strokeWidth = 3f
        }

        // 黑色填充画笔
        val fillPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
            style = Paint.Style.FILL
        }
        
        numbers.forEach { numberInfo ->
            // 将图像坐标转换为屏幕坐标
            val imagePoint = floatArrayOf(numberInfo.centerX, numberInfo.centerY)
            matrix.mapPoints(imagePoint)
            
            val screenX = imagePoint[0]
            val screenY = imagePoint[1]
            
            // 检查是否在屏幕范围内
            if (screenX >= -50 && screenX <= viewWidth + 50 &&
                screenY >= -50 && screenY <= viewHeight + 50) {

                strokePaint.textSize = numberInfo.textSize
                fillPaint.textSize = numberInfo.textSize

                val numberText = numberInfo.number.toString()
                val textBounds = Rect()
                fillPaint.getTextBounds(numberText, 0, numberText.length, textBounds)

                val textY = screenY + textBounds.height() / 2f

                // 先绘制白色描边，再绘制黑色填充
                canvas.drawText(numberText, screenX, textY, strokePaint)
                canvas.drawText(numberText, screenX, textY, fillPaint)
            }
        }
        
        if (numbers.isNotEmpty()) {
            android.util.Log.d("NumberDisplayHelper", 
                "在屏幕坐标系绘制了${numbers.size}个数字")
        }
    }
    
    /**
     * 获取颜色映射信息（用于调试）
     */
    fun getColorMappingInfo(regions: List<Region>): Map<String, Int> {
        val uniqueColors = regions.map { normalizeColorHex(it.colorHex) }
            .distinct()
            .sorted()
        
        return uniqueColors.mapIndexed { index, color ->
            color to (index + 1)
        }.toMap()
    }
    
    /**
     * 标准化颜色格式
     */
    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        if (normalized.length == 4) {
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }
}