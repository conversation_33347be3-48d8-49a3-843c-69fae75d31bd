package com.example.coloringproject.view

import android.graphics.*
import com.example.coloringproject.data.Region
import com.example.coloringproject.utils.OptimizedMosaicRenderer
import com.example.coloringproject.utils.SmartNumberDisplayManager
import com.example.coloringproject.utils.NumberDisplay

/**
 * ColoringView增强器
 * 提供优化的马赛克和数字显示功能，直接替换现有逻辑
 */
object ColoringViewEnhancer {
    
    private val optimizedMosaicRenderer = OptimizedMosaicRenderer()
    private val smartNumberManager = SmartNumberDisplayManager()
    
    /**
     * 增强的马赛克绘制方法
     * 替换ColoringView中的现有马赛克绘制逻辑
     */
    fun drawEnhancedMosaic(
        canvas: Canvas,
        regions: List<Region>,
        filledRegions: Set<Int>,
        scaleFactor: Float,
        currentColorHex: String?
    ) {
        // 只绘制当前选中颜色的未填色区域
        val targetRegions = if (currentColorHex != null) {
            val normalizedColor = normalizeColorHex(currentColorHex)
            regions.filter { region ->
                !filledRegions.contains(region.id) &&
                normalizeColorHex(region.colorHex) == normalizedColor
            }
        } else {
            emptyList()
        }
        
        // 限制绘制数量，避免性能问题
        val maxRegions = when {
            scaleFactor > 4.0f -> 15
            scaleFactor > 2.0f -> 10
            else -> 5
        }
        
        targetRegions.take(maxRegions).forEach { region ->
            optimizedMosaicRenderer.renderRegionMosaic(
                canvas = canvas,
                region = region,
                isFilled = filledRegions.contains(region.id),
                scaleFactor = scaleFactor
            )
        }
    }
    
    /**
     * 增强的数字显示方法
     */
    fun drawEnhancedNumbers(
        canvas: Canvas,
        regions: List<Region>,
        filledRegions: Set<Int>,
        scaleFactor: Float,
        currentColorHex: String?,
        visibleRect: RectF
    ) {
        val visibleNumbers = smartNumberManager.calculateVisibleNumbers(
            regions = regions,
            currentScale = scaleFactor,
            visibleRect = visibleRect,
            selectedColorHex = currentColorHex,
            filledRegions = filledRegions
        )
        
        drawNumbers(canvas, visibleNumbers)
    }
    
    /**
     * 绘制数字
     */
    private fun drawNumbers(canvas: Canvas, numbers: List<NumberDisplay>) {
        val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }
        
        val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.WHITE
            alpha = 220
        }
        
        numbers.forEach { numberDisplay ->
            val x = numberDisplay.centerX
            val y = numberDisplay.centerY
            
            textPaint.textSize = numberDisplay.textSize
            
            val numberText = numberDisplay.number.toString()
            val textBounds = Rect()
            textPaint.getTextBounds(numberText, 0, numberText.length, textBounds)
            
            // 绘制背景圆圈
            val radius = Math.max(textBounds.width(), textBounds.height()) / 2f + 3f
            canvas.drawCircle(x, y, radius, backgroundPaint)
            
            // 绘制数字
            canvas.drawText(numberText, x, y + textBounds.height() / 2f, textPaint)
        }
    }
    
    /**
     * 标准化颜色格式
     */
    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        if (normalized.length == 4) {
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }
    
    /**
     * 检查是否应该显示增强功能
     */
    fun shouldShowEnhancements(
        scaleFactor: Float,
        currentColorHex: String?,
        isInteracting: Boolean
    ): Boolean {
        // 在交互过程中暂停增强功能，提升性能
        if (isInteracting) return false
        
        // 必须有选中的颜色
        if (currentColorHex == null) return false
        
        // 缩放级别太小时不显示
        if (scaleFactor < 0.8f) return false
        
        return true
    }
}