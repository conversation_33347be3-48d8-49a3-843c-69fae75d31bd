package com.example.coloringproject.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import com.example.coloringproject.BuildConfig
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.MosaicBlock
import com.example.coloringproject.data.Region
import com.example.coloringproject.data.TouchResult
import com.example.coloringproject.data.ColoringAction
import com.example.coloringproject.data.ActionType
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt

/**
 * 进度验证结果
 */
data class ProgressValidationResult(
    val isValid: Boolean,
    val validRegions: Set<Int>,
    val invalidRegions: Set<Int>,
    val message: String
)

/**
 * 自定义填色View
 * 支持触摸填色、缩放、平移等功能
 */
class ColoringView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 数据
    private var coloringData: ColoringData? = null
    private var outlineBitmap: Bitmap? = null
    private var regionBitmap: Bitmap? = null

    // 绘制相关
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val regionPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val matrix = Matrix()
    private val tempMatrix = Matrix()
    private var filledRegionsBitmap: Bitmap? = null
    private var lastFilledRegionsHash = 0  // 用于检测填色变化，避免不必要的重绘

    // 缩放和平移
    private var scaleFactor = 1f
    private var translateX = 0f
    private var translateY = 0f
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 动态缩放范围
    private var dynamicMinScale = 0.1f  // 将根据屏幕适配动态计算
    private var dynamicMaxScale = 20.0f // 将根据用户习惯动态计算

    // 缩放手势检测
    private val scaleDetector = ScaleGestureDetector(context, ScaleListener())
    private val gestureDetector = GestureDetector(context, GestureListener())

    // 性能优化相关
    private var lastInvalidateTime = 0L
    private var isScaling = false
    private var matrixDirty = false
    private val highQualityPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isFilterBitmap = true  // 启用bitmap过滤，改善缩放质量
        isDither = true        // 启用抖动，减少色带
    }

    // 手势体验优化相关
    private var scaleVelocity = 0f  // 缩放速度，用于惯性效果
    private var lastScaleTime = 0L
    private var lastScaleFactor = 1f
    private var inertiaAnimator: android.animation.ValueAnimator? = null
    private var elasticAnimator: android.animation.ValueAnimator? = null
    private var isInElasticMode = false

    // 多指手势防跳动相关
    private var lastPointerCount = 0
    private var multiTouchEndTime = 0L
    private var isDragCooldownActive = false

    // 马赛克绘制性能优化相关
    private var isDragging = false
    private var dragEndTime = 0L
    private var visibleRect = RectF()
    private var visibleRectDirty = true

    // 马赛克缓存相关
    private var mosaicCacheBitmap: Bitmap? = null
    private var mosaicCacheCanvas: Canvas? = null
    private var mosaicCacheDirty = true

    // 异步初始化状态标志
    private var isAsyncInitializing = false
    private var lastHintRegionsHash = 0
    private var isInteracting = false  // 是否正在交互（拖动或缩放）
    private var lastInteractionTime = 0L

    // 填色状态
    private val filledRegions = mutableSetOf<Int>()
    private var currentColorHex: String? = null

    // 马赛克提醒相关
    private var showHints = true
    private val hintPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var hintAlpha = 0.6f
    private var hintRegions = listOf<Region>()

    // 马赛克区域点击检测缓存
    private var hintRegionPixelMap: Map<String, Region>? = null

    // 马赛克颜色缓存
    private val blueColor = Color.parseColor("#5B9BD5")
    private val whiteColor = Color.parseColor("#FFFFFF")
    private val mosaicPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val mosaicBorderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 1f
        color = Color.parseColor("#4A90C2")
    }

    // 马赛克边界裁剪开关 - 可以通过设置控制
    private var enableMosaicBoundaryClipping = true

    // 像素级马赛克开关 - 控制是否使用像素级渲染
    private var enablePixelLevelMosaic = true

    // 智能数字显示开关 - 控制是否启用智能数字显示逻辑
    private var enableSmartNumberDisplay = true

    // 触摸缓冲区配置 (适应手指触摸，优化性能)
    private var touchBufferRadius = 4  // 减少到4像素缓冲半径，平衡性能和体验
    private val minTouchBufferRadius = 2  // 最小2像素缓冲
    private val maxTouchBufferRadius = 8  // 最大8像素缓冲，避免性能问题

    // 回调
    var onRegionTouched: ((TouchResult) -> Unit)? = null
    var onProgressChanged: ((Int, Int) -> Unit)? = null // (filled, total)
    var onRegionFilled: ((Region) -> Unit)? = null // 区域填色完成回调
    var onColorCompleted: ((String, Int) -> Unit)? = null // 某种颜色全部完成回调
    var onProjectCompleted: (() -> Unit)? = null // 整个项目完成回调
    var onColorAutoSelected: ((String) -> Unit)? = null // 长按自动选择颜色回调

    // 撤销/重做功能
    private val actionHistory = mutableListOf<ColoringAction>()
    private var currentActionIndex = -1
    private val maxHistorySize = 50

    // 性能优化：预分配常用对象，避免频繁创建
    private val tempRectF = RectF()
    
    // 常量
    companion object {
        private const val TOUCH_TOLERANCE = 10f
        private const val DETAIL_ZOOM_SCALE = 8.0f  // 细节缩放级别

        // 缩放参数配置
        private const val MIN_SCALE_RATIO = 0.8f     // 最小缩放为适合屏幕的0.8倍
        private const val MAX_ZOOM_STEPS = 2         // 从原图最多两次缩放到最大
        private const val ZOOM_STEP_FACTOR = 2.5f    // 每次缩放的倍数，确保两次可达到理想最大值

        // 可滑动区域扩展配置
        private const val EXTRA_SCROLL_RATIO = 0.5f  // 额外滑动空间比例（相对于屏幕尺寸）
        private const val MIN_EXTRA_SCROLL = 200f    // 最小额外滑动空间（像素）
        private const val MAX_EXTRA_SCROLL = 800f    // 最大额外滑动空间（像素）

        // 性能优化常量
        private const val INVALIDATE_INTERVAL = 16L  // 60FPS，约16ms间隔
        private const val SCALE_SENSITIVITY = 0.95f  // 缩放敏感度调整，让缩放更平滑

        // 手势体验优化常量
        private const val INERTIA_DURATION = 300L    // 惯性动画持续时间（毫秒）
        private const val INERTIA_THRESHOLD = 0.01f  // 惯性触发的最小速度阈值
        private const val ELASTIC_DURATION = 200L    // 弹性动画持续时间（毫秒）
        private const val ELASTIC_OVERSHOOT = 0.1f   // 弹性超调比例
        private const val VELOCITY_DECAY = 0.95f     // 速度衰减因子

        // 多指手势防跳动常量
        private const val DRAG_COOLDOWN_DURATION = 150L  // 拖拽冷却时间（毫秒）
        private const val MIN_DRAG_DISTANCE = 10f        // 最小拖拽距离，避免微小抖动

        // 马赛克性能优化常量
        private const val DRAG_QUALITY_RESTORE_DELAY = 100L  // 拖动结束后恢复高质量的延迟（毫秒）
        private const val VISIBLE_MARGIN = 50f               // 可见区域边距，避免边缘马赛克被裁剪
        private const val CACHE_SCALE_FACTOR = 1.0f          // 缓存的缩放因子，1.0表示原始尺寸
        private const val INTERACTION_SETTLE_DELAY = 200L    // 交互结束后的稳定延迟（毫秒）
        
        // 异步初始化常量
        private const val ASYNC_INIT_TIMEOUT = 5000L         // 异步初始化超时时间（毫秒）
        private const val PIXEL_BATCH_SIZE = 1000            // 像素处理批次大小
    }
    


    init {
        regionPaint.style = Paint.Style.FILL

        // 初始化马赛克提醒画笔
        hintPaint.style = Paint.Style.FILL
        hintPaint.alpha = (255 * hintAlpha).toInt()

        // 初始化马赛克画笔
        mosaicPaint.style = Paint.Style.FILL

        // 确保View可以接收触摸事件
        isClickable = true
        isFocusable = true
        isFocusableInTouchMode = true

        // 根据设备特性调整触摸缓冲区
        adjustTouchBufferForDevice()
    }

    // 移除了复杂的纹理生成方法，现在使用简化的马赛克块绘制

    /**
     * 设置填色数据
     */
    fun setColoringData(data: ColoringData, outlineBitmap: Bitmap) {
        this.coloringData = data
        this.outlineBitmap = outlineBitmap

        // 创建区域位图
        createRegionBitmap()

        // 重置状态
        filledRegions.clear()

        // 立即设置变换（如果View已有尺寸）或等待View尺寸确定
        if (width > 0 && height > 0) {
            setupInitialTransform()
        } else {
            post {
                setupInitialTransform()
            }
        }

        invalidate()
    }
    
    // 数据设置完成回调
    var onDataSetupComplete: (() -> Unit)? = null

    /**
     * 异步设置填色数据 - 优化版本，避免视觉跳跃
     */
    fun setColoringDataAsync(data: ColoringData, outlineBitmap: Bitmap) {
        android.util.Log.d("ColoringView", "开始异步设置填色数据")
        android.util.Log.d("PERFORMANCE_E2E", "ColoringView.setColoringDataAsync 开始: ${System.currentTimeMillis()}")

        com.example.coloringproject.utils.PerformanceMonitor.startTiming("ColoringView_AsyncSetup")

        // 设置异步初始化标志，防止在初始化完成前进行绘制
        isAsyncInitializing = true

        this.coloringData = data
        this.outlineBitmap = outlineBitmap

        // 重置状态
        filledRegions.clear()

        // 立即设置正确的初始变换，避免视觉跳跃
        if (width > 0 && height > 0) {
            val immediateTransformTime = com.example.coloringproject.utils.PerformanceMonitor.measureTime("ImmediateTransform") {
                setupInitialTransformImmediate()
            }
            android.util.Log.d("PERFORMANCE_E2E", "立即变换设置完成: ${immediateTransformTime}ms")
        }
        
        // 记录数据基本信息
        android.util.Log.d("PERFORMANCE_E2E", "项目数据: ${data.regions.size}个区域, 位图尺寸: ${outlineBitmap.width}x${outlineBitmap.height}")
        
        // 异步创建区域位图，避免阻塞UI
        Thread {
            try {
                val regionBitmapTime = com.example.coloringproject.utils.PerformanceMonitor.measureTime("CreateRegionBitmap_Detailed") {
                    createRegionBitmapOptimized()
                }
                android.util.Log.d("PERFORMANCE_E2E", "区域位图创建完成: ${regionBitmapTime}ms")
                
                // 回到主线程更新UI
                post {
                    android.util.Log.d("PERFORMANCE_E2E", "回到主线程开始UI更新: ${System.currentTimeMillis()}")

                    // 如果View尺寸在异步过程中发生了变化，重新调整变换
                    if (width > 0 && height > 0) {
                        val adjustTransformTime = com.example.coloringproject.utils.PerformanceMonitor.measureTime("AdjustTransform_Detailed") {
                            adjustInitialTransformIfNeeded()
                        }
                        android.util.Log.d("PERFORMANCE_E2E", "变换调整完成: ${adjustTransformTime}ms")
                    }

                    // 清除异步初始化标志，表示所有资源已准备完毕
                    isAsyncInitializing = false

                    val invalidateTime = com.example.coloringproject.utils.PerformanceMonitor.measureTime("Invalidate_Detailed") {
                        invalidate()
                    }
                    android.util.Log.d("PERFORMANCE_E2E", "界面刷新完成: ${invalidateTime}ms")

                    val totalTime = com.example.coloringproject.utils.PerformanceMonitor.endTiming("ColoringView_AsyncSetup")
                    android.util.Log.i("PERFORMANCE_E2E", "ColoringView异步设置总耗时: ${totalTime}ms")
                    android.util.Log.d("PERFORMANCE_E2E", "ColoringView设置完成时间: ${System.currentTimeMillis()}")

                    android.util.Log.d("ColoringView", "异步填色数据设置完成")

                    // 通知数据设置完成
                    onDataSetupComplete?.invoke()
                }
            } catch (e: Exception) {
                android.util.Log.e("ColoringView", "异步设置填色数据失败", e)
                android.util.Log.e("PERFORMANCE_E2E", "ColoringView异步设置失败，使用降级方案")
                
                // 降级到同步方式
                post {
                    val fallbackTime = com.example.coloringproject.utils.PerformanceMonitor.measureTime("Fallback_Setup") {
                        createRegionBitmap()
                        setupInitialTransform()
                        // 清除异步初始化标志
                        isAsyncInitializing = false
                        invalidate()
                    }
                    android.util.Log.d("PERFORMANCE_E2E", "降级方案完成: ${fallbackTime}ms")

                    com.example.coloringproject.utils.PerformanceMonitor.endTiming("ColoringView_AsyncSetup")

                    // 通知数据设置完成
                    onDataSetupComplete?.invoke()
                }
            }
        }.start()
    }

    /**
     * 创建区域位图用于快速区域检测
     */
    private fun createRegionBitmap() {
        val data = coloringData ?: return
        val outline = outlineBitmap ?: return

        regionBitmap = Bitmap.createBitmap(
            outline.width,
            outline.height,
            Bitmap.Config.ARGB_8888
        )

        val canvas = Canvas(regionBitmap!!)
        val paint = Paint()
        paint.style = Paint.Style.FILL

        // 为每个区域绘制唯一颜色用于区域检测
        data.regions.forEach { region ->
            // 使用区域ID作为颜色值进行编码
            val regionColor = encodeRegionId(region.id)
            paint.color = regionColor

            // 绘制区域像素 - 使用小矩形确保覆盖完整
            region.pixels.forEach { pixel ->
                val x = pixel[0]
                val y = pixel[1]
                if (x >= 0 && x < outline.width && y >= 0 && y < outline.height) {
                    // 使用小矩形而不是点，确保触摸检测更可靠
                    val rect = RectF(
                        (x - 1).toFloat(),
                        (y - 1).toFloat(),
                        (x + 1).toFloat(),
                        (y + 1).toFloat()
                    )
                    canvas.drawRect(rect, paint)
                }
            }
        }
    }
    
    /**
     * 优化的区域位图创建 - 详细性能分析版本
     */
    private fun createRegionBitmapOptimized() {
        val data = coloringData ?: return
        val outline = outlineBitmap ?: return
        
        val startTime = System.currentTimeMillis()
        android.util.Log.d("PERFORMANCE_E2E", "开始区域位图创建: ${outline.width}x${outline.height}, 区域数: ${data.regions.size}")

        val width = outline.width
        val height = outline.height
        
        // 记录内存分配时间
        val allocStartTime = System.currentTimeMillis()
        val pixels = IntArray(width * height)
        pixels.fill(0)
        val allocTime = System.currentTimeMillis() - allocStartTime
        android.util.Log.d("PERFORMANCE_E2E", "像素数组分配耗时: ${allocTime}ms")

        // 记录像素处理时间
        val pixelStartTime = System.currentTimeMillis()
        var totalPixels = 0
        
        data.regions.forEach { region ->
            val regionColor = encodeRegionId(region.id)
            
            region.pixels.forEach { pixel ->
                val x = pixel[0]
                val y = pixel[1]
                
                if (x >= 0 && x < width && y >= 0 && y < height) {
                    pixels[y * width + x] = regionColor
                    totalPixels++
                }
            }
        }
        
        val pixelTime = System.currentTimeMillis() - pixelStartTime
        android.util.Log.d("PERFORMANCE_E2E", "像素处理耗时: ${pixelTime}ms, 处理像素数: $totalPixels")

        // 记录位图创建时间
        val bitmapStartTime = System.currentTimeMillis()
        regionBitmap = Bitmap.createBitmap(pixels, width, height, Bitmap.Config.ARGB_8888)
        val bitmapTime = System.currentTimeMillis() - bitmapStartTime
        android.util.Log.d("PERFORMANCE_E2E", "位图创建耗时: ${bitmapTime}ms")
        
        val totalTime = System.currentTimeMillis() - startTime
        android.util.Log.i("PERFORMANCE_E2E", "区域位图创建总耗时: ${totalTime}ms (分配:${allocTime}ms + 像素:${pixelTime}ms + 位图:${bitmapTime}ms)")
        
        // 分析性能瓶颈
        val maxTime = maxOf(allocTime, pixelTime, bitmapTime)
        val bottleneck = when (maxTime) {
            allocTime -> "内存分配"
            pixelTime -> "像素处理"
            bitmapTime -> "位图创建"
            else -> "未知"
        }
        android.util.Log.d("PERFORMANCE_E2E", "性能瓶颈: $bottleneck (${maxTime}ms)")
    }
    


    /**
     * 将区域ID编码为颜色值
     */
    private fun encodeRegionId(regionId: Int): Int {
        // 使用RGB通道编码区域ID，Alpha通道设为255
        val r = (regionId and 0xFF0000) shr 16
        val g = (regionId and 0x00FF00) shr 8
        val b = regionId and 0x0000FF
        return Color.argb(255, r, g, b)
    }

    /**
     * 从颜色值解码区域ID
     */
    private fun decodeRegionId(color: Int): Int {
        val r = Color.red(color)
        val g = Color.green(color)
        val b = Color.blue(color)
        return (r shl 16) or (g shl 8) or b
    }

    /**
     * 设置当前填色颜色
     */
    fun setCurrentColor(colorHex: String) {
        val oldColor = currentColorHex
        currentColorHex = colorHex

        // 只有在颜色真正改变时才清除缓存和更新
        if (oldColor != colorHex) {
            // 清除旧的缓存
            hintRegionPixelMap = null
            
            // 如果数据还没准备好，延迟重试
            if (coloringData == null) {
                android.util.Log.w("ColoringView", "ColoringData not ready, retrying color update in 50ms")
                postDelayed({
                    if (coloringData != null) {
                        updateHintRegions()
                        invalidate()
                        android.util.Log.d("ColoringView", "Delayed color update successful: $colorHex")
                    } else {
                        android.util.Log.w("ColoringView", "ColoringData still not ready after retry")
                    }
                }, 50)
            } else {
                updateHintRegions()
                invalidate()
                android.util.Log.d("ColoringView", "Current color changed: $oldColor -> $colorHex")
            }
        }
    }

    /**
     * 更新提醒区域 - 优化版本：延迟构建映射
     */
    private fun updateHintRegions() {
        android.util.Log.d("ColoringView", "=== updateHintRegions 开始 ===")
        
        val data = coloringData
        if (data == null) {
            android.util.Log.w("ColoringView", "updateHintRegions: coloringData is null")
            return
        }
        
        val currentColor = currentColorHex
        if (currentColor == null) {
            android.util.Log.w("ColoringView", "updateHintRegions: currentColorHex is null")
            return
        }

        // 标准化颜色格式进行比较
        val normalizedCurrentColor = normalizeColorHex(currentColor)

        // 找到所有匹配当前颜色且未填色的区域
        hintRegions = data.regions.filter { region ->
            val normalizedRegionColor = normalizeColorHex(region.colorHex)
            val colorMatches = normalizedRegionColor == normalizedCurrentColor
            val notFilled = !filledRegions.contains(region.id)
            colorMatches && notFilled
        }

        // 清除旧的像素映射缓存
        hintRegionPixelMap = null
        
        // 标记马赛克缓存需要更新
        mosaicCacheDirty = true

        // 延迟构建像素映射 - 只在真正需要时才构建
        // 这样可以避免频繁切换颜色时的性能问题
        
        android.util.Log.d(
            "ColoringView",
            "Updated hints: currentColor=$normalizedCurrentColor, hintRegions=${hintRegions.size} (映射延迟构建)"
        )
        android.util.Log.d("ColoringView", "=== updateHintRegions 完成 ===")
        
        // 立即刷新显示
        invalidate()
    }
    
    /**
     * 按需构建马赛克像素映射 - 只在真正需要时才构建
     */
    private fun ensureHintRegionPixelMap() {
        if (hintRegionPixelMap != null) return
        
        android.util.Log.d("ColoringView", "按需构建马赛克像素映射")
        
        // 如果区域数量较少，同步构建
        if (hintRegions.size <= 5) {
            buildHintRegionPixelMap()
        } else {
            // 区域较多时异步构建
            buildHintRegionPixelMapAsync()
        }
    }

    /**
     * 异步构建马赛克区域的像素映射缓存
     */
    private fun buildHintRegionPixelMapAsync() {
        // 清除旧缓存
        hintRegionPixelMap = null

        // 如果区域很少，直接同步构建
        if (hintRegions.size <= 3) {
            buildHintRegionPixelMap()
            return
        }

        // 异步构建大量区域的缓存
        Thread {
            try {
                val startTime = System.currentTimeMillis()
                buildHintRegionPixelMap()
                val endTime = System.currentTimeMillis()

                android.util.Log.d("ColoringView", "Async hint cache built in ${endTime - startTime}ms")

                // 构建完成后刷新UI
                post {
                    invalidate()
                }
            } catch (e: Exception) {
                android.util.Log.e("ColoringView", "Error building hint cache", e)
            }
        }.start()
    }

    /**
     * 构建马赛克区域的像素映射缓存
     * 增强版本：确保完整覆盖
     */
    private fun buildHintRegionPixelMap() {
        val pixelMap = mutableMapOf<String, Region>()
        var totalPixels = 0

        var bufferPixels = 0

        hintRegions.forEach { region ->
            // 1. 添加区域的所有原始像素
            region.pixels.forEach { pixel ->
                val key = "${pixel[0]},${pixel[1]}"
                pixelMap[key] = region
                totalPixels++
            }

            // 2. 优化的触摸缓冲区构建（避免ANR）
            if (region.pixels.size < 1000) {  // 只为小区域添加缓冲区
                bufferPixels += addTouchBufferForRegion(region, pixelMap, bufferPixels)
            } else {
                // 大区域只添加边界缓冲区
                bufferPixels += addBoundaryBufferForRegion(region, pixelMap, bufferPixels)
            }

            // 3. 额外添加边界框内的像素（原有逻辑保留）
            if (region.boundingBox != null && region.boundingBox.size >= 4) {
                val left = region.boundingBox[0]
                val top = region.boundingBox[1]
                val right = region.boundingBox[2]
                val bottom = region.boundingBox[3]

                // 为边界框边缘添加额外的检测点
                for (x in left..right) {
                    for (y in top..bottom) {
                        val key = "$x,$y"
                        if (!pixelMap.containsKey(key)) {
                            // 检查这个点是否真的在区域内
                            if (isPointInRegionDirect(x, y, region)) {
                                pixelMap[key] = region
                                totalPixels++
                            }
                        }
                    }
                }
            }
        }

        hintRegionPixelMap = pixelMap
        android.util.Log.d(
            "ColoringView",
            "Built finger-friendly hint pixel map: ${hintRegions.size} regions, $totalPixels original pixels, $bufferPixels buffer pixels (${touchBufferRadius}px radius)"
        )

        // 验证马赛克区域的完整性
        validateHintRegions()
    }

    /**
     * 验证马赛克区域的完整性
     */
    private fun validateHintRegions() {
        val pixelMap = hintRegionPixelMap ?: return

        hintRegions.forEach { region ->
            val regionPixelCount = region.pixels.size
            val mappedPixelCount = pixelMap.values.count { it.id == region.id }

            if (mappedPixelCount < regionPixelCount) {
                android.util.Log.w(
                    "ColoringView",
                    "Region ${region.id} missing pixels: expected $regionPixelCount, mapped $mappedPixelCount"
                )
            }

            // 检查边界框覆盖
            if (region.boundingBox != null && region.boundingBox.size >= 4) {
                val left = region.boundingBox[0]
                val top = region.boundingBox[1]
                val right = region.boundingBox[2]
                val bottom = region.boundingBox[3]
                val boundingBoxArea = (right - left + 1) * (bottom - top + 1)

                if (mappedPixelCount < regionPixelCount * 0.8) {
                    android.util.Log.w(
                        "ColoringView",
                        "Region ${region.id} may have poor coverage: $mappedPixelCount pixels in ${boundingBoxArea} bounding box"
                    )
                }
            }
        }
    }

    /**
     * 直接检查点是否在区域内（不使用缓存）
     */
    private fun isPointInRegionDirect(x: Int, y: Int, region: Region): Boolean {
        return region.pixels.any { pixel ->
            pixel[0] == x && pixel[1] == y
        }
    }

    /**
     * 标准化颜色十六进制格式
     */
    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        // 确保是6位十六进制格式
        if (normalized.length == 4) {
            // #RGB -> #RRGGBB
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }

    /**
     * 设置是否显示提醒
     */
    fun setShowHints(show: Boolean) {
        showHints = show
        invalidate()
    }

    /**
     * 设置提醒透明度
     */
    fun setHintAlpha(alpha: Float) {
        hintAlpha = alpha.coerceIn(0f, 1f)
        hintPaint.alpha = (255 * hintAlpha).toInt()
        invalidate()
    }

    /**
     * 获取填色进度
     */
    fun getProgress(): Pair<Int, Int> {
        val total = coloringData?.regions?.size ?: 0
        return Pair(filledRegions.size, total)
    }

    /**
     * 获取已填色的区域ID集合
     */
    fun getFilledRegions(): Set<Int> {
        return filledRegions.toSet()
    }

    /**
     * 设置已填色的区域（用于历史进度恢复）
     * 增强版本：包含数据验证和错误处理
     */
    fun setFilledRegions(regions: Set<Int>) {
        val data = coloringData
        if (data == null) {
            android.util.Log.w("ColoringView", "Cannot set filled regions: ColoringData not loaded")
            return
        }

        // 验证区域ID的有效性
        val validRegionIds = data.regions.map { it.id }.toSet()
        val validFilledRegions = regions.filter { regionId ->
            val isValid = validRegionIds.contains(regionId)
            if (!isValid) {
                android.util.Log.w("ColoringView", "Invalid region ID found in progress: $regionId")
            }
            isValid
        }.toSet()

        // 记录验证结果
        val invalidCount = regions.size - validFilledRegions.size
        if (invalidCount > 0) {
            android.util.Log.w("ColoringView", "Filtered out $invalidCount invalid region IDs during progress restore")
        }

        // 安全地设置填色区域
        filledRegions.clear()
        filledRegions.addAll(validFilledRegions)

        // 清除所有相关缓存，确保数据一致性
        clearAllCaches()

        // 更新提醒区域（如果当前有选中颜色）
        updateHintRegions()

        // 重绘界面
        invalidate()

        android.util.Log.d("ColoringView", "Progress restored: ${validFilledRegions.size} regions filled (${invalidCount} invalid regions filtered)")
    }

    /**
     * 安全的进度恢复方法
     * 包含完整的数据验证和错误处理
     */
    fun restoreProgressSafely(regions: Set<Int>): Boolean {
        return try {
            val data = coloringData
            if (data == null) {
                android.util.Log.e("ColoringView", "Cannot restore progress: ColoringData not loaded")
                return false
            }

            // 等待View完全初始化
            if (width <= 0 || height <= 0) {
                android.util.Log.w("ColoringView", "View not ready, deferring progress restore")
                post {
                    restoreProgressSafely(regions)
                }
                return true
            }

            // 验证数据完整性
            val validationResult = validateProgressData(regions, data)
            if (!validationResult.isValid) {
                android.util.Log.w("ColoringView", "Progress validation failed: ${validationResult.message}")
                // 尝试部分恢复
                if (validationResult.validRegions.isNotEmpty()) {
                    setFilledRegions(validationResult.validRegions)
                    return true
                }
                return false
            }

            // 执行安全恢复
            setFilledRegions(regions)

            android.util.Log.d("ColoringView", "Progress safely restored: ${regions.size} regions")
            return true

        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error during progress restore", e)
            return false
        }
    }

    /**
     * 验证进度数据的有效性
     */
    private fun validateProgressData(regions: Set<Int>, data: ColoringData): ProgressValidationResult {
        val validRegionIds = data.regions.map { it.id }.toSet()
        val validRegions = regions.filter { validRegionIds.contains(it) }.toSet()
        val invalidRegions = regions - validRegions

        // 详细的调试信息
        android.util.Log.d("ColoringView", "=== 区域ID验证详情 ===")
        android.util.Log.d("ColoringView", "保存的区域ID: $regions")
        android.util.Log.d("ColoringView", "项目中的前10个区域ID: ${validRegionIds.take(10)}")
        android.util.Log.d("ColoringView", "项目总区域数: ${data.regions.size}")
        android.util.Log.d("ColoringView", "有效区域: $validRegions")
        android.util.Log.d("ColoringView", "无效区域: $invalidRegions")
        android.util.Log.d("ColoringView", "========================")

        val isValid = invalidRegions.isEmpty()
        val message = if (isValid) {
            "All regions valid"
        } else {
            "Found ${invalidRegions.size} invalid region IDs: ${invalidRegions.take(5)}"
        }

        return ProgressValidationResult(isValid, validRegions, invalidRegions, message)
    }

    /**
     * 清除所有相关缓存
     */
    private fun clearAllCaches() {
        // 清除填色区域bitmap缓存
        filledRegionsBitmap = null
        lastFilledRegionsHash = 0

        // 清除马赛克提醒像素映射缓存
        hintRegionPixelMap = null

        android.util.Log.d("ColoringView", "All caches cleared for progress restore")
    }

    /**
     * 验证当前填色状态的一致性
     */
    fun validateCurrentState(): StateValidationResult {
        val data = coloringData
        if (data == null) {
            return StateValidationResult(false, "ColoringData not loaded")
        }

        val validRegionIds = data.regions.map { it.id }.toSet()
        val invalidFilledRegions = filledRegions.filter { !validRegionIds.contains(it) }

        val isValid = invalidFilledRegions.isEmpty()
        val message = if (isValid) {
            "All filled regions are valid (${filledRegions.size}/${data.regions.size})"
        } else {
            "Found ${invalidFilledRegions.size} invalid filled regions: ${invalidFilledRegions.take(3)}"
        }

        return StateValidationResult(isValid, message)
    }

    /**
     * 状态验证结果
     */
    data class StateValidationResult(
        val isValid: Boolean,
        val message: String
    )

    /**
     * 添加填色区域（用于自动演示）
     */
    fun addFilledRegion(regionId: Int) {
        filledRegions.add(regionId)
        // 清除缓存的填色bitmap，强制重新生成
        filledRegionsBitmap = null
        updateHintRegions()
        invalidate()
    }

    /**
     * 重置填色状态
     */
    fun resetColoring() {
        filledRegions.clear()
        updateHintRegions()
        invalidate()
    }

    /**
     * 重置视图到初始状态（居中并充满屏幕）
     */
    fun resetView() {
        setupInitialTransform()
    }

    /**
     * 定位到指定区域
     */
    fun focusOnRegion(region: Region) {
        if (region.pixels.isEmpty()) {
            android.util.Log.w("ColoringView", "Region ${region.id} has no pixels")
            return
        }

        if (width <= 0 || height <= 0) {
            android.util.Log.w("ColoringView", "View size not ready: ${width}x${height}")
            return
        }

        try {
            // 计算区域的中心点
            val centerX = region.pixels.map { it[0] }.average().toFloat()
            val centerY = region.pixels.map { it[1] }.average().toFloat()

            // 设置合适的缩放级别（放大到细节级别）
            scaleFactor = DETAIL_ZOOM_SCALE

            // 计算平移量，使区域中心显示在屏幕中心
            translateX = width / 2f - centerX * scaleFactor
            translateY = height / 2f - centerY * scaleFactor

            android.util.Log.d(
                "ColoringView",
                "Before constrain: translate=($translateX, $translateY), scale=$scaleFactor"
            )

            // 限制平移范围
            constrainTranslation()

            // 更新变换矩阵
            markMatrixDirty()
            updateMatrix()

            // 重绘界面
            invalidate()

            android.util.Log.d(
                "ColoringView",
                "Focused on region ${region.id} at center ($centerX, $centerY)"
            )

        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error focusing on region ${region.id}", e)
            // 发生错误时，回到初始状态
            setupInitialTransform()
        }
    }

    /**
     * 设置初始变换 - 居中并充满屏幕
     */
    private fun setupInitialTransform() {
        val bitmap = outlineBitmap ?: return

        if (width <= 0 || height <= 0) return

        // 首先计算动态缩放范围
        calculateDynamicScaleRange()

        // 计算缩放比例，使图片充满屏幕
        val scaleX = width.toFloat() / bitmap.width
        val scaleY = height.toFloat() / bitmap.height

        // 使用较小的缩放比例，确保图片完全显示在屏幕内
        scaleFactor = minOf(scaleX, scaleY)

        // 计算居中位置
        val scaledWidth = bitmap.width * scaleFactor
        val scaledHeight = bitmap.height * scaleFactor

        translateX = (width - scaledWidth) / 2f
        translateY = (height - scaledHeight) / 2f

        markMatrixDirty()
        updateMatrix()
        invalidate()

        android.util.Log.d(
            "ColoringView",
            "Initial transform: scale=$scaleFactor, translate=($translateX, $translateY)"
        )
        android.util.Log.d(
            "ColoringView",
            "Bitmap size: ${bitmap.width}x${bitmap.height}, View size: ${width}x${height}"
        )
    }
    
    /**
     * 快速初始变换设置 - 简化计算，提升性能
     */
    private fun setupInitialTransformFast() {
        val bitmap = outlineBitmap ?: return

        if (width <= 0 || height <= 0) return
        
        android.util.Log.d("ColoringView", "开始快速初始变换设置")

        // 简化的缩放计算，避免复杂的动态范围计算
        val scaleX = width.toFloat() / bitmap.width
        val scaleY = height.toFloat() / bitmap.height
        scaleFactor = minOf(scaleX, scaleY)

        // 简化的居中计算
        val scaledWidth = bitmap.width * scaleFactor
        val scaledHeight = bitmap.height * scaleFactor
        translateX = (width - scaledWidth) / 2f
        translateY = (height - scaledHeight) / 2f

        // 设置基础的缩放范围（避免复杂计算）
        dynamicMinScale = scaleFactor * 0.5f
        dynamicMaxScale = scaleFactor * 8.0f

        markMatrixDirty()
        updateMatrix()

        android.util.Log.d("ColoringView", "快速初始变换完成: scale=$scaleFactor")
    }

    /**
     * 立即设置初始变换 - 避免视觉跳跃
     */
    private fun setupInitialTransformImmediate() {
        val bitmap = outlineBitmap ?: return

        if (width <= 0 || height <= 0) return

        android.util.Log.d("ColoringView", "立即设置初始变换，避免视觉跳跃")

        // 立即计算正确的缩放和位置
        val scaleX = width.toFloat() / bitmap.width
        val scaleY = height.toFloat() / bitmap.height
        scaleFactor = minOf(scaleX, scaleY)

        // 立即计算居中位置
        val scaledWidth = bitmap.width * scaleFactor
        val scaledHeight = bitmap.height * scaleFactor
        translateX = (width - scaledWidth) / 2f
        translateY = (height - scaledHeight) / 2f

        // 设置基础的缩放范围
        dynamicMinScale = scaleFactor * 0.5f
        dynamicMaxScale = scaleFactor * 8.0f

        markMatrixDirty()
        updateMatrix()

        // 移除立即刷新，等待所有资源准备完毕后统一刷新
        // invalidate() // 注释掉，避免在区域位图创建完成前触发重绘

        android.util.Log.d("ColoringView", "立即变换设置完成: scale=$scaleFactor, translate=($translateX, $translateY)")
    }

    /**
     * 如果需要，调整初始变换 - 处理View尺寸变化的情况
     */
    private fun adjustInitialTransformIfNeeded() {
        val bitmap = outlineBitmap ?: return

        if (width <= 0 || height <= 0) return

        // 重新计算正确的缩放比例
        val scaleX = width.toFloat() / bitmap.width
        val scaleY = height.toFloat() / bitmap.height
        val correctScale = minOf(scaleX, scaleY)

        // 如果当前缩放与正确缩放差异较大，进行调整
        if (kotlin.math.abs(scaleFactor - correctScale) > 0.01f) {
            android.util.Log.d("ColoringView", "检测到缩放差异，进行调整: $scaleFactor -> $correctScale")
            
            scaleFactor = correctScale

            // 重新计算居中位置
            val scaledWidth = bitmap.width * scaleFactor
            val scaledHeight = bitmap.height * scaleFactor
            translateX = (width - scaledWidth) / 2f
            translateY = (height - scaledHeight) / 2f

            // 更新缩放范围
            dynamicMinScale = scaleFactor * 0.5f
            dynamicMaxScale = scaleFactor * 8.0f

            markMatrixDirty()
            updateMatrix()
            
            android.util.Log.d("ColoringView", "变换调整完成: scale=$scaleFactor, translate=($translateX, $translateY)")
        } else {
            android.util.Log.d("ColoringView", "变换无需调整，当前缩放正确")
        }
    }

    /**
     * 重置变换到初始状态
     */
    private fun resetTransform() {
        setupInitialTransform()
    }

    /**
     * 更新变换矩阵 - 优化版本，避免不必要的重新计算
     */
    private fun updateMatrix() {
        if (!matrixDirty) return

        matrix.reset()
        matrix.postScale(scaleFactor, scaleFactor)
        matrix.postTranslate(translateX, translateY)
        matrixDirty = false
    }

    /**
     * 标记矩阵需要更新
     */
    private fun markMatrixDirty() {
        matrixDirty = true
        visibleRectDirty = true  // 矩阵变化时，可见区域也需要重新计算
    }

    /**
     * 优化的重绘方法 - 在缩放过程中进行节流
     */
    private fun throttledInvalidate() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastInvalidateTime >= INVALIDATE_INTERVAL) {
            updateMatrix()
            invalidate()
            lastInvalidateTime = currentTime
        }
    }

    /**
     * 计算动态缩放敏感度 - 根据当前缩放级别和用户习惯调整
     */
    private fun calculateDynamicSensitivity(): Float {
        val fitScreenScale = calculateFitScreenScale()
        val relativeScale = scaleFactor / fitScreenScale

        return when {
            // 在最小缩放附近，更敏感，便于快速回到正常尺寸
            relativeScale < 0.9f -> 0.98f
            // 在原图尺寸附近，使用优化的敏感度，便于精确控制
            relativeScale < 1.2f -> 0.96f
            // 在放大状态，稍微降低敏感度，便于精细调整
            relativeScale > 3f -> 0.92f
            // 正常范围使用默认值
            else -> SCALE_SENSITIVITY
        }
    }

    /**
     * 计算缩放速度
     */
    private fun calculateScaleVelocity(currentScale: Float, currentTime: Long): Float {
        if (lastScaleTime == 0L) {
            lastScaleTime = currentTime
            lastScaleFactor = currentScale
            return 0f
        }

        val deltaTime = currentTime - lastScaleTime
        val deltaScale = currentScale - lastScaleFactor

        lastScaleTime = currentTime
        lastScaleFactor = currentScale

        return if (deltaTime > 0) deltaScale / deltaTime else 0f
    }

    /**
     * 计算当前可见区域 - 用于视口裁剪优化
     */
    private fun calculateVisibleRect(): RectF {
        if (!visibleRectDirty) return visibleRect

        val invertMatrix = Matrix()
        if (matrix.invert(invertMatrix)) {
            // 将屏幕坐标转换为图像坐标
            val corners = floatArrayOf(
                -VISIBLE_MARGIN, -VISIBLE_MARGIN,           // 左上角（带边距）
                width + VISIBLE_MARGIN, height + VISIBLE_MARGIN  // 右下角（带边距）
            )
            invertMatrix.mapPoints(corners)

            visibleRect.set(
                corners[0].coerceAtLeast(0f),
                corners[1].coerceAtLeast(0f),
                corners[2],
                corners[3]
            )
        } else {
            // 如果矩阵不可逆，返回整个图像区域
            val bitmap = outlineBitmap
            if (bitmap != null) {
                visibleRect.set(0f, 0f, bitmap.width.toFloat(), bitmap.height.toFloat())
            }
        }

        visibleRectDirty = false
        return visibleRect
    }

    /**
     * 检查区域是否在可见范围内 - 优化版本
     */
    private fun isRegionInVisibleArea(region: Region, visibleArea: RectF): Boolean {
        // 首先检查边界框
        val boundingBox = region.boundingBox
        if (boundingBox != null && boundingBox.size >= 4) {
            val left = boundingBox[0].toFloat()
            val top = boundingBox[1].toFloat()
            val right = boundingBox[2].toFloat()
            val bottom = boundingBox[3].toFloat()

            // 检查边界框是否与可见区域相交
            return !(right < visibleArea.left || left > visibleArea.right ||
                    bottom < visibleArea.top || top > visibleArea.bottom)
        }

        // 如果没有边界框，检查第一个像素点（快速估算）
        if (region.pixels.isNotEmpty()) {
            val firstPixel = region.pixels[0]
            val x = firstPixel[0].toFloat()
            val y = firstPixel[1].toFloat()
            return visibleArea.contains(x, y)
        }

        return false
    }

    /**
     * 启动惯性缩放动画
     */
    private fun startInertiaAnimation(focusX: Float, focusY: Float) {
        // 取消之前的动画
        inertiaAnimator?.cancel()
        elasticAnimator?.cancel()

        if (kotlin.math.abs(scaleVelocity) < INERTIA_THRESHOLD) return

        val startScale = scaleFactor
        val velocityDecay = VELOCITY_DECAY
        val targetScale = (startScale + scaleVelocity * INERTIA_DURATION / 1000f * velocityDecay)
            .coerceIn(dynamicMinScale, dynamicMaxScale)

        if (kotlin.math.abs(targetScale - startScale) < 0.01f) return

        inertiaAnimator = android.animation.ValueAnimator.ofFloat(0f, 1f).apply {
            duration = INERTIA_DURATION
            interpolator = android.view.animation.DecelerateInterpolator(2f)

            addUpdateListener { animation ->
                isInteracting = true  // 动画过程中标记为交互状态

                val progress = animation.animatedValue as Float
                val currentScale = startScale + (targetScale - startScale) * progress

                val scaleChange = currentScale / scaleFactor
                scaleFactor = currentScale

                // 调整平移量，保持焦点位置
                translateX = focusX + (translateX - focusX) * scaleChange
                translateY = focusY + (translateY - focusY) * scaleChange

                markMatrixDirty()
                updateMatrix()
                invalidate()
            }

            addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    isInteracting = false
                    lastInteractionTime = System.currentTimeMillis()

                    constrainTranslation()
                    markMatrixDirty()
                    updateMatrix()
                    invalidate()
                }
            })

            start()
        }
    }

    /**
     * 启动弹性边界动画
     */
    private fun startElasticAnimation(targetScale: Float, focusX: Float, focusY: Float) {
        // 取消之前的动画
        inertiaAnimator?.cancel()
        elasticAnimator?.cancel()

        val startScale = scaleFactor
        isInElasticMode = true

        elasticAnimator = android.animation.ValueAnimator.ofFloat(0f, 1f).apply {
            duration = ELASTIC_DURATION
            interpolator = android.view.animation.OvershootInterpolator(1.5f)

            addUpdateListener { animation ->
                isInteracting = true  // 动画过程中标记为交互状态

                val progress = animation.animatedValue as Float
                val currentScale = startScale + (targetScale - startScale) * progress

                val scaleChange = currentScale / scaleFactor
                scaleFactor = currentScale

                // 调整平移量，保持焦点位置
                translateX = focusX + (translateX - focusX) * scaleChange
                translateY = focusY + (translateY - focusY) * scaleChange

                markMatrixDirty()
                updateMatrix()
                invalidate()
            }

            addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    isInElasticMode = false
                    isInteracting = false
                    lastInteractionTime = System.currentTimeMillis()

                    constrainTranslation()
                    markMatrixDirty()
                    updateMatrix()
                    invalidate()
                }
            })

            start()
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val outline = outlineBitmap ?: return

        // 如果正在异步初始化且变换还未正确设置，确保使用正确的初始变换
        if (isAsyncInitializing && width > 0 && height > 0) {
            // 确保变换已正确设置，避免显示默认的小画面
            if (scaleFactor <= 1.1f) { // 如果缩放因子接近默认值，重新设置
                val scaleX = width.toFloat() / outline.width
                val scaleY = height.toFloat() / outline.height
                val correctScale = minOf(scaleX, scaleY)

                if (kotlin.math.abs(scaleFactor - correctScale) > 0.01f) {
                    scaleFactor = correctScale
                    val scaledWidth = outline.width * scaleFactor
                    val scaledHeight = outline.height * scaleFactor
                    translateX = (width - scaledWidth) / 2f
                    translateY = (height - scaledHeight) / 2f
                    markMatrixDirty()
                }
            }
        }

        // 确保矩阵是最新的
        updateMatrix()

        // 保存画布状态
        canvas.save()

        // 应用变换
        canvas.setMatrix(matrix)

        // 1. 绘制线稿（底层）
        canvas.drawBitmap(outline, 0f, 0f, highQualityPaint)

        // 2. 绘制已填色的区域（中层，自动遮挡不需要的线条）
        // 在异步初始化期间，可能regionBitmap还未创建完成，但已填色区域应该正常显示
        if (!isAsyncInitializing || regionBitmap != null) {
            drawFilledRegions(canvas)
        }

        // 3. 绘制马赛克提醒区域（顶层，确保可见）
        // 在异步初始化期间，暂时不显示提示区域，避免性能问题
        if (showHints && !isAsyncInitializing) {
            drawHintRegions(canvas)
        }

        // 恢复画布状态
        canvas.restore()

        // 4. 在屏幕坐标系下绘制优化的数字标注（避免双重变换问题）
        // 在异步初始化期间，暂时不显示数字标注
        if (!isAsyncInitializing) {
            drawOptimizedNumbersInScreenCoords(canvas)
        }
    }

    /**
     * 绘制已填色的区域
     */
    private fun drawFilledRegions(canvas: Canvas) {
        val data = coloringData ?: return
        val outline = outlineBitmap ?: return

        // 重新生成填色区域bitmap
        updateFilledRegionsBitmap(outline.width, outline.height, data)

        filledRegionsBitmap?.let { bitmap ->
            // 使用高质量的缩放绘制bitmap
            val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                isFilterBitmap = true  // 启用bitmap过滤
                isDither = true        // 启用抖动
            }

            canvas.drawBitmap(bitmap, 0f, 0f, paint)
        }
    }
    
    /**
     * 绘制优化的数字标注（屏幕坐标系）
     * 使用新的数字显示管理器，实现大小与区域相关、按颜色顺序显示
     */
    private fun drawOptimizedNumbersInScreenCoords(canvas: Canvas) {
        com.example.coloringproject.view.NumberDisplayHelper.drawOptimizedNumbersInScreenCoords(
            canvas = canvas,
            regions = hintRegions,
            filledRegions = filledRegions,
            scaleFactor = scaleFactor,
            currentColorHex = currentColorHex,
            matrix = matrix,
            viewWidth = width,
            viewHeight = height
        )
    }
    
    /**
     * 绘制优化的数字标注（图像坐标系）
     * 在马赛克绘制时使用，避免双重变换
     */
    private fun drawOptimizedNumbers(canvas: Canvas) {
        // 计算可见区域
        val visibleRect = calculateVisibleRect()
        
        com.example.coloringproject.view.NumberDisplayHelper.drawOptimizedNumbers(
            canvas = canvas,
            regions = hintRegions,
            filledRegions = filledRegions,
            scaleFactor = scaleFactor,
            currentColorHex = currentColorHex,
            visibleRect = visibleRect
        )
    }

    /**
     * 更新填色区域bitmap（智能重绘优化）
     */
    private fun updateFilledRegionsBitmap(width: Int, height: Int, data: ColoringData) {
        // 计算当前填色状态的哈希值
        val currentHash = filledRegions.hashCode()

        // 检查是否需要重新生成bitmap
        val needsUpdate = filledRegionsBitmap == null ||
                filledRegionsBitmap?.width != width ||
                filledRegionsBitmap?.height != height ||
                lastFilledRegionsHash != currentHash

        if (!needsUpdate) {
            // 填色状态没有变化，无需重绘
            return
        }

        // 更新哈希值
        lastFilledRegionsHash = currentHash

        // 创建或重用bitmap
        if (filledRegionsBitmap?.width != width || filledRegionsBitmap?.height != height) {
            filledRegionsBitmap?.recycle()
            filledRegionsBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        }

        val bitmap = filledRegionsBitmap ?: return

        // 清除之前的内容
        bitmap.eraseColor(Color.TRANSPARENT)

        filledRegions.forEach { regionId ->
            val region = data.regions.find { it.id == regionId }
            region?.let {
                // 确保颜色完全不透明，能够遮挡线稿
                val originalColor = Color.parseColor(it.colorHex)
                val opaqueColor = Color.argb(
                    255,
                    Color.red(originalColor),
                    Color.green(originalColor),
                    Color.blue(originalColor)
                )

                // 直接在bitmap上设置像素，确保精确度和完全覆盖
                it.pixels.forEach { pixel ->
                    val x = pixel[0]
                    val y = pixel[1]
                    if (x in 0 until width && y in 0 until height) {
                        bitmap.setPixel(x, y, opaqueColor)
                    }
                }
            }
        }
    }

    /**
     * 绘制马赛克提醒区域 - 智能缓存版本，交互时不卡顿
     */
    private fun drawHintRegions(canvas: Canvas) {
        if (hintRegions.isEmpty()) return

        // 检查是否正在交互（拖动或缩放）
        val currentTime = System.currentTimeMillis()
        val isCurrentlyInteracting = isInteracting || isDragging || isScaling ||
                (currentTime - lastInteractionTime < INTERACTION_SETTLE_DELAY)

        // 只在非交互状态下更新缓存，避免交互时卡顿
        if (!isCurrentlyInteracting && (mosaicCacheDirty || isMosaicContentChanged())) {
            regenerateMosaicCache()
        }

        // 使用缓存绘制马赛克（交互和静止时都使用缓存，保证流畅）
        drawCachedMosaic(canvas)

        // 数字编号始终绘制 - 在拖动和缩放时也保持可见（使用优化版本）
        drawOptimizedNumbers(canvas)
    }

    /**
     * 绘制缓存的马赛克 - 高性能且用户无感知
     */
    private fun drawCachedMosaic(canvas: Canvas) {
        val cacheBitmap = mosaicCacheBitmap ?: return

        // 直接绘制缓存的马赛克Bitmap，性能极高
        canvas.drawBitmap(cacheBitmap, 0f, 0f, highQualityPaint)

        // 可选：添加视口裁剪进一步优化（在极大图片时）
        // 但由于使用缓存，通常不需要额外裁剪
    }



    /**
     * 检查区域是否在当前可见范围内
     */
    private fun isRegionVisible(region: Region): Boolean {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                // 转换到屏幕坐标
                val points = floatArrayOf(
                    bbox[0].toFloat(), bbox[1].toFloat(),  // 左上角
                    bbox[2].toFloat(), bbox[3].toFloat()   // 右下角
                )
                matrix.mapPoints(points)

                // 检查是否与屏幕相交
                return points[0] < width && points[2] > 0 &&
                       points[1] < height && points[3] > 0
            }
        }
        return true // 如果没有boundingBox，默认绘制
    }

    /**
     * 在屏幕坐标系下绘制数字标注 - 解决双重变换位移问题
     * 保持交互时数字的稳定显示
     */
    private fun drawImportantRegionNumbersInScreenCoords(canvas: Canvas) {
        // 快速预检查
        if (scaleFactor < 1.5f || hintRegions.isEmpty()) return

        // 检查是否正在交互
        val currentTime = System.currentTimeMillis()
        val isCurrentlyInteracting = isInteracting || isDragging || isScaling ||
                (currentTime - lastInteractionTime < INTERACTION_SETTLE_DELAY)

        // 获取可见区域用于裁剪优化
        val visibleArea = calculateVisibleRect()
        var drawnCount = 0
        val maxDrawCount = if (isCurrentlyInteracting) 15 else Int.MAX_VALUE  // 交互时限制数量

        // 绘制当前颜色所有区域的编号，优先绘制可见区域内的
        for (region in hintRegions) {
            if (isCurrentlyInteracting && drawnCount >= maxDrawCount) break

            // 视口裁剪：只绘制可见区域内的数字
            if (isRegionInVisibleArea(region, visibleArea)) {
                if (isCurrentlyInteracting) {
                    // 交互时使用简化的数字绘制，提高性能
                    drawRegionNumberInScreenCoordsSimplified(canvas, region)
                } else {
                    // 静止时使用完整的数字绘制
                    drawRegionNumberInScreenCoords(canvas, region)
                }
                drawnCount++
            }
        }

        if (isCurrentlyInteracting) {
            android.util.Log.d("ColoringView", "Interactive mode: drawn $drawnCount region numbers")
        }
    }

    /**
     * 绘制当前颜色所有区域的编号 - 优化版本，支持交互时持久显示
     * @deprecated 使用drawImportantRegionNumbersInScreenCoords替代，避免位移问题
     */
    private fun drawImportantRegionNumbers(canvas: Canvas) {
        // 快速预检查
        if (scaleFactor < 1.5f || hintRegions.isEmpty()) return

        // 检查是否正在交互
        val currentTime = System.currentTimeMillis()
        val isCurrentlyInteracting = isInteracting || isDragging || isScaling ||
                (currentTime - lastInteractionTime < INTERACTION_SETTLE_DELAY)

        // 获取可见区域用于裁剪优化
        val visibleArea = calculateVisibleRect()
        var drawnCount = 0
        val maxDrawCount = if (isCurrentlyInteracting) 15 else Int.MAX_VALUE  // 交互时限制数量

        // 绘制当前颜色所有区域的编号，优先绘制可见区域内的
        for (region in hintRegions) {
            if (isCurrentlyInteracting && drawnCount >= maxDrawCount) break

            // 视口裁剪：只绘制可见区域内的数字
            if (isRegionInVisibleArea(region, visibleArea)) {
                if (isCurrentlyInteracting) {
                    // 交互时使用简化的数字绘制，提高性能
                    drawRegionNumberSimplified(canvas, region)
                } else {
                    // 静止时使用完整的数字绘制
                    drawRegionNumber(canvas, region)
                }
                drawnCount++
            }
        }

        if (isCurrentlyInteracting) {
            android.util.Log.d("ColoringView", "Interactive mode: drawn $drawnCount region numbers")
        }
    }

    /**
     * 计算马赛克大小 - 根据缩放级别智能调整
     */
    private fun calculateMosaicSize(): Int {
        // 基础马赛克大小
        val baseMosaicSize = 12f

        // 根据缩放级别调整，缩放越大马赛克越大
        val scaledSize = baseMosaicSize / scaleFactor

        // 限制马赛克大小范围，确保视觉效果
        return scaledSize.toInt().coerceIn(4, 24)
    }

    /**
     * 绘制单个区域的马赛克效果 - 使用纹理贴图
     */
    private fun drawMosaicRegion(canvas: Canvas, region: Region, mosaicSize: Int) {
        // 使用高性能的纹理绘制替代复杂的像素级绘制
        drawTexturedMosaic(canvas, region)
    }

    /**
     * 极简马赛克绘制 - 最高性能方案
     */
    private fun drawTexturedMosaic(canvas: Canvas, region: Region) {
        // 使用最简单的纯色马赛克，确保流畅性能
        drawSimpleMosaic(canvas, region)
    }

    /**
     * 智能马赛克块绘制 - 像素级完美贴合版本
     */
    private fun drawSimpleMosaic(canvas: Canvas, region: Region) {
        if (region.pixels.isEmpty()) return

        if (enablePixelLevelMosaic) {
            // 使用像素级马赛克渲染 - 完美贴合区域边界
            android.util.Log.d("ColoringView", "Using pixel-level mosaic for region ${region.id}")
            drawPixelLevelMosaic(canvas, region)
        } else {
            // 回退到块级渲染 - 高性能但边界不够精确
            android.util.Log.d("ColoringView", "Using block-based mosaic for region ${region.id}")
            drawBlockBasedMosaic(canvas, region)
        }
    }

    /**
     * 块级马赛克绘制 - 保留作为备用方案
     */
    private fun drawBlockBasedMosaic(canvas: Canvas, region: Region) {
        // 设置马赛克颜色
        mosaicPaint.color = blueColor
        mosaicPaint.alpha = 128 // 半透明效果

        // 检查是否有预计算的马赛克数据
        if (region.mosaicBlocks != null && region.mosaicBlocks.isNotEmpty()) {
            // 使用预计算的马赛克数据 - 高性能路径
            android.util.Log.d("ColoringView", "Using precomputed mosaic data for region ${region.id}")
            drawPrecomputedMosaic(canvas, region)
        } else {
            // 回退到运行时计算 - 兼容性路径
            android.util.Log.d("ColoringView", "Using fallback mosaic for region ${region.id}, clipping=${enableMosaicBoundaryClipping}")
            drawFallbackMosaic(canvas, region)
        }
    }

    /**
     * 绘制预计算的马赛克数据 - 最高性能，支持区域聚合
     */
    private fun drawPrecomputedMosaic(canvas: Canvas, region: Region) {
        val mosaicBlocks = region.mosaicBlocks ?: return

        // 检查是否需要聚合渲染（针对分散的小区域）
        if (shouldUseAggregatedRendering(region, mosaicBlocks)) {
            drawAggregatedMosaic(canvas, region, mosaicBlocks)
        } else {
            drawStandardMosaic(canvas, mosaicBlocks)
        }
    }

    /**
     * 标准马赛克绘制 - 支持边界裁剪
     */
    private fun drawStandardMosaic(canvas: Canvas, mosaicBlocks: List<MosaicBlock>) {
        for (block in mosaicBlocks) {
            // 应用透明度因子
            val originalAlpha = mosaicPaint.alpha
            mosaicPaint.alpha = (originalAlpha * block.alphaFactor).toInt()

            if (enableMosaicBoundaryClipping) {
                // 绘制边界裁剪的格子马赛克块
                drawBoundaryClippedCheckerboardBlock(canvas, block)
            } else {
                // 绘制标准格子马赛克块
                drawCheckerboardMosaicBlock(canvas, block)
            }

            // 恢复透明度
            mosaicPaint.alpha = originalAlpha
        }
    }

    /**
     * 绘制标准格子马赛克块
     */
    private fun drawCheckerboardMosaicBlock(canvas: Canvas, block: MosaicBlock) {
        drawCheckerboardPattern(
            canvas,
            block.x.toFloat(),
            block.y.toFloat(),
            block.size.toFloat(),
            block.size.toFloat(),
            block.alphaFactor
        )
    }

    /**
     * 绘制边界裁剪的格子马赛克块 - 更平滑的边缘
     */
    private fun drawBoundaryClippedCheckerboardBlock(canvas: Canvas, block: MosaicBlock) {
        // 获取当前选中颜色对应的区域
        val currentRegions = hintRegions
        if (currentRegions.isEmpty()) {
            // 回退到标准格子绘制
            drawCheckerboardPattern(
                canvas,
                block.x.toFloat(),
                block.y.toFloat(),
                block.size.toFloat(),
                block.size.toFloat(),
                block.alphaFactor
            )
            return
        }

        // 使用简化的边界检查 - 性能优化
        val clippedRect = calculateSimplifiedClipping(block, currentRegions)

        if (clippedRect != null) {
            // 绘制裁剪后的格子马赛克块
            drawCheckerboardPattern(
                canvas,
                clippedRect.left,
                clippedRect.top,
                clippedRect.width(),
                clippedRect.height(),
                block.alphaFactor
            )
        } else {
            // 如果完全在区域外，绘制一个缩小的格子中心块
            val shrinkFactor = 0.7f
            val shrinkOffset = block.size * (1 - shrinkFactor) / 2
            drawCheckerboardPattern(
                canvas,
                block.x + shrinkOffset,
                block.y + shrinkOffset,
                block.size - shrinkOffset * 2,
                block.size - shrinkOffset * 2,
                block.alphaFactor * 0.8f
            )
        }
    }

    /**
     * 简化的边界裁剪计算 - 性能优化版本
     */
    private fun calculateSimplifiedClipping(block: MosaicBlock, regions: List<Region>): RectF? {
        val blockRect = RectF(
            block.x.toFloat(),
            block.y.toFloat(),
            (block.x + block.size).toFloat(),
            (block.y + block.size).toFloat()
        )

        // 只检查马赛克块的5个关键点：四个角 + 中心
        val checkPoints = arrayOf(
            Pair(block.x, block.y),                                    // 左上角
            Pair(block.x + block.size - 1, block.y),                   // 右上角
            Pair(block.x, block.y + block.size - 1),                   // 左下角
            Pair(block.x + block.size - 1, block.y + block.size - 1),  // 右下角
            Pair(block.x + block.size / 2, block.y + block.size / 2)   // 中心点
        )

        var validPoints = 0
        for ((x, y) in checkPoints) {
            if (isPointInAnyRegion(x, y, regions)) {
                validPoints++
            }
        }

        return when {
            validPoints >= 4 -> blockRect  // 几乎完全在区域内，绘制完整块
            validPoints >= 2 -> {
                // 部分在区域内，绘制缩小的块
                val shrinkFactor = 0.8f
                val shrinkOffset = block.size * (1 - shrinkFactor) / 2
                RectF(
                    block.x + shrinkOffset,
                    block.y + shrinkOffset,
                    block.x + block.size - shrinkOffset,
                    block.y + block.size - shrinkOffset
                )
            }
            validPoints >= 1 -> {
                // 少量在区域内，绘制很小的中心块
                val shrinkFactor = 0.6f
                val shrinkOffset = block.size * (1 - shrinkFactor) / 2
                RectF(
                    block.x + shrinkOffset,
                    block.y + shrinkOffset,
                    block.x + block.size - shrinkOffset,
                    block.y + block.size - shrinkOffset
                )
            }
            else -> null  // 完全在区域外，不绘制
        }
    }

    /**
     * 检查点是否在任何区域内 - 优化版本
     */
    private fun isPointInAnyRegion(x: Int, y: Int, regions: List<Region>): Boolean {
        // 使用现有的像素映射缓存进行快速查找
        val key = "$x,$y"
        val mappedRegion = hintRegionPixelMap?.get(key)
        return mappedRegion != null && regions.contains(mappedRegion)
    }

    /**
     * 计算马赛克块的裁剪区域
     */
    private fun calculateMosaicClipping(blockRect: RectF, regions: List<Region>): RectF? {
        var clippedRect: RectF? = null

        for (region in regions) {
            // 快速边界框检查
            region.boundingBox?.let { bbox ->
                if (bbox.size >= 4) {
                    val regionRect = RectF(
                        bbox[0].toFloat(),
                        bbox[1].toFloat(),
                        bbox[2].toFloat(),
                        bbox[3].toFloat()
                    )

                    // 计算交集
                    val intersection = RectF()
                    if (intersection.setIntersect(blockRect, regionRect)) {
                        // 进一步精确检查：采样马赛克块内的像素
                        val refinedRect = refineClippingWithPixelSampling(intersection, region)
                        if (refinedRect != null) {
                            clippedRect = if (clippedRect == null) {
                                refinedRect
                            } else {
                                // 合并多个区域的交集
                                RectF().apply {
                                    left = minOf(clippedRect!!.left, refinedRect.left)
                                    top = minOf(clippedRect!!.top, refinedRect.top)
                                    right = maxOf(clippedRect!!.right, refinedRect.right)
                                    bottom = maxOf(clippedRect!!.bottom, refinedRect.bottom)
                                }
                            }
                        }
                    }
                }
            }
        }

        return clippedRect
    }

    /**
     * 通过像素采样精确裁剪
     */
    private fun refineClippingWithPixelSampling(rect: RectF, region: Region): RectF? {
        // 为了性能，只对边缘进行采样检查
        val samplePoints = listOf(
            // 四个角
            Pair(rect.left.toInt(), rect.top.toInt()),
            Pair(rect.right.toInt(), rect.top.toInt()),
            Pair(rect.left.toInt(), rect.bottom.toInt()),
            Pair(rect.right.toInt(), rect.bottom.toInt()),
            // 四个边的中点
            Pair((rect.left + rect.right).toInt() / 2, rect.top.toInt()),
            Pair((rect.left + rect.right).toInt() / 2, rect.bottom.toInt()),
            Pair(rect.left.toInt(), (rect.top + rect.bottom).toInt() / 2),
            Pair(rect.right.toInt(), (rect.top + rect.bottom).toInt() / 2),
            // 中心点
            Pair((rect.left + rect.right).toInt() / 2, (rect.top + rect.bottom).toInt() / 2)
        )

        var validPoints = 0
        var minX = rect.right
        var minY = rect.bottom
        var maxX = rect.left
        var maxY = rect.top

        // 检查采样点是否在区域内
        for ((x, y) in samplePoints) {
            if (isPixelInRegion(x, y, region)) {
                validPoints++
                minX = minOf(minX, x.toFloat())
                minY = minOf(minY, y.toFloat())
                maxX = maxOf(maxX, x.toFloat())
                maxY = maxOf(maxY, y.toFloat())
            }
        }

        // 如果有足够的有效点，返回精确的边界
        return if (validPoints >= 3) {
            RectF(minX, minY, maxX, maxY)
        } else if (validPoints > 0) {
            // 如果只有少数点有效，返回一个缩小的区域
            val shrinkFactor = 0.7f
            val centerX = (rect.left + rect.right) / 2
            val centerY = (rect.top + rect.bottom) / 2
            val halfWidth = (rect.right - rect.left) * shrinkFactor / 2
            val halfHeight = (rect.bottom - rect.top) * shrinkFactor / 2

            RectF(
                centerX - halfWidth,
                centerY - halfHeight,
                centerX + halfWidth,
                centerY + halfHeight
            )
        } else {
            null
        }
    }

    /**
     * 检查像素是否在区域内
     */
    private fun isPixelInRegion(x: Int, y: Int, region: Region): Boolean {
        // 使用现有的像素映射缓存
        val key = "$x,$y"
        return hintRegionPixelMap?.get(key) == region
    }

    /**
     * 设置马赛克边界裁剪功能
     * @param enabled true=启用边界裁剪(更平滑), false=禁用(更高性能)
     */
    fun setMosaicBoundaryClipping(enabled: Boolean) {
        enableMosaicBoundaryClipping = enabled
        // 如果当前有马赛克显示，重新绘制
        if (hintRegions.isNotEmpty()) {
            invalidate()
        }
    }

    /**
     * 获取当前马赛克边界裁剪状态
     */
    fun isMosaicBoundaryClippingEnabled(): Boolean {
        return enableMosaicBoundaryClipping
    }

    /**
     * 强制启用边界裁剪并重新绘制 - 用于测试
     */
    fun forceEnableBoundaryClipping() {
        enableMosaicBoundaryClipping = true
        android.util.Log.d("ColoringView", "Force enabled boundary clipping")
        invalidate()
    }

    /**
     * 设置像素级马赛克渲染
     * @param enabled true=像素级渲染(完美贴合), false=块级渲染(高性能)
     */
    fun setPixelLevelMosaic(enabled: Boolean) {
        enablePixelLevelMosaic = enabled
        android.util.Log.d("ColoringView", "Pixel-level mosaic: $enabled")
        // 如果当前有马赛克显示，重新绘制
        if (hintRegions.isNotEmpty()) {
            invalidate()
        }
    }

    /**
     * 获取当前像素级马赛克状态
     */
    fun isPixelLevelMosaicEnabled(): Boolean {
        return enablePixelLevelMosaic
    }

    /**
     * 设置智能数字显示
     * @param enabled true=启用智能显示(小区域不显示), false=传统显示
     */
    fun setSmartNumberDisplay(enabled: Boolean) {
        enableSmartNumberDisplay = enabled
        android.util.Log.d("ColoringView", "Smart number display: $enabled")
        invalidate()
    }

    /**
     * 获取当前智能数字显示状态
     */
    fun isSmartNumberDisplayEnabled(): Boolean {
        return enableSmartNumberDisplay
    }

    /**
     * 计算区域的显示大小（考虑缩放）
     */
    private fun calculateRegionDisplaySize(region: Region): Float {
        val bbox = region.boundingBox
        if (bbox == null || bbox.size < 4) {
            // 如果没有边界框，使用像素数量估算
            return sqrt(region.pixels.size.toFloat()) * scaleFactor
        }

        val width = (bbox[2] - bbox[0]) * scaleFactor
        val height = (bbox[3] - bbox[1]) * scaleFactor
        return minOf(width, height) // 返回较小的边长
    }

    /**
     * 判断区域是否足够大可以显示数字
     */
    private fun shouldShowRegionNumber(region: Region): Boolean {
        // 1. 检查缩放级别 - 太小时不显示
        if (scaleFactor < 1.0f) return false

        // 2. 检查区域显示大小
        val displaySize = calculateRegionDisplaySize(region)
        val minSizeForNumber = 35f // 最小35像素才显示数字
        if (displaySize < minSizeForNumber) return false

        // 3. 检查是否为细长区域
        if (isNarrowRegion(region)) {
            // 细长区域需要更大的尺寸才显示数字
            return displaySize >= 50f
        }

        // 4. 检查区域像素数量（避免显示在分散的小区域）
        val pixelCount = region.pixels.size
        if (pixelCount < 100) return false // 少于100像素的区域不显示

        return true
    }

    /**
     * 计算适合区域的数字大小
     */
    private fun calculateNumberSize(region: Region): Float {
        val displaySize = calculateRegionDisplaySize(region)

        return when {
            displaySize >= 80f -> 16f   // 大区域：16sp
            displaySize >= 50f -> 12f   // 中等区域：12sp
            displaySize >= 30f -> 10f   // 小区域：10sp
            else -> 0f                  // 太小不显示
        }
    }

    /**
     * 检查数字是否会遮挡重要内容
     */
    private fun wouldNumberObstructContent(region: Region, fontSize: Float): Boolean {
        val displaySize = calculateRegionDisplaySize(region)
        val textArea = fontSize * fontSize * 0.8f // 估算文字占用面积
        val regionArea = displaySize * displaySize

        // 如果文字占用区域面积超过30%，认为会遮挡内容
        val obstructionRatio = textArea / regionArea
        return obstructionRatio > 0.3f
    }

    /**
     * 检查区域是否为细长形状（容易被数字遮挡）
     */
    private fun isNarrowRegion(region: Region): Boolean {
        val bbox = region.boundingBox
        if (bbox == null || bbox.size < 4) return false

        val width = (bbox[2] - bbox[0]) * scaleFactor
        val height = (bbox[3] - bbox[1]) * scaleFactor

        if (width <= 0 || height <= 0) return false

        val aspectRatio = maxOf(width / height, height / width)
        return aspectRatio > 3.0f // 长宽比超过3:1认为是细长区域
    }

    /**
     * 聚合马赛克绘制 - 为分散的小区域提供连接性视觉提示
     */
    private fun drawAggregatedMosaic(canvas: Canvas, region: Region, mosaicBlocks: List<MosaicBlock>) {
        // 1. 绘制标准马赛克块
        drawStandardMosaic(canvas, mosaicBlocks)

        // 2. 添加连接性提示（淡化的连接线或区域高亮）
        if (mosaicBlocks.size > 1) {
            drawConnectivityHints(canvas, region, mosaicBlocks)
        }
    }

    /**
     * 绘制连接性提示
     */
    private fun drawConnectivityHints(canvas: Canvas, region: Region, mosaicBlocks: List<MosaicBlock>) {
        // 创建一个非常淡的背景高亮来显示整个区域的连接性
        val originalAlpha = mosaicPaint.alpha
        mosaicPaint.alpha = 20 // 非常淡的背景

        // 绘制区域的边界框作为连接性提示
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                canvas.drawRect(
                    bbox[0].toFloat(),
                    bbox[1].toFloat(),
                    bbox[2].toFloat(),
                    bbox[3].toFloat(),
                    mosaicPaint
                )
            }
        }

        // 恢复透明度
        mosaicPaint.alpha = originalAlpha
    }

    /**
     * 判断是否应该使用聚合渲染
     */
    private fun shouldUseAggregatedRendering(region: Region, mosaicBlocks: List<MosaicBlock>): Boolean {
        // 如果区域很小但马赛克块分散，使用聚合渲染
        val regionArea = region.area
        val blockCount = mosaicBlocks.size

        // 条件1: 小区域但有多个马赛克块
        if (regionArea < 500 && blockCount > 1) {
            return true
        }

        // 条件2: 马赛克块密度很低（可能是分散的区域）
        val blockDensity = blockCount.toFloat() / regionArea
        if (blockDensity < 0.01 && blockCount > 2) { // 密度很低但有多个块
            return true
        }

        return false
    }

    /**
     * 回退马赛克绘制 - 兼容旧数据格式
     */
    private fun drawFallbackMosaic(canvas: Canvas, region: Region) {
        // 使用网格采样，创建稀疏的马赛克效果
        val blockSize = 8 // 8x8像素的网格
        val drawnBlocks = mutableSetOf<Pair<Int, Int>>()

        // 限制最大绘制块数
        val maxBlocks = 50
        var blockCount = 0

        // 按网格采样像素
        for (i in region.pixels.indices step maxOf(1, region.pixels.size / 100)) {
            if (blockCount >= maxBlocks) break

            val pixel = region.pixels[i]
            val x = pixel[0]
            val y = pixel[1]

            // 计算网格位置
            val gridX = (x / blockSize) * blockSize
            val gridY = (y / blockSize) * blockSize
            val blockKey = Pair(gridX, gridY)

            // 避免重复绘制同一个网格块
            if (!drawnBlocks.contains(blockKey)) {
                drawnBlocks.add(blockKey)

                if (enableMosaicBoundaryClipping) {
                    // 使用边界裁剪绘制格子马赛克块
                    drawFallbackBoundaryClippedCheckerboardBlock(canvas, gridX, gridY, blockSize, region)
                } else {
                    // 绘制标准格子马赛克块
                    drawCheckerboardPattern(
                        canvas,
                        gridX.toFloat(),
                        gridY.toFloat(),
                        blockSize.toFloat(),
                        blockSize.toFloat(),
                        1.0f
                    )
                }

                blockCount++
            }
        }
    }

    /**
     * 回退路径的边界裁剪格子马赛克块绘制
     */
    private fun drawFallbackBoundaryClippedCheckerboardBlock(
        canvas: Canvas,
        gridX: Int,
        gridY: Int,
        blockSize: Int,
        region: Region
    ) {
        // 检查马赛克块的5个关键点
        val checkPoints = arrayOf(
            Pair(gridX, gridY),                                    // 左上角
            Pair(gridX + blockSize - 1, gridY),                   // 右上角
            Pair(gridX, gridY + blockSize - 1),                   // 左下角
            Pair(gridX + blockSize - 1, gridY + blockSize - 1),   // 右下角
            Pair(gridX + blockSize / 2, gridY + blockSize / 2)    // 中心点
        )

        var validPoints = 0
        for ((x, y) in checkPoints) {
            if (isPointInRegion(x, y, region)) {
                validPoints++
            }
        }

        // 根据有效点数量决定绘制策略
        when {
            validPoints >= 4 -> {
                // 几乎完全在区域内，绘制完整格子块
                drawCheckerboardPattern(
                    canvas,
                    gridX.toFloat(),
                    gridY.toFloat(),
                    blockSize.toFloat(),
                    blockSize.toFloat(),
                    1.0f
                )
            }
            validPoints >= 2 -> {
                // 部分在区域内，绘制缩小的格子块
                val shrinkFactor = 0.8f
                val shrinkOffset = blockSize * (1 - shrinkFactor) / 2
                drawCheckerboardPattern(
                    canvas,
                    gridX + shrinkOffset,
                    gridY + shrinkOffset,
                    blockSize - shrinkOffset * 2,
                    blockSize - shrinkOffset * 2,
                    0.9f
                )
            }
            validPoints >= 1 -> {
                // 少量在区域内，绘制很小的格子中心块
                val shrinkFactor = 0.6f
                val shrinkOffset = blockSize * (1 - shrinkFactor) / 2
                drawCheckerboardPattern(
                    canvas,
                    gridX + shrinkOffset,
                    gridY + shrinkOffset,
                    blockSize - shrinkOffset * 2,
                    blockSize - shrinkOffset * 2,
                    0.7f
                )
            }
            // validPoints == 0: 不绘制
        }
    }




    // 移除了不再需要的复杂路径和边界计算方法

    // 移除了复杂的棋盘格绘制方法，现在使用高性能的纹理贴图方案

    /**
     * 像素级马赛克渲染 - 完美贴合区域边界
     */
    private fun drawPixelLevelMosaic(canvas: Canvas, region: Region) {
        if (region.pixels.isEmpty()) return

        // 马赛克参数
        val checkerSize = 4 // 4x4像素格子
        val baseAlpha = 180 // 基础透明度 (0-255)

        // 性能优化：对大区域进行采样
        val maxPixels = 2000 // 最多处理2000个像素点
        val sampleRate = maxOf(1, region.pixels.size / maxPixels)

        // 分别收集蓝色和白色像素点，用于批量绘制
        val bluePixels = mutableListOf<Float>()
        val whitePixels = mutableListOf<Float>()

        // 获取区域边界框，用于计算格子偏移
        val bbox = region.boundingBox
        val offsetX = if (bbox != null && bbox.size >= 4) bbox[0] else 0
        val offsetY = if (bbox != null && bbox.size >= 4) bbox[1] else 0

        // 遍历区域像素，按格子模式分类
        for (i in region.pixels.indices step sampleRate) {
            val pixel = region.pixels[i]
            val x = pixel[0]
            val y = pixel[1]

            // 计算该像素属于哪个格子（相对于区域起点）
            val gridX = (x - offsetX) / checkerSize
            val gridY = (y - offsetY) / checkerSize
            val isBluePixel = (gridX + gridY) % 2 == 0

            if (isBluePixel) {
                bluePixels.add(x.toFloat())
                bluePixels.add(y.toFloat())
            } else {
                whitePixels.add(x.toFloat())
                whitePixels.add(y.toFloat())
            }
        }

        // 批量绘制蓝色像素
        if (bluePixels.isNotEmpty()) {
            mosaicPaint.color = blueColor
            mosaicPaint.alpha = baseAlpha
            mosaicPaint.strokeWidth = 1.5f // 稍微粗一点，确保可见
            canvas.drawPoints(bluePixels.toFloatArray(), mosaicPaint)
        }

        // 批量绘制白色像素
        if (whitePixels.isNotEmpty()) {
            mosaicPaint.color = whiteColor
            mosaicPaint.alpha = baseAlpha
            mosaicPaint.strokeWidth = 1.5f
            canvas.drawPoints(whitePixels.toFloatArray(), mosaicPaint)
        }
    }

    /**
     * 绘制蓝白格子图案 - 保留作为备用方案
     */
    private fun drawCheckerboardPattern(
        canvas: Canvas,
        left: Float,
        top: Float,
        width: Float,
        height: Float,
        alphaFactor: Float = 1.0f
    ) {
        // 格子大小 - 每个小格子的尺寸
        val checkerSize = 4f // 4x4像素的小格子

        // 计算需要绘制的格子数量
        val cols = (width / checkerSize).toInt() + 1
        val rows = (height / checkerSize).toInt() + 1

        // 保存原始透明度
        val originalAlpha = mosaicPaint.alpha
        val adjustedAlpha = (255 * alphaFactor * 0.8f).toInt() // 稍微透明

        for (row in 0 until rows) {
            for (col in 0 until cols) {
                val x = left + col * checkerSize
                val y = top + row * checkerSize

                // 计算当前格子的实际大小（处理边界）
                val actualWidth = minOf(checkerSize, left + width - x)
                val actualHeight = minOf(checkerSize, top + height - y)

                if (actualWidth > 0 && actualHeight > 0) {
                    // 棋盘格模式：(row + col) % 2 决定颜色
                    val isBlue = (row + col) % 2 == 0

                    mosaicPaint.color = if (isBlue) blueColor else whiteColor
                    mosaicPaint.alpha = adjustedAlpha

                    // 绘制小格子
                    canvas.drawRect(
                        x,
                        y,
                        x + actualWidth,
                        y + actualHeight,
                        mosaicPaint
                    )

                    // 绘制格子边框（可选，增强视觉效果）
                    if (actualWidth >= checkerSize && actualHeight >= checkerSize) {
                        mosaicBorderPaint.alpha = (adjustedAlpha * 0.3f).toInt()
                        canvas.drawRect(
                            x,
                            y,
                            x + actualWidth,
                            y + actualHeight,
                            mosaicBorderPaint
                        )
                    }
                }
            }
        }

        // 恢复原始透明度
        mosaicPaint.alpha = originalAlpha
    }

    /**
     * 撤销上一步操作
     */
    fun undo(): Boolean {
        if (!canUndo()) return false

        val action = actionHistory[currentActionIndex]
        currentActionIndex--

        when (action.type) {
            ActionType.FILL -> {
                // 撤销填色：移除区域
                filledRegions.remove(action.regionId)
                filledRegionsBitmap = null // 强制重新生成
                updateHintRegions()
                invalidate()

                // 通知进度变化
                onProgressChanged?.invoke(filledRegions.size, coloringData?.regions?.size ?: 0)

                android.util.Log.d("ColoringView", "Undo fill region ${action.regionId}")
                return true
            }
            ActionType.CLEAR -> {
                // 撤销清除：重新填色
                filledRegions.add(action.regionId)
                filledRegionsBitmap = null // 强制重新生成
                updateHintRegions()
                invalidate()

                // 通知进度变化
                onProgressChanged?.invoke(filledRegions.size, coloringData?.regions?.size ?: 0)

                android.util.Log.d("ColoringView", "Undo clear region ${action.regionId}")
                return true
            }
        }

        return false
    }

    /**
     * 重做下一步操作
     */
    fun redo(): Boolean {
        if (!canRedo()) return false

        currentActionIndex++
        val action = actionHistory[currentActionIndex]

        when (action.type) {
            ActionType.FILL -> {
                // 重做填色
                filledRegions.add(action.regionId)
                filledRegionsBitmap = null // 强制重新生成
                updateHintRegions()
                invalidate()

                // 通知进度变化
                onProgressChanged?.invoke(filledRegions.size, coloringData?.regions?.size ?: 0)

                android.util.Log.d("ColoringView", "Redo fill region ${action.regionId}")
                return true
            }
            ActionType.CLEAR -> {
                // 重做清除
                filledRegions.remove(action.regionId)
                filledRegionsBitmap = null // 强制重新生成
                updateHintRegions()
                invalidate()

                // 通知进度变化
                onProgressChanged?.invoke(filledRegions.size, coloringData?.regions?.size ?: 0)

                android.util.Log.d("ColoringView", "Redo clear region ${action.regionId}")
                return true
            }
        }

        return false
    }

    /**
     * 检查是否可以撤销
     */
    fun canUndo(): Boolean {
        return currentActionIndex >= 0
    }

    /**
     * 检查是否可以重做
     */
    fun canRedo(): Boolean {
        return currentActionIndex < actionHistory.size - 1
    }

    /**
     * 添加操作到历史记录
     */
    private fun addActionToHistory(action: ColoringAction) {
        // 如果当前不在历史记录末尾，删除后面的记录
        if (currentActionIndex < actionHistory.size - 1) {
            actionHistory.subList(currentActionIndex + 1, actionHistory.size).clear()
        }

        // 添加新操作
        actionHistory.add(action)
        currentActionIndex = actionHistory.size - 1

        // 限制历史记录大小
        if (actionHistory.size > maxHistorySize) {
            actionHistory.removeAt(0)
            currentActionIndex--
        }

        android.util.Log.d("ColoringView", "Added action to history: ${action.type} region ${action.regionId}")
    }

    /**
     * 清空操作历史
     */
    fun clearHistory() {
        actionHistory.clear()
        currentActionIndex = -1
        android.util.Log.d("ColoringView", "Cleared action history")
    }

    /**
     * 检查某种颜色是否全部完成
     */
    private fun checkColorCompletion(colorHex: String) {
        val data = coloringData ?: return
        val normalizedColor = normalizeColorHex(colorHex)

        // 找到该颜色的所有区域
        val colorRegions = data.regions.filter {
            normalizeColorHex(it.colorHex) == normalizedColor
        }

        // 检查是否全部填色
        val allFilled = colorRegions.all { filledRegions.contains(it.id) }

        if (allFilled && colorRegions.isNotEmpty()) {
            onColorCompleted?.invoke(colorHex, colorRegions.size)
            android.util.Log.d("ColoringView", "Color $normalizedColor completed! ${colorRegions.size} regions")
        }
    }

    /**
     * 清除指定区域的填色
     */
    fun clearRegion(regionId: Int): Boolean {
        if (!filledRegions.contains(regionId)) return false

        val region = coloringData?.regions?.find { it.id == regionId } ?: return false

        // 记录操作到历史
        val action = ColoringAction(ActionType.CLEAR, regionId, region.colorHex)
        addActionToHistory(action)

        // 清除填色
        filledRegions.remove(regionId)
        filledRegionsBitmap = null // 强制重新生成
        updateHintRegions()
        invalidate()

        // 通知进度更新
        val (filled, total) = getProgress()
        onProgressChanged?.invoke(filled, total)

        android.util.Log.d("ColoringView", "Cleared region $regionId")
        return true
    }

    /**
     * 获取填色统计信息
     */
    fun getColoringStats(): Map<String, Any> {
        val data = coloringData ?: return emptyMap()
        val totalRegions = data.regions.size
        val filledCount = filledRegions.size
        val progressPercentage = if (totalRegions > 0) (filledCount * 100) / totalRegions else 0

        // 计算各颜色的完成情况
        val colorProgress = mutableMapOf<String, Pair<Int, Int>>() // 颜色 -> (已完成, 总数)
        data.regions.groupBy { normalizeColorHex(it.colorHex) }.forEach { (color, regions) ->
            val completed = regions.count { filledRegions.contains(it.id) }
            colorProgress[color] = Pair(completed, regions.size)
        }

        return mapOf(
            "totalRegions" to totalRegions,
            "filledRegions" to filledCount,
            "progressPercentage" to progressPercentage,
            "colorProgress" to colorProgress,
            "canUndo" to canUndo(),
            "canRedo" to canRedo(),
            "historySize" to actionHistory.size
        )
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        // 清理历史记录
        clearHistory()
        // 简化的实现不需要特殊清理
    }

    /**
     * 绘制区域编号 - 优化版本，减少卡顿
     * 根据区域大小和缩放级别决定是否显示，并动态调整字体大小
     */
    private fun drawRegionNumber(canvas: Canvas, region: Region) {
        // 检查是否启用智能数字显示
        if (enableSmartNumberDisplay) {
            // 智能显示检查 - 使用新的区域大小计算方法
            if (!shouldShowRegionNumber(region)) return
        } else {
            // 传统显示逻辑 - 简单的缩放检查
            if (scaleFactor < 2.0f) return
        }

        // 获取对应的颜色编号
        val colorNumber = getColorNumberForRegion(region)
        if (colorNumber == null) return

        // 根据区域大小动态计算字体大小
        val fontSize = calculateNumberSize(region)
        if (fontSize <= 0f) return // 区域太小，不显示数字

        // 计算区域中心点
        val centerPoint = calculateRegionCenter(region)

        // 检查中心点是否在当前可见区域内
        if (!isPointVisible(centerPoint.first, centerPoint.second)) return

        // 检查数字是否会遮挡重要内容
        if (wouldNumberObstructContent(region, fontSize)) return

        // 设置文字画笔 - 使用静态画笔避免重复创建
        getTextPaint().apply {
            textSize = fontSize
            // 根据区域大小调整文字样式
            when {
                fontSize >= 16f -> {
                    strokeWidth = 2f
                    style = Paint.Style.FILL_AND_STROKE
                }
                fontSize >= 12f -> {
                    strokeWidth = 1f
                    style = Paint.Style.FILL_AND_STROKE
                }
                else -> {
                    style = Paint.Style.FILL
                }
            }
        }

        // 绘制编号文字
        val textY = centerPoint.second + fontSize / 3 // 调整垂直居中
        canvas.drawText(colorNumber.toString(), centerPoint.first, textY, getTextPaint())
    }

    /**
     * 在屏幕坐标系下绘制区域编号 - 完整版本
     */
    private fun drawRegionNumberInScreenCoords(canvas: Canvas, region: Region) {
        // 检查是否启用智能数字显示
        if (enableSmartNumberDisplay) {
            // 智能显示检查 - 使用新的区域大小计算方法
            if (!shouldShowRegionNumber(region)) return
        } else {
            // 传统显示逻辑 - 简单的缩放检查
            if (scaleFactor < 2.0f) return
        }

        // 获取对应的颜色编号
        val colorNumber = getColorNumberForRegion(region)
        if (colorNumber == null) return

        // 根据区域大小动态计算字体大小
        val fontSize = calculateNumberSize(region)
        if (fontSize <= 0f) return // 区域太小，不显示数字

        // 计算区域中心点（图像坐标系）
        val imageCenterPoint = calculateRegionCenter(region)

        // 转换到屏幕坐标系
        val screenCoords = convertImageToScreenCoords(imageCenterPoint.first, imageCenterPoint.second)
        if (screenCoords == null) return

        // 检查屏幕坐标是否在可见区域内
        if (!isScreenPointVisible(screenCoords.first, screenCoords.second)) return

        // 检查数字是否会遮挡重要内容
        if (wouldNumberObstructContent(region, fontSize)) return

        // 设置文字画笔 - 使用静态画笔避免重复创建
        getTextPaint().apply {
            textSize = fontSize
            // 根据区域大小调整文字样式
            when {
                fontSize >= 16f -> {
                    strokeWidth = 2f
                    style = Paint.Style.FILL_AND_STROKE
                }
                fontSize >= 12f -> {
                    strokeWidth = 1f
                    style = Paint.Style.FILL_AND_STROKE
                }
                else -> {
                    style = Paint.Style.FILL
                }
            }
        }

        // 在屏幕坐标系下绘制编号文字
        val textY = screenCoords.second + fontSize / 3
        canvas.drawText(colorNumber.toString(), screenCoords.first, textY, getTextPaint())
    }

    /**
     * 在屏幕坐标系下绘制区域编号 - 简化版本（用于交互时）
     */
    private fun drawRegionNumberInScreenCoordsSimplified(canvas: Canvas, region: Region) {
        // 简化的缩放检查
        if (scaleFactor < 2.0f) return

        // 获取对应的颜色编号
        val colorNumber = getColorNumberForRegion(region)
        if (colorNumber == null) return

        // 使用固定的字体大小，避免复杂计算
        val fontSize = (scaleFactor * 8f).coerceIn(10f, 20f)

        // 快速计算区域中心点（使用边界框）
        val imageCenterPoint = calculateRegionCenterFast(region)

        // 转换到屏幕坐标系
        val screenCoords = convertImageToScreenCoords(imageCenterPoint.first, imageCenterPoint.second)
        if (screenCoords == null) return

        // 设置简化的文字画笔
        getTextPaint().apply {
            textSize = fontSize
            strokeWidth = 1f
            style = Paint.Style.FILL_AND_STROKE
        }

        // 在屏幕坐标系下绘制编号文字
        val textY = screenCoords.second + fontSize / 3
        canvas.drawText(colorNumber.toString(), screenCoords.first, textY, getTextPaint())
    }

    /**
     * 简化的区域编号绘制 - 用于交互时的高性能显示
     * @deprecated 使用drawRegionNumberInScreenCoordsSimplified替代，避免位移问题
     */
    private fun drawRegionNumberSimplified(canvas: Canvas, region: Region) {
        // 简化的缩放检查
        if (scaleFactor < 2.0f) return

        // 获取对应的颜色编号
        val colorNumber = getColorNumberForRegion(region)
        if (colorNumber == null) return

        // 使用固定的字体大小，避免复杂计算
        val fontSize = (scaleFactor * 8f).coerceIn(10f, 20f)

        // 快速计算区域中心点（使用边界框）
        val centerPoint = calculateRegionCenterFast(region)

        // 设置简化的文字画笔
        getTextPaint().apply {
            textSize = fontSize
            strokeWidth = 1f
            style = Paint.Style.FILL_AND_STROKE
        }

        // 绘制编号文字
        val textY = centerPoint.second + fontSize / 3
        canvas.drawText(colorNumber.toString(), centerPoint.first, textY, getTextPaint())
    }

    /**
     * 快速计算区域中心点 - 使用边界框，避免复杂的像素计算
     */
    private fun calculateRegionCenterFast(region: Region): Pair<Float, Float> {
        val boundingBox = region.boundingBox
        return if (boundingBox != null && boundingBox.size >= 4) {
            val centerX = (boundingBox[0] + boundingBox[2]) / 2f
            val centerY = (boundingBox[1] + boundingBox[3]) / 2f
            Pair(centerX, centerY)
        } else {
            // 如果没有边界框，使用第一个像素点
            if (region.pixels.isNotEmpty()) {
                val firstPixel = region.pixels[0]
                Pair(firstPixel[0].toFloat(), firstPixel[1].toFloat())
            } else {
                Pair(0f, 0f)
            }
        }
    }

    // 静态文字画笔，避免重复创建
    private var textPaint: Paint? = null

    /**
     * 获取文字画笔，复用对象减少内存分配
     */
    private fun getTextPaint(): Paint {
        if (textPaint == null) {
            textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                color = Color.WHITE
                textAlign = Paint.Align.CENTER
                style = Paint.Style.FILL
                typeface = Typeface.DEFAULT_BOLD
                // 添加阴影效果，确保在蓝色背景上可见
                setShadowLayer(2f, 1f, 1f, Color.BLACK)
            }
        }
        return textPaint!!
    }

    /**
     * 检查点是否在当前可见区域内
     */
    private fun isPointVisible(x: Float, y: Float): Boolean {
        val points = floatArrayOf(x, y)
        matrix.mapPoints(points)
        val screenX = points[0]
        val screenY = points[1]

        // 添加一些边距，避免边缘文字被裁剪
        val margin = 50f
        return screenX >= -margin && screenX <= width + margin &&
               screenY >= -margin && screenY <= height + margin
    }

    /**
     * 获取区域对应的颜色编号
     */
    private fun getColorNumberForRegion(region: Region): Int? {
        val data = coloringData ?: return null

        // 查找与区域颜色匹配的调色板项目
        val matchingColor = data.colorPalette.find { palette ->
            normalizeColorHex(palette.colorHex) == normalizeColorHex(region.colorHex)
        }

        return matchingColor?.id
    }

    /**
     * 计算区域中心点
     */
    private fun calculateRegionCenter(region: Region): Pair<Float, Float> {
        if (region.pixels.isEmpty()) {
            return Pair(0f, 0f)
        }

        // 使用boundingBox计算中心（如果可用）
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                val centerX = (bbox[0] + bbox[2]) / 2f
                val centerY = (bbox[1] + bbox[3]) / 2f
                return Pair(centerX, centerY)
            }
        }

        // 否则计算像素点的平均位置
        val avgX = region.pixels.map { it[0] }.average().toFloat()
        val avgY = region.pixels.map { it[1] }.average().toFloat()
        return Pair(avgX, avgY)
    }

    /**
     * 根据区域大小和缩放级别动态计算字体大小 - 优化版本
     */
    private fun calculateFontSize(region: Region): Float {
        // 简化计算，使用像素数量而不是复杂的显示大小计算
        val pixelCount = region.pixels.size

        // 基于像素数量快速计算基础字体大小
        val baseFontSize = when {
            pixelCount > 5000 -> 28f   // 大区域
            pixelCount > 2000 -> 22f   // 中等区域
            pixelCount > 1000 -> 18f   // 小区域
            pixelCount > 500 -> 16f    // 很小区域
            else -> 14f                // 最小区域
        }

        // 根据缩放级别调整字体大小
        val adjustedSize = baseFontSize * scaleFactor

        // 限制字体大小范围，确保可读性
        return adjustedSize.coerceIn(12f, 42f)
    }



    override fun onTouchEvent(event: MotionEvent): Boolean {
        return try {
            // 1. 检查视图状态
            if (!isAttachedToWindow || width <= 0 || height <= 0) {
                android.util.Log.w("ColoringView", "View not ready for touch events")
                return false
            }
            
            // 2. 检查事件有效性
            if (event.actionMasked == MotionEvent.ACTION_CANCEL) {
                android.util.Log.d("ColoringView", "Touch event cancelled")
                return false
            }
            
            // 3. 边界检查
            val x = event.x
            val y = event.y
            if (x < 0 || x >= width || y < 0 || y >= height) {
                android.util.Log.w("ColoringView", "Touch outside bounds: ($x, $y)")
                return false
            }
            
            // 4. 检查必要资源
            if (outlineBitmap == null || coloringData == null) {
                android.util.Log.w("ColoringView", "Drawing resources not initialized")
                return false
            }

            var handled = false
            val currentPointerCount = event.pointerCount

            // 检测多指到单指的转换
            detectMultiTouchTransition(currentPointerCount)

            // 调试日志
            if (currentPointerCount > 1) {
                android.util.Log.d("ColoringView", "Multi-touch detected: $currentPointerCount pointers")
            }

            // 优先处理缩放手势
            handled = scaleDetector.onTouchEvent(event) || handled

            // 处理双击手势
            handled = gestureDetector.onTouchEvent(event) || handled

            // 只有在非缩放状态下才处理单点触控
            if (!scaleDetector.isInProgress) {
                when (event.actionMasked) {
                    MotionEvent.ACTION_DOWN -> {
                        handleTouchDown(event)
                        handled = true
                    }

                    MotionEvent.ACTION_MOVE -> {
                        handleTouchMove(event, currentPointerCount)
                        handled = true
                    }

                    MotionEvent.ACTION_UP -> {
                        handleTouchUp()
                        handled = true
                    }

                    MotionEvent.ACTION_POINTER_DOWN -> {
                        handlePointerDown()
                        handled = true
                    }

                    MotionEvent.ACTION_POINTER_UP -> {
                        handlePointerUp()
                        handled = true
                    }
                }
            }

            // 更新指针数量记录
            lastPointerCount = currentPointerCount

            handled || super.onTouchEvent(event)
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Touch event error: ${e.message}", e)
            // 发送崩溃报告但不让应用崩溃
            reportNonFatalError("TouchEventError", e)
            false
        }
    }
    
    private fun handleTouchDown(event: MotionEvent) {
        try {
            // 重置拖拽冷却状态
            isDragCooldownActive = false
            isDragging = false
            isInteracting = false
            lastTouchX = event.x
            lastTouchY = event.y
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Touch down error", e)
            reportNonFatalError("TouchDownError", e)
        }
    }
    
    private fun handleTouchMove(event: MotionEvent, currentPointerCount: Int) {
        try {
            // 只有单点触控时才进行平移，并且不在冷却期内
            if (currentPointerCount == 1 && !isDragCooldownActive) {
                val dx = event.x - lastTouchX
                val dy = event.y - lastTouchY
                val distance = kotlin.math.sqrt(dx * dx + dy * dy)

                // 只有移动距离超过最小阈值才进行拖拽，避免微小抖动
                if (distance >= MIN_DRAG_DISTANCE) {
                    // 标记为拖动和交互状态
                    if (!isDragging) {
                        isDragging = true
                        isInteracting = true
                        android.util.Log.d("ColoringView", "Drag started")
                    }

                    translateX += dx
                    translateY += dy

                    // 限制平移范围
                    constrainTranslation()

                    markMatrixDirty()
                    updateMatrix()
                    
                    // 安全的invalidate调用
                    if (isAttachedToWindow) {
                        invalidate()
                    }

                    lastTouchX = event.x
                    lastTouchY = event.y
                }
            } else if (isDragCooldownActive) {
                // 在冷却期内，更新触摸位置但不进行拖拽
                lastTouchX = event.x
                lastTouchY = event.y
                android.util.Log.d("ColoringView", "Drag blocked during cooldown period")
            }
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Touch move error", e)
            reportNonFatalError("TouchMoveError", e)
        }
    }
    
    private fun handleTouchUp() {
        try {
            // 结束拖动状态
            if (isDragging) {
                isDragging = false
                isInteracting = false
                dragEndTime = System.currentTimeMillis()
                lastInteractionTime = System.currentTimeMillis()

                // 延迟恢复高质量绘制和缓存更新
                postDelayed({
                    if (isAttachedToWindow) {
                        invalidate()  // 触发高质量重绘和缓存更新
                        android.util.Log.d("ColoringView", "High quality mosaic restored")
                    }
                }, DRAG_QUALITY_RESTORE_DELAY)

                android.util.Log.d("ColoringView", "Drag ended")
            }

            // 单击处理由GestureDetector处理
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Touch up error", e)
            reportNonFatalError("TouchUpError", e)
        }
    }
    
    private fun handlePointerDown() {
        try {
            // 多点触控开始，重置冷却状态
            isDragCooldownActive = false
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Pointer down error", e)
            reportNonFatalError("PointerDownError", e)
        }
    }
    
    private fun handlePointerUp() {
        try {
            // 多点触控结束，可能需要启动冷却期
            // 处理逻辑在detectMultiTouchTransition中
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Pointer up error", e)
            reportNonFatalError("PointerUpError", e)
        }
    }
    
    private fun reportNonFatalError(errorType: String, exception: Exception) {
        // 这里可以集成崩溃报告系统，如Firebase Crashlytics
        android.util.Log.e("ColoringView", "Non-fatal error [$errorType]: ${exception.message}", exception)
    }

    /**
     * 检测多指到单指的转换，启动拖拽冷却期
     */
    private fun detectMultiTouchTransition(currentPointerCount: Int) {
        // 检测从多指（>=2）到单指（1）的转换
        if (lastPointerCount >= 2 && currentPointerCount == 1) {
            // 启动拖拽冷却期
            multiTouchEndTime = System.currentTimeMillis()
            isDragCooldownActive = true

            android.util.Log.d("ColoringView", "Multi-touch transition detected, starting drag cooldown")

            // 启动冷却期定时器
            postDelayed({
                isDragCooldownActive = false
                android.util.Log.d("ColoringView", "Drag cooldown ended")
            }, DRAG_COOLDOWN_DURATION)
        }

        // 如果重新变为多指，立即取消冷却期
        if (currentPointerCount >= 2 && isDragCooldownActive) {
            isDragCooldownActive = false
            android.util.Log.d("ColoringView", "Multi-touch resumed, canceling drag cooldown")
        }
    }

    /**
     * 处理触摸填色 - 安全版本
     */
    private fun handleTouch(screenX: Float, screenY: Float) {
        try {
            val regionBmp = regionBitmap
            val currentColor = currentColorHex
            val data = coloringData
            
            // 检查必要资源
            if (regionBmp == null || currentColor == null || data == null) {
                android.util.Log.w("ColoringView", "Resources not ready for touch handling")
                onRegionTouched?.invoke(TouchResult(null, null, false, "资源未准备就绪"))
                return
            }

            // 将屏幕坐标转换为图像坐标
            val imageCoords = convertScreenToImageCoords(screenX, screenY)
            if (imageCoords == null) {
                onRegionTouched?.invoke(TouchResult(null, null, false, "坐标转换失败"))
                return
            }
            
            val imageX = imageCoords.first
            val imageY = imageCoords.second

            // 检查坐标是否在图像范围内
            if (imageX < 0 || imageX >= regionBmp.width || imageY < 0 || imageY >= regionBmp.height) {
                onRegionTouched?.invoke(TouchResult(null, null, false, "触摸位置超出图像范围"))
                return
            }

            // 首先检查是否点击在马赛克提示区域内
            android.util.Log.d(
                "ColoringView",
                "Checking hint region at ($imageX, $imageY), total hints: ${hintRegions.size}"
            )

            val hintRegion = findHintRegionAtPositionSafely(imageX, imageY)
            if (hintRegion != null) {
                // 点击在马赛克提示区域内，直接填色
                android.util.Log.d("ColoringView", "Hint region found! Filling region ${hintRegion.id}")
                fillRegionSafely(hintRegion, currentColor)
                return
            } else {
                android.util.Log.d("ColoringView", "No hint region found at ($imageX, $imageY)")

                // 如果没有找到马赛克区域，但当前有马赛克显示，尝试强制匹配
                if (hintRegions.isNotEmpty()) {
                    val forceMatchedRegion = tryForceMatchHintRegionSafely(imageX, imageY)
                    if (forceMatchedRegion != null) {
                        android.util.Log.d(
                            "ColoringView",
                            "Force matched hint region ${forceMatchedRegion.id}"
                        )
                        fillRegionSafely(forceMatchedRegion, currentColor)
                        return
                    }
                }
            }

            // 安全地获取触摸位置的区域ID
            val regionId = getRegionIdAtPositionSafely(regionBmp, imageX, imageY)
            if (regionId == null) {
                onRegionTouched?.invoke(TouchResult(null, null, false, "无法获取区域信息"))
                return
            }

            // 查找对应的区域
            val region = data.regions.find { it.id == regionId }

            if (region != null) {
                // 检查是否已经填色
                if (filledRegions.contains(regionId)) {
                    onRegionTouched?.invoke(TouchResult(regionId, region.colorHex, false, "区域已填色"))
                    return
                }

                // 使用标准化颜色进行匹配检查
                val normalizedCurrentColor = normalizeColorHex(currentColor)
                val normalizedRegionColor = normalizeColorHex(region.colorHex)

                android.util.Log.d(
                    "ColoringView",
                    "Touch: current=$normalizedCurrentColor, region=$normalizedRegionColor, match=${normalizedCurrentColor == normalizedRegionColor}"
                )

                if (normalizedCurrentColor == normalizedRegionColor) {
                    // 填色成功
                    fillRegionSafely(region, currentColor)
                } else {
                    onRegionTouched?.invoke(
                        TouchResult(
                            regionId,
                            region.colorHex,
                            false,
                            "颜色不匹配：需要$normalizedCurrentColor，但区域是$normalizedRegionColor"
                        )
                    )
                }
            } else {
                onRegionTouched?.invoke(TouchResult(null, null, false, "未找到对应区域"))
            }
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error handling touch", e)
            reportNonFatalError("HandleTouchError", e)
            onRegionTouched?.invoke(TouchResult(null, null, false, "触摸处理出错: ${e.message}"))
        }
    }
    
    /**
     * 安全的屏幕坐标到图像坐标转换
     */
    private fun convertScreenToImageCoords(screenX: Float, screenY: Float): Pair<Int, Int>? {
        return try {
            val points = floatArrayOf(screenX, screenY)
            val invertMatrix = Matrix()
            if (matrix.invert(invertMatrix)) {
                invertMatrix.mapPoints(points)
                Pair(points[0].toInt(), points[1].toInt())
            } else {
                android.util.Log.w("ColoringView", "Matrix inversion failed")
                null
            }
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error converting coordinates", e)
            null
        }
    }

    /**
     * 安全的图像坐标到屏幕坐标转换
     */
    private fun convertImageToScreenCoords(imageX: Float, imageY: Float): Pair<Float, Float>? {
        return try {
            val points = floatArrayOf(imageX, imageY)
            matrix.mapPoints(points)
            Pair(points[0], points[1])
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error converting image to screen coordinates", e)
            null
        }
    }

    /**
     * 检查屏幕坐标点是否在可见区域内
     */
    private fun isScreenPointVisible(screenX: Float, screenY: Float): Boolean {
        return screenX >= -50f && screenX <= width + 50f &&
               screenY >= -50f && screenY <= height + 50f
    }
    
    /**
     * 安全地获取位置的区域ID
     */
    private fun getRegionIdAtPositionSafely(regionBmp: Bitmap, x: Int, y: Int): Int? {
        return try {
            if (x >= 0 && x < regionBmp.width && y >= 0 && y < regionBmp.height) {
                val pixelColor = regionBmp.getPixel(x, y)
                decodeRegionId(pixelColor)
            } else {
                null
            }
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error getting region ID at ($x, $y)", e)
            null
        }
    }
    
    /**
     * 安全地查找提示区域
     */
    private fun findHintRegionAtPositionSafely(x: Int, y: Int): Region? {
        return try {
            findHintRegionAtPosition(x, y)
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error finding hint region at ($x, $y)", e)
            null
        }
    }
    
    /**
     * 安全地尝试强制匹配提示区域
     */
    private fun tryForceMatchHintRegionSafely(x: Int, y: Int): Region? {
        return try {
            tryForceMatchHintRegion(x, y)
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error force matching hint region at ($x, $y)", e)
            null
        }
    }
    
    /**
     * 安全地填充区域
     */
    private fun fillRegionSafely(region: Region, color: String) {
        try {
            fillRegion(region, color)
        } catch (e: Exception) {
            android.util.Log.e("ColoringView", "Error filling region ${region.id}", e)
            reportNonFatalError("FillRegionError", e)
            onRegionTouched?.invoke(TouchResult(region.id, region.colorHex, false, "填色失败: ${e.message}"))
        }
    }

    /**
     * 处理长按事件：自动关联到该区域的颜色
     */
    private fun handleLongPress(screenX: Float, screenY: Float) {
        val regionBmp = regionBitmap ?: return

        // 将屏幕坐标转换为图像坐标
        val points = floatArrayOf(screenX, screenY)
        val invertMatrix = Matrix()
        if (matrix.invert(invertMatrix)) {
            invertMatrix.mapPoints(points)
        }

        val imageX = points[0].toInt()
        val imageY = points[1].toInt()

        // 检查坐标是否在图像范围内
        if (imageX < 0 || imageX >= regionBmp.width || imageY < 0 || imageY >= regionBmp.height) {
            android.util.Log.d("ColoringView", "长按位置超出图像范围")
            return
        }

        // 获取触摸位置的区域ID
        val pixelColor = regionBmp.getPixel(imageX, imageY)
        val regionId = decodeRegionId(pixelColor)

        // 查找对应的区域
        val region = coloringData?.regions?.find { it.id == regionId }

        if (region != null) {
            // 检查是否已经填色
            if (filledRegions.contains(regionId)) {
                android.util.Log.d("ColoringView", "长按区域已填色，无需操作")
                return
            }

            // 自动选择该区域的颜色
            val regionColor = region.colorHex
            android.util.Log.d("ColoringView", "长按自动选择颜色: $regionColor (区域 $regionId)")

            // 触发颜色自动选择回调
            onColorAutoSelected?.invoke(regionColor)

            // 可选：添加触觉反馈
            performHapticFeedback(android.view.HapticFeedbackConstants.LONG_PRESS)

        } else {
            android.util.Log.d("ColoringView", "长按位置未找到对应区域")
        }
    }

    /**
     * 查找指定位置的马赛克提示区域
     * 增强版本：确保100%可点击，按需构建映射
     */
    private fun findHintRegionAtPosition(imageX: Int, imageY: Int): Region? {
        // 确保像素映射已构建（按需构建）
        ensureHintRegionPixelMap()
        
        // 首先尝试精确匹配
        val pixelMap = hintRegionPixelMap
        if (pixelMap != null) {
            val key = "$imageX,$imageY"
            val exactMatch = pixelMap[key]
            if (exactMatch != null) {
                android.util.Log.d("ColoringView", "Exact hint match at ($imageX, $imageY)")
                return exactMatch
            }
        }

        // 精确匹配失败，尝试邻近搜索（容错处理）
        val nearbyRegion = findNearbyHintRegion(imageX, imageY)
        if (nearbyRegion != null) {
            android.util.Log.d("ColoringView", "Nearby hint match at ($imageX, $imageY)")
            return nearbyRegion
        }

        // 最后回退到遍历查找
        val fallbackRegion = hintRegions.find { region ->
            isPointInRegion(imageX, imageY, region)
        }

        if (fallbackRegion != null) {
            android.util.Log.d("ColoringView", "Fallback hint match at ($imageX, $imageY)")
        } else {
            android.util.Log.d("ColoringView", "No hint region found at ($imageX, $imageY)")
        }

        return fallbackRegion
    }

    /**
     * 查找邻近的马赛克提示区域（手指友好的容错处理）
     */
    private fun findNearbyHintRegion(centerX: Int, centerY: Int): Region? {
        val pixelMap = hintRegionPixelMap ?: return null

        // 扩大搜索范围，适应手指触摸
        val searchRadius = maxOf(minTouchBufferRadius, touchBufferRadius / 2)

        for (dx in -searchRadius..searchRadius) {
            for (dy in -searchRadius..searchRadius) {
                val distance = kotlin.math.sqrt((dx * dx + dy * dy).toDouble())

                // 在搜索半径内查找
                if (distance <= searchRadius) {
                    val key = "${centerX + dx},${centerY + dy}"
                    val region = pixelMap[key]
                    if (region != null) {
                        return region
                    }
                }
            }
        }

        return null
    }

    /**
     * 尝试强制匹配马赛克区域（最后的保障机制）
     */
    private fun tryForceMatchHintRegion(clickX: Int, clickY: Int): Region? {
        // 如果用户点击的位置没有精确匹配到马赛克区域，
        // 但确实有马赛克显示，尝试找到最近的马赛克区域

        var closestRegion: Region? = null
        var minDistance = Double.MAX_VALUE

        hintRegions.forEach { region ->
            // 计算点击位置到区域中心的距离
            if (region.boundingBox != null && region.boundingBox.size >= 4) {
                val centerX = (region.boundingBox[0] + region.boundingBox[2]) / 2.0
                val centerY = (region.boundingBox[1] + region.boundingBox[3]) / 2.0

                val distance = kotlin.math.sqrt(
                    (clickX - centerX) * (clickX - centerX) +
                            (clickY - centerY) * (clickY - centerY)
                )

                if (distance < minDistance) {
                    minDistance = distance
                    closestRegion = region
                }
            }
        }

        // 根据触摸缓冲区调整强制匹配的距离阈值
        val forceMatchThreshold = maxOf(maxTouchBufferRadius.toDouble(), 25.0)

        if (minDistance <= forceMatchThreshold) {
            android.util.Log.d(
                "ColoringView",
                "Force matching closest region at distance $minDistance (threshold: $forceMatchThreshold)"
            )
            return closestRegion
        }

        return null
    }

    /**
     * 根据屏幕密度和区域大小动态调整触摸缓冲区
     */
    private fun adjustTouchBufferForDevice() {
        val density = context.resources.displayMetrics.density
        val scaledDensity = context.resources.displayMetrics.scaledDensity

        // 根据屏幕密度调整缓冲区大小（优化性能）
        val baseTouchBuffer = when {
            density >= 3.0f -> 6   // 高密度屏幕，适中缓冲区
            density >= 2.0f -> 5   // 中密度屏幕，标准缓冲区
            density >= 1.5f -> 4   // 低密度屏幕，较小缓冲区
            else -> 3              // 极低密度屏幕，最小缓冲区
        }

        // 考虑用户的字体缩放设置
        val fontScaleFactor = scaledDensity / density
        val adjustedBuffer = (baseTouchBuffer * fontScaleFactor).toInt()

        // 限制在合理范围内
        touchBufferRadius = adjustedBuffer.coerceIn(minTouchBufferRadius, maxTouchBufferRadius)

        android.util.Log.d("ColoringView", "Adjusted touch buffer: density=$density, scaledDensity=$scaledDensity, buffer=${touchBufferRadius}px")
    }

    /**
     * 设置自定义触摸缓冲区大小
     */
    fun setTouchBufferRadius(radius: Int) {
        val oldRadius = touchBufferRadius
        touchBufferRadius = radius.coerceIn(minTouchBufferRadius, maxTouchBufferRadius)

        if (oldRadius != touchBufferRadius) {
            // 缓冲区大小变化，需要重建缓存
            hintRegionPixelMap = null
            updateHintRegions()
            android.util.Log.d("ColoringView", "Touch buffer radius changed: $oldRadius -> $touchBufferRadius")
        }
    }

    /**
     * 获取当前触摸缓冲区大小
     */
    fun getTouchBufferRadius(): Int = touchBufferRadius

    /**
     * 为小区域添加完整的触摸缓冲区
     */
    private fun addTouchBufferForRegion(region: Region, pixelMap: MutableMap<String, Region>, bufferPixels: Int): Int {
        var addedPixels = 0
        val reducedRadius = minOf(touchBufferRadius, 5)  // 限制最大半径为5像素

        // 预计算圆形模板
        val circleTemplate = mutableListOf<Pair<Int, Int>>()
        for (dx in -reducedRadius..reducedRadius) {
            for (dy in -reducedRadius..reducedRadius) {
                val distance = kotlin.math.sqrt((dx * dx + dy * dy).toDouble())
                if (distance <= reducedRadius) {
                    circleTemplate.add(Pair(dx, dy))
                }
            }
        }

        // 只为部分像素添加缓冲区（采样）
        val sampleStep = maxOf(1, region.pixels.size / 100)  // 最多采样100个点
        region.pixels.forEachIndexed { index, pixel ->
            if (index % sampleStep == 0) {  // 采样
                val centerX = pixel[0]
                val centerY = pixel[1]

                circleTemplate.forEach { (dx, dy) ->
                    val bufferX = centerX + dx
                    val bufferY = centerY + dy
                    val key = "$bufferX,$bufferY"

                    if (!pixelMap.containsKey(key)) {
                        pixelMap[key] = region
                        addedPixels++
                    }
                }
            }
        }

        return addedPixels
    }

    /**
     * 为大区域只添加边界缓冲区
     */
    private fun addBoundaryBufferForRegion(region: Region, pixelMap: MutableMap<String, Region>, bufferPixels: Int): Int {
        var addedPixels = 0

        if (region.boundingBox != null && region.boundingBox.size >= 4) {
            val left = region.boundingBox[0]
            val top = region.boundingBox[1]
            val right = region.boundingBox[2]
            val bottom = region.boundingBox[3]

            val smallRadius = 2  // 只添加2像素的边界缓冲

            // 只在边界周围添加缓冲区
            for (x in (left - smallRadius)..(right + smallRadius)) {
                for (y in listOf(top - smallRadius, top - 1, bottom + 1, bottom + smallRadius)) {
                    val key = "$x,$y"
                    if (!pixelMap.containsKey(key)) {
                        // 检查是否真的靠近区域
                        if (isNearRegion(x, y, region, smallRadius)) {
                            pixelMap[key] = region
                            addedPixels++
                        }
                    }
                }
            }

            for (y in (top - smallRadius)..(bottom + smallRadius)) {
                for (x in listOf(left - smallRadius, left - 1, right + 1, right + smallRadius)) {
                    val key = "$x,$y"
                    if (!pixelMap.containsKey(key)) {
                        if (isNearRegion(x, y, region, smallRadius)) {
                            pixelMap[key] = region
                            addedPixels++
                        }
                    }
                }
            }
        }

        return addedPixels
    }

    /**
     * 检查点是否靠近区域
     */
    private fun isNearRegion(x: Int, y: Int, region: Region, maxDistance: Int): Boolean {
        // 简化检查：只检查是否在边界框附近
        if (region.boundingBox != null && region.boundingBox.size >= 4) {
            val left = region.boundingBox[0]
            val top = region.boundingBox[1]
            val right = region.boundingBox[2]
            val bottom = region.boundingBox[3]

            return x >= left - maxDistance && x <= right + maxDistance &&
                   y >= top - maxDistance && y <= bottom + maxDistance
        }
        return false
    }

    /**
     * 检查点是否在区域内
     * 优化版本：先检查边界框，再检查具体像素
     */
    private fun isPointInRegion(x: Int, y: Int, region: Region): Boolean {
        // 首先检查是否在边界框内
        val boundingBox = region.boundingBox
        if (boundingBox != null && boundingBox.size >= 4) {
            val left = boundingBox[0]
            val top = boundingBox[1]
            val right = boundingBox[2]
            val bottom = boundingBox[3]

            if (x < left || x > right || y < top || y > bottom) {
                return false
            }
        }

        // 在边界框内，检查具体像素
        return region.pixels.any { pixel ->
            pixel[0] == x && pixel[1] == y
        }
    }

    /**
     * 填充区域
     */
    private fun fillRegion(region: Region, currentColor: String) {
        // 检查是否已经填色
        if (filledRegions.contains(region.id)) {
            onRegionTouched?.invoke(TouchResult(region.id, region.colorHex, false, "区域已填色"))
            return
        }

        // 记录操作到历史
        val action = ColoringAction(ActionType.FILL, region.id, currentColor)
        addActionToHistory(action)

        // 填色成功
        filledRegions.add(region.id)

        // 清除缓存的填色bitmap，强制重新生成
        filledRegionsBitmap = null

        // 更新提醒区域
        updateHintRegions()

        invalidate()

        // 通知区域填色完成
        onRegionFilled?.invoke(region)

        // 通知进度更新
        val (filled, total) = getProgress()
        onProgressChanged?.invoke(filled, total)

        // 检查是否完成了某种颜色的所有区域
        checkColorCompletion(currentColor)

        // 检查是否完成了整个项目
        if (filled == total) {
            onProjectCompleted?.invoke()
        }

        onRegionTouched?.invoke(TouchResult(region.id, region.colorHex, true, "填色成功"))
    }

    /**
     * 限制平移范围 - 支持可滑动区域大于图片区域
     */
    private fun constrainTranslation() {
        val outline = outlineBitmap ?: return

        if (width <= 0 || height <= 0) return

        val scaledWidth = outline.width * scaleFactor
        val scaledHeight = outline.height * scaleFactor

        // 计算额外滑动空间
        val extraScrollX = calculateExtraScrollSpace(width.toFloat())
        val extraScrollY = calculateExtraScrollSpace(height.toFloat())

        // 计算X轴平移范围（增加额外滑动空间）
        val maxTranslateX: Float
        val minTranslateX: Float

        if (scaledWidth > width) {
            // 图片比屏幕宽，允许左右平移 + 额外空间
            maxTranslateX = extraScrollX
            minTranslateX = width - scaledWidth - extraScrollX
        } else {
            // 图片比屏幕窄，以居中为基础 + 额外空间
            val centerX = (width - scaledWidth) / 2f
            maxTranslateX = centerX + extraScrollX
            minTranslateX = centerX - extraScrollX
        }

        // 计算Y轴平移范围（增加额外滑动空间）
        val maxTranslateY: Float
        val minTranslateY: Float

        if (scaledHeight > height) {
            // 图片比屏幕高，允许上下平移 + 额外空间
            maxTranslateY = extraScrollY
            minTranslateY = height - scaledHeight - extraScrollY
        } else {
            // 图片比屏幕矮，以居中为基础 + 额外空间
            val centerY = (height - scaledHeight) / 2f
            maxTranslateY = centerY + extraScrollY
            minTranslateY = centerY - extraScrollY
        }

        // 确保范围有效（最小值 <= 最大值）
        val validMinX = minOf(minTranslateX, maxTranslateX)
        val validMaxX = maxOf(minTranslateX, maxTranslateX)
        val validMinY = minOf(minTranslateY, maxTranslateY)
        val validMaxY = maxOf(minTranslateY, maxTranslateY)

        translateX = translateX.coerceIn(validMinX, validMaxX)
        translateY = translateY.coerceIn(validMinY, validMaxY)

        android.util.Log.d(
            "ColoringView",
            "constrainTranslation: scaledSize=${scaledWidth}x${scaledHeight}, viewSize=${width}x${height}"
        )
        android.util.Log.d(
            "ColoringView",
            "extraScroll: X=$extraScrollX, Y=$extraScrollY"
        )
        android.util.Log.d(
            "ColoringView",
            "translateRange: X[$validMinX, $validMaxX], Y[$validMinY, $validMaxY]"
        )
        android.util.Log.d("ColoringView", "finalTranslate: ($translateX, $translateY)")
    }

    /**
     * 计算额外滑动空间
     * 根据屏幕尺寸和配置参数计算合适的额外滑动空间
     */
    private fun calculateExtraScrollSpace(screenSize: Float): Float {
        // 基于屏幕尺寸的比例计算
        val ratioBasedSpace = screenSize * EXTRA_SCROLL_RATIO

        // 限制在最小和最大值之间
        return ratioBasedSpace.coerceIn(MIN_EXTRA_SCROLL, MAX_EXTRA_SCROLL)
    }

    /**
     * 缩放手势监听器 - 手势体验优化版本
     */
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        private var lastFocusX = 0f
        private var lastFocusY = 0f

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            // 取消任何正在进行的动画
            inertiaAnimator?.cancel()
            elasticAnimator?.cancel()

            val previousScale = scaleFactor
            val currentTime = System.currentTimeMillis()

            // 使用动态敏感度调整
            val dynamicSensitivity = calculateDynamicSensitivity()
            val adjustedScaleFactor = 1f + (detector.scaleFactor - 1f) * dynamicSensitivity

            val newScale = scaleFactor * adjustedScaleFactor

            // 改进的焦点计算 - 使用加权平均来稳定多指触控
            val focusX = if (detector.focusX.isFinite()) {
                // 使用平滑过渡的焦点位置
                if (lastFocusX != 0f) {
                    lastFocusX * 0.7f + detector.focusX * 0.3f
                } else {
                    detector.focusX
                }
            } else {
                width / 2f
            }

            val focusY = if (detector.focusY.isFinite()) {
                if (lastFocusY != 0f) {
                    lastFocusY * 0.7f + detector.focusY * 0.3f
                } else {
                    detector.focusY
                }
            } else {
                height / 2f
            }

            lastFocusX = focusX
            lastFocusY = focusY

            // 弹性边界处理
            if (newScale < dynamicMinScale) {
                // 允许轻微超出下边界，提供弹性感觉
                val overshoot = (dynamicMinScale - newScale) * ELASTIC_OVERSHOOT
                scaleFactor = dynamicMinScale - overshoot
                isInElasticMode = true
            } else if (newScale > dynamicMaxScale) {
                // 允许轻微超出上边界
                val overshoot = (newScale - dynamicMaxScale) * ELASTIC_OVERSHOOT
                scaleFactor = dynamicMaxScale + overshoot
                isInElasticMode = true
            } else {
                scaleFactor = newScale
                isInElasticMode = false
            }

            // 调整平移量，使缩放以焦点为中心
            val scaleChange = scaleFactor / previousScale
            translateX = focusX + (translateX - focusX) * scaleChange
            translateY = focusY + (translateY - focusY) * scaleChange

            // 计算缩放速度用于惯性效果
            scaleVelocity = calculateScaleVelocity(scaleFactor, currentTime)

            // 标记矩阵需要更新
            markMatrixDirty()

            // 节流重绘 - 避免过度频繁的invalidate调用
            if (currentTime - lastInvalidateTime >= INVALIDATE_INTERVAL) {
                updateMatrix()
                invalidate()
                lastInvalidateTime = currentTime
            }

            return true
        }

        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            isScaling = true
            isDragging = false  // 缩放时不是拖动
            isInteracting = true  // 标记为交互状态
            lastFocusX = 0f
            lastFocusY = 0f
            scaleVelocity = 0f
            lastScaleTime = 0L

            // 取消任何正在进行的动画
            inertiaAnimator?.cancel()
            elasticAnimator?.cancel()

            android.util.Log.d("ColoringView", "Scale begin")
            return true
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            isScaling = false
            isInteracting = false
            lastInteractionTime = System.currentTimeMillis()

            val focusX = if (lastFocusX != 0f) lastFocusX else width / 2f
            val focusY = if (lastFocusY != 0f) lastFocusY else height / 2f

            // 处理弹性边界回弹
            if (isInElasticMode) {
                val targetScale = scaleFactor.coerceIn(dynamicMinScale, dynamicMaxScale)
                if (kotlin.math.abs(targetScale - scaleFactor) > 0.01f) {
                    startElasticAnimation(targetScale, focusX, focusY)
                    android.util.Log.d("ColoringView", "Scale end - elastic bounce to: $targetScale")
                    return
                }
            }

            // 启动惯性动画（如果有足够的速度）
            if (kotlin.math.abs(scaleVelocity) >= INERTIA_THRESHOLD) {
                startInertiaAnimation(focusX, focusY)
                android.util.Log.d("ColoringView", "Scale end - inertia animation with velocity: $scaleVelocity")
            } else {
                // 没有惯性，直接进行边界约束
                constrainTranslation()
                markMatrixDirty()
                updateMatrix()
                invalidate()
                android.util.Log.d("ColoringView", "Scale end - final scale: $scaleFactor")
            }

            // 缩放结束后延迟恢复高质量马赛克绘制和缓存更新
            postDelayed({
                invalidate()  // 触发缓存更新和高质量重绘
                android.util.Log.d("ColoringView", "High quality mosaic restored after scaling")
            }, INTERACTION_SETTLE_DELAY)
        }
    }

    /**
     * 清理动画资源
     */
    private fun cleanupAnimations() {
        inertiaAnimator?.cancel()
        elasticAnimator?.cancel()
        inertiaAnimator = null
        elasticAnimator = null
    }

    /**
     * 创建或更新马赛克缓存
     */
    private fun ensureMosaicCache() {
        val bitmap = outlineBitmap ?: return

        val cacheWidth = (bitmap.width * CACHE_SCALE_FACTOR).toInt()
        val cacheHeight = (bitmap.height * CACHE_SCALE_FACTOR).toInt()

        // 检查是否需要重新创建缓存
        if (mosaicCacheBitmap == null ||
            mosaicCacheBitmap!!.width != cacheWidth ||
            mosaicCacheBitmap!!.height != cacheHeight) {

            // 清理旧缓存
            mosaicCacheBitmap?.recycle()

            // 创建新缓存
            mosaicCacheBitmap = Bitmap.createBitmap(cacheWidth, cacheHeight, Bitmap.Config.ARGB_8888)
            mosaicCacheCanvas = Canvas(mosaicCacheBitmap!!)
            mosaicCacheDirty = true

            android.util.Log.d("ColoringView", "Created mosaic cache: ${cacheWidth}x${cacheHeight}")
        }
    }

    /**
     * 检查马赛克内容是否发生变化
     */
    private fun isMosaicContentChanged(): Boolean {
        val currentHash = hintRegions.hashCode()
        if (currentHash != lastHintRegionsHash) {
            lastHintRegionsHash = currentHash
            return true
        }
        return false
    }

    /**
     * 重新生成马赛克缓存
     */
    private fun regenerateMosaicCache() {
        ensureMosaicCache()

        val cacheCanvas = mosaicCacheCanvas ?: return
        val cacheBitmap = mosaicCacheBitmap ?: return

        // 清空缓存
        cacheCanvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)

        // 设置缓存画布的缩放
        cacheCanvas.save()
        cacheCanvas.scale(CACHE_SCALE_FACTOR, CACHE_SCALE_FACTOR)

        // 绘制所有马赛克到缓存
        hintRegions.forEach { region ->
            drawMosaicRegion(cacheCanvas, region, 0)
        }

        cacheCanvas.restore()
        mosaicCacheDirty = false

        android.util.Log.d("ColoringView", "Regenerated mosaic cache with ${hintRegions.size} regions")
    }

    /**
     * 清理马赛克缓存
     */
    private fun clearMosaicCache() {
        mosaicCacheBitmap?.recycle()
        mosaicCacheBitmap = null
        mosaicCacheCanvas = null
        mosaicCacheDirty = true
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cleanupAnimations()
        clearMosaicCache()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // 当View尺寸变化时，重新计算动态缩放范围
        if (w > 0 && h > 0 && outlineBitmap != null) {
            calculateDynamicScaleRange()
            android.util.Log.d("ColoringView", "Size changed: ${oldw}x${oldh} -> ${w}x${h}, recalculated scale range")
        }
    }

    /**
     * 双击手势监听器
     */
    private inner class GestureListener : GestureDetector.SimpleOnGestureListener() {
        override fun onDoubleTap(e: MotionEvent): Boolean {
            // 双击缩放到细节级别或恢复到适合屏幕
            val targetScale = if (scaleFactor < DETAIL_ZOOM_SCALE) {
                DETAIL_ZOOM_SCALE
            } else {
                // 恢复到适合屏幕的缩放级别
                calculateFitScreenScale()
            }

            animateZoomTo(targetScale, e.x, e.y)
            return true
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            // 单击填色
            handleTouch(e.x, e.y)
            return true
        }

        override fun onLongPress(e: MotionEvent) {
            // 长按处理：自动关联到该区域的颜色
            handleLongPress(e.x, e.y)
        }
    }

    /**
     * 计算适合屏幕的缩放级别
     */
    private fun calculateFitScreenScale(): Float {
        val bitmap = regionBitmap ?: return 1f
        val scaleX = width.toFloat() / bitmap.width
        val scaleY = height.toFloat() / bitmap.height
        return minOf(scaleX, scaleY)
    }

    /**
     * 计算动态缩放范围 - 根据屏幕适配和用户习惯
     */
    private fun calculateDynamicScaleRange() {
        val fitScreenScale = calculateFitScreenScale()

        // 最小缩放：适合屏幕的0.8倍
        dynamicMinScale = fitScreenScale * MIN_SCALE_RATIO

        // 最大缩放：从原图尺寸最多两次缩放可达到
        // 原图尺寸就是fitScreenScale，两次缩放每次按ZOOM_STEP_FACTOR倍数
        // 使用简单的乘法代替pow函数：2.5^2 = 6.25
        val maxZoomMultiplier = ZOOM_STEP_FACTOR * ZOOM_STEP_FACTOR  // 2次缩放
        dynamicMaxScale = fitScreenScale * maxZoomMultiplier

        android.util.Log.d("ColoringView", "Dynamic scale range: [$dynamicMinScale, $dynamicMaxScale]")
        android.util.Log.d("ColoringView", "Fit screen scale: $fitScreenScale")
        android.util.Log.d("ColoringView", "Zoom steps: $MAX_ZOOM_STEPS x $ZOOM_STEP_FACTOR = $maxZoomMultiplier")
    }

    /**
     * 动画缩放到指定级别
     */
    private fun animateZoomTo(targetScale: Float, focusX: Float, focusY: Float) {
        val startScale = scaleFactor
        val startTranslateX = translateX
        val startTranslateY = translateY

        // 计算目标平移量
        val scaleChange = targetScale / startScale
        val targetTranslateX = focusX + (startTranslateX - focusX) * scaleChange
        val targetTranslateY = focusY + (startTranslateY - focusY) * scaleChange

        // 简单的即时缩放（可以后续改为动画）
        scaleFactor = targetScale.coerceIn(dynamicMinScale, dynamicMaxScale)
        translateX = targetTranslateX
        translateY = targetTranslateY

        constrainTranslation()
        markMatrixDirty()
        updateMatrix()
        invalidate()
    }

    /**
     * 放大
     */
    fun zoomIn() {
        val centerX = width / 2f
        val centerY = height / 2f
        val targetScale = (scaleFactor * ZOOM_STEP_FACTOR).coerceAtMost(dynamicMaxScale)
        animateZoomTo(targetScale, centerX, centerY)
    }

    /**
     * 缩小
     */
    fun zoomOut() {
        val centerX = width / 2f
        val centerY = height / 2f
        val targetScale = (scaleFactor / ZOOM_STEP_FACTOR).coerceAtLeast(dynamicMinScale)
        animateZoomTo(targetScale, centerX, centerY)
    }

    /**
     * 缩放到适合屏幕并居中显示
     */
    fun zoomToFit() {
        val bitmap = regionBitmap ?: return

        val targetScale = calculateFitScreenScale()

        // 计算图片在目标缩放下的尺寸
        val scaledWidth = bitmap.width * targetScale
        val scaledHeight = bitmap.height * targetScale

        // 计算居中的平移量
        val targetTranslateX = (width - scaledWidth) / 2f
        val targetTranslateY = (height - scaledHeight) / 2f

        // 应用缩放和平移
        scaleFactor = targetScale.coerceIn(dynamicMinScale, dynamicMaxScale)
        translateX = targetTranslateX
        translateY = targetTranslateY

        constrainTranslation()
        markMatrixDirty()
        updateMatrix()
        invalidate()

        android.util.Log.d("ColoringView", "ZoomToFit: scale=$scaleFactor, translate=($translateX, $translateY)")
    }

    /**
     * 生成当前画作的截图
     * @param includeOriginal 是否包含原图作为对比
     * @return 生成的Bitmap
     */
    fun captureArtwork(includeOriginal: Boolean = false): Bitmap? {
        val originalBitmap = regionBitmap ?: return null

        if (!includeOriginal) {
            // 只返回填色后的画作
            return createFilledArtwork()
        } else {
            // 返回原图和填色图的对比
            return createComparisonArtwork()
        }
    }

    /**
     * 创建填色后的画作（包含线稿）
     */
    private fun createFilledArtwork(): Bitmap {
        val originalBitmap = regionBitmap!!
        val outline = outlineBitmap!!
        val width = originalBitmap.width
        val height = originalBitmap.height

        // 创建结果bitmap
        val resultBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(resultBitmap)

        // 绘制白色背景
        canvas.drawColor(Color.WHITE)

        // 1. 先绘制线稿轮廓（底层）
        val outlinePaint = Paint(Paint.ANTI_ALIAS_FLAG)
        canvas.drawBitmap(outline, 0f, 0f, outlinePaint)

        // 2. 再绘制填色区域（上层，遮挡线稿）
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        val data = coloringData ?: return resultBitmap

        data.regions.forEach { region ->
            if (filledRegions.contains(region.id)) {
                // 只绘制已填色区域，使用完全不透明的颜色遮挡线稿
                val originalColor = Color.parseColor(region.colorHex)
                val opaqueColor = Color.argb(
                    255, // 完全不透明
                    Color.red(originalColor),
                    Color.green(originalColor),
                    Color.blue(originalColor)
                )

                // 绘制区域像素
                region.pixels.forEach { pixel ->
                    val x = pixel[0]
                    val y = pixel[1]
                    if (x in 0 until width && y in 0 until height) {
                        resultBitmap.setPixel(x, y, opaqueColor)
                    }
                }
            }
            // 未填色区域不绘制，保持线稿可见
        }

        return resultBitmap
    }

    /**
     * 创建对比画作（原图 + 填色图）
     */
    private fun createComparisonArtwork(): Bitmap {
        val originalBitmap = regionBitmap!!
        val filledBitmap = createFilledArtwork()

        val width = originalBitmap.width
        val height = originalBitmap.height

        // 创建对比图（左右布局）
        val comparisonBitmap = Bitmap.createBitmap(width * 2 + 20, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(comparisonBitmap)

        // 绘制白色背景
        canvas.drawColor(Color.WHITE)

        // 绘制原图（左侧）
        canvas.drawBitmap(originalBitmap, 0f, 0f, null)

        // 绘制分隔线
        val linePaint = Paint()
        linePaint.color = Color.GRAY
        linePaint.strokeWidth = 4f
        canvas.drawLine(width + 8f, 0f, width + 12f, height.toFloat(), linePaint)

        // 绘制填色图（右侧）
        canvas.drawBitmap(filledBitmap, (width + 20).toFloat(), 0f, null)

        // 添加标签
        val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        textPaint.color = Color.BLACK
        textPaint.textSize = 24f
        textPaint.textAlign = Paint.Align.CENTER

        canvas.drawText("原图", width / 2f, 30f, textPaint)
        canvas.drawText("填色后", width * 1.5f + 10f, 30f, textPaint)

        return comparisonBitmap
    }

    /**
     * 显示下一个颜色提醒 - 缩放到待涂色区域
     */
    fun showNextColorHint() {
        if (!currentColorHex.isNullOrEmpty() && coloringData != null) {
            // 找到当前颜色的未填充区域
            val normalizedCurrentColor = normalizeColorHex(currentColorHex!!)
            val currentColorRegions = coloringData!!.regions.filter { region ->
                normalizeColorHex(region.colorHex) == normalizedCurrentColor &&
                !filledRegions.contains(region.id)
            }

            if (currentColorRegions.isNotEmpty()) {
                // 选择第一个未填充的区域进行缩放
                val targetRegion = currentColorRegions.first()

                android.util.Log.d("ColoringView", "Zooming to region ${targetRegion.id} for color $normalizedCurrentColor")

                // 缩放到目标区域并放置在屏幕中心
                focusOnRegion(targetRegion)

                // 创建提醒动画效果
                val animator = android.animation.ValueAnimator.ofFloat(0f, 1f)
                animator.duration = 1000
                animator.repeatCount = 2
                animator.repeatMode = android.animation.ValueAnimator.REVERSE

                animator.addUpdateListener { animation ->
                    // 动画期间重绘，突出显示目标区域
                    invalidate()
                }

                animator.start()
            } else {
                android.util.Log.d("ColoringView", "No unfilled regions found for color $normalizedCurrentColor")
            }
        }
    }



    /**
     * 缩放到指定区域
     */
    private fun zoomToRegion(region: Region) {
        if (region.pixels.isNotEmpty()) {
            val bounds = android.graphics.RectF()
            // 计算区域边界
            var minX = Float.MAX_VALUE
            var minY = Float.MAX_VALUE
            var maxX = Float.MIN_VALUE
            var maxY = Float.MIN_VALUE

            region.pixels.forEach { pixel ->
                val x = pixel[0].toFloat()
                val y = pixel[1].toFloat()
                minX = minOf(minX, x)
                minY = minOf(minY, y)
                maxX = maxOf(maxX, x)
                maxY = maxOf(maxY, y)
            }

            bounds.set(minX, minY, maxX, maxY)

            // 计算缩放比例和偏移量
            val scaleX = width * 0.8f / bounds.width()
            val scaleY = height * 0.8f / bounds.height()
            val targetScale = minOf(scaleX, scaleY, 5.0f) // 使用固定的最大缩放值

            val centerX = bounds.centerX()
            val centerY = bounds.centerY()

            // 平滑动画到目标位置
            val animator = android.animation.ValueAnimator.ofFloat(0f, 1f)
            animator.duration = 500

            val startScale = scaleFactor
            val startTransX = translateX
            val startTransY = translateY

            val targetTransX = width / 2f - centerX * targetScale
            val targetTransY = height / 2f - centerY * targetScale

            animator.addUpdateListener { animation ->
                val progress = animation.animatedValue as Float

                scaleFactor = startScale + (targetScale - startScale) * progress
                translateX = startTransX + (targetTransX - startTransX) * progress
                translateY = startTransY + (targetTransY - startTransY) * progress

                invalidate()
            }

            animator.start()
        }
    }


}
