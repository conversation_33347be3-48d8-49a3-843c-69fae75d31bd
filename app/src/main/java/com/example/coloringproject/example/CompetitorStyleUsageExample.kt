//package com.example.coloringproject.example
//
//import android.app.Activity
//import android.os.Bundle
//import android.util.Log
//import android.view.Menu
//import android.view.MenuItem
//import com.example.coloringproject.R
//import com.example.coloringproject.view.ColoringView
//
///**
// * 竞品风格数字显示使用示例
// * 展示如何在Activity中集成和使用新的数字显示功能
// */
//class CompetitorStyleUsageExample : Activity() {
//
//    private lateinit var coloringView: ColoringView
//
//    companion object {
//        private const val TAG = "CompetitorStyleExample"
//    }
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//
//        // 初始化ColoringView
//        initializeColoringView()
//
//        // 设置竞品风格数字显示
//        setupCompetitorStyleNumbers()
//
//        Log.d(TAG, "竞品风格数字显示示例初始化完成")
//    }
//
//    /**
//     * 初始化ColoringView
//     */
//    private fun initializeColoringView() {
//        coloringView = ColoringView(this)
//        setContentView(coloringView)
//
//        // 设置数据加载完成回调
//        coloringView.onDataSetupComplete = {
//            Log.d(TAG, "数据加载完成，启用竞品风格数字显示")
//            coloringView.enableCompetitorStyleNumbers()
//        }
//    }
//
//    /**
//     * 设置竞品风格数字显示
//     */
//    private fun setupCompetitorStyleNumbers() {
//        // 启用竞品风格（显示所有区域的数字）
//        coloringView.enableCompetitorStyleNumbers()
//
//        Log.d(TAG, "当前数字显示模式: ${coloringView.getNumberDisplayMode()}")
//    }
//
//    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
//        // 添加菜单项来切换数字显示模式
//        menu?.add(0, 1, 0, "原有模式（仅选中颜色）")
//        menu?.add(0, 2, 0, "竞品模式（所有区域）")
//        menu?.add(0, 3, 0, "智能混合模式")
//        menu?.add(0, 4, 0, "性能测试")
//        return true
//    }
//
//    override fun onOptionsItemSelected(item: MenuItem): Boolean {
//        when (item.itemId) {
//            1 -> {
//                // 切换到原有模式
//                coloringView.enableOriginalStyleNumbers()
//                Log.d(TAG, "切换到原有模式")
//                showToast("已切换到原有模式（仅显示选中颜色的数字）")
//                return true
//            }
//            2 -> {
//                // 切换到竞品模式
//                coloringView.enableCompetitorStyleNumbers()
//                Log.d(TAG, "切换到竞品模式")
//                showToast("已切换到竞品模式（显示所有区域数字）")
//                return true
//            }
//            3 -> {
//                // 切换到智能混合模式
//                coloringView.enableSmartMixedNumbers()
//                Log.d(TAG, "切换到智能混合模式")
//                showToast("已切换到智能混合模式")
//                return true
//            }
//            4 -> {
//                // 性能测试
//                performanceTest()
//                return true
//            }
//        }
//        return super.onOptionsItemSelected(item)
//    }
//
//    /**
//     * 显示Toast消息
//     */
//    private fun showToast(message: String) {
//        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
//    }
//
//    /**
//     * 性能测试
//     */
//    private fun performanceTest() {
//        Log.d(TAG, "开始性能测试...")
//
//        val startTime = System.currentTimeMillis()
//        var frameCount = 0
//
//        // 模拟连续重绘测试
//        val testRunnable = object : Runnable {
//            override fun run() {
//                if (frameCount < 60) { // 测试60帧
//                    coloringView.invalidate()
//                    frameCount++
//                    coloringView.postDelayed(this, 16) // 约60fps
//                } else {
//                    val endTime = System.currentTimeMillis()
//                    val totalTime = endTime - startTime
//                    val avgFrameTime = totalTime.toFloat() / frameCount
//
//                    Log.d(TAG, "性能测试完成:")
//                    Log.d(TAG, "总时间: ${totalTime}ms")
//                    Log.d(TAG, "帧数: $frameCount")
//                    Log.d(TAG, "平均帧时间: ${avgFrameTime}ms")
//                    Log.d(TAG, "估算FPS: ${1000f / avgFrameTime}")
//
//                    val performanceLevel = when {
//                        avgFrameTime <= 16f -> "优秀 (60+ FPS)"
//                        avgFrameTime <= 33f -> "良好 (30+ FPS)"
//                        avgFrameTime <= 50f -> "一般 (20+ FPS)"
//                        else -> "需要优化 (<20 FPS)"
//                    }
//
//                    showToast("性能测试完成: $performanceLevel")
//                }
//            }
//        }
//
//        coloringView.post(testRunnable)
//    }
//
//    /**
//     * 演示不同缩放级别下的数字显示效果
//     */
//    private fun demonstrateScalingEffects() {
//        Log.d(TAG, "演示缩放效果...")
//
//        val scaleFactors = listOf(0.5f, 1.0f, 2.0f, 3.0f, 5.0f)
//        var currentIndex = 0
//
//        val scaleRunnable = object : Runnable {
//            override fun run() {
//                if (currentIndex < scaleFactors.size) {
//                    val scale = scaleFactors[currentIndex]
//                    Log.d(TAG, "设置缩放级别: ${scale}x")
//
//                    // 这里需要调用ColoringView的缩放方法
//                    // coloringView.setScaleFactor(scale)
//
//                    currentIndex++
//                    coloringView.postDelayed(this, 2000) // 每2秒切换一次
//                } else {
//                    Log.d(TAG, "缩放效果演示完成")
//                    showToast("缩放效果演示完成")
//                }
//            }
//        }
//
//        coloringView.post(scaleRunnable)
//    }
//
//    override fun onResume() {
//        super.onResume()
//        Log.d(TAG, "Activity恢复，当前数字显示模式: ${coloringView.getNumberDisplayMode()}")
//    }
//
//    override fun onPause() {
//        super.onPause()
//        Log.d(TAG, "Activity暂停")
//    }
//
//    override fun onDestroy() {
//        super.onDestroy()
//        Log.d(TAG, "Activity销毁，清理资源")
//    }
//}
//
///**
// * 使用指南
// * ========
// *
// * 1. 基本使用：
// *    coloringView.enableCompetitorStyleNumbers()
// *
// * 2. 切换模式：
// *    coloringView.enableOriginalStyleNumbers()     // 原有模式
// *    coloringView.enableCompetitorStyleNumbers()   // 竞品模式
// *    coloringView.enableSmartMixedNumbers()        // 智能混合
// *
// * 3. 检查当前模式：
// *    val mode = coloringView.getNumberDisplayMode()
// *
// * 4. 性能优化建议：
// *    - 在低端设备上可以限制显示的数字数量
// *    - 交互时会自动降低绘制质量提升流畅度
// *    - 缓存机制会自动优化重复绘制的性能
// *
// * 5. 注意事项：
// *    - 确保在数据加载完成后再切换模式
// *    - 大量数字显示时注意内存使用
// *    - 可以通过菜单或设置让用户选择显示模式
// */
