package com.example.coloringproject.utils

import android.graphics.*
import com.example.coloringproject.data.Region
import kotlin.math.*

/**
 * 数字显示管理器
 * 专注于优化数字显示：大小与区域相关，数值按颜色顺序，缩放级别控制显示
 */
class NumberDisplayManager {
    
    companion object {
        private const val MIN_SCALE_FOR_NUMBERS = 1.2f // 最小缩放级别才显示数字
        private const val MIN_REGION_SIZE_FOR_NUMBER = 25f // 最小区域显示尺寸（缩放后）
        private const val BASE_NUMBER_SIZE = 14f // 基础数字大小
        private const val MAX_NUMBER_SIZE = 28f // 最大数字大小
        private const val MIN_NUMBER_SIZE = 8f // 最小数字大小
    }
    
    // 颜色到数字的映射缓存
    private var colorToNumberMap: Map<String, Int> = emptyMap()
    private var lastProjectColors: List<String> = emptyList()
    
    /**
     * 计算应该显示的数字
     */
    fun calculateNumbersToDisplay(
        regions: List<Region>,
        filledRegions: Set<Int>,
        currentScale: Float,
        selectedColorHex: String?,
        visibleRect: RectF
    ): List<NumberDisplayInfo> {
        
        // 缩放级别太小时不显示数字
        if (currentScale < MIN_SCALE_FOR_NUMBERS) {
            return emptyList()
        }
        
        // 必须有选中的颜色
        if (selectedColorHex == null) {
            return emptyList()
        }
        
        // 更新颜色映射（必须在筛选之前）
        updateColorMapping(regions)
        
        val normalizedSelectedColor = normalizeColorHex(selectedColorHex)
        
        android.util.Log.d("NumberDisplayManager", "=== 开始计算数字显示 ===")
        android.util.Log.d("NumberDisplayManager", "总区域数: ${regions.size}")
        android.util.Log.d("NumberDisplayManager", "已填色区域: ${filledRegions.size}")
        android.util.Log.d("NumberDisplayManager", "选中颜色: $normalizedSelectedColor")
        
        // 筛选匹配的未填色区域
        val candidateRegions = regions.filter { region ->
            val isNotFilled = !filledRegions.contains(region.id)
            val colorMatches = normalizeColorHex(region.colorHex) == normalizedSelectedColor
            val isVisible = isRegionVisible(region, visibleRect)
            
            android.util.Log.d("NumberDisplayManager", "区域${region.id}: 颜色=${normalizeColorHex(region.colorHex)}, 未填色=$isNotFilled, 颜色匹配=$colorMatches, 可见=$isVisible")
            
            isNotFilled && colorMatches && isVisible
        }
        
        android.util.Log.d("NumberDisplayManager", "筛选后候选区域数量: ${candidateRegions.size}")
        
        // 计算每个区域的显示信息
        val numbersToDisplay = mutableListOf<NumberDisplayInfo>()
        
        candidateRegions.forEach { region ->
            val displaySize = calculateRegionDisplaySize(region, currentScale)
            
            // 只有足够大的区域才显示数字
            if (displaySize >= MIN_REGION_SIZE_FOR_NUMBER) {
                val center = calculateRegionCenter(region)
                val numberSize = calculateNumberSize(displaySize)
                val colorNumber = getColorNumber(region.colorHex)
                
                numbersToDisplay.add(
                    NumberDisplayInfo(
                        regionId = region.id,
                        centerX = center.x,
                        centerY = center.y,
                        number = colorNumber,
                        textSize = numberSize,
                        displaySize = displaySize
                    )
                )
            }
        }
        
        // 按区域大小排序，大区域优先显示
        return numbersToDisplay.sortedByDescending { it.displaySize }
    }
    
    /**
     * 更新颜色到数字的映射
     */
    private fun updateColorMapping(regions: List<Region>) {
        // 获取所有唯一颜色并排序
        val uniqueColors = regions.map { normalizeColorHex(it.colorHex) }
            .distinct()
            .sorted() // 按颜色值排序，确保一致性
        
        // 如果颜色列表没有变化，不需要重新映射
        if (uniqueColors == lastProjectColors) {
            return
        }
        
        // 创建新的颜色到数字映射
        colorToNumberMap = uniqueColors.mapIndexed { index, color ->
            color to (index + 1) // 从1开始编号
        }.toMap()
        
        lastProjectColors = uniqueColors
        
        android.util.Log.d("NumberDisplayManager", "更新颜色映射: ${colorToNumberMap.size}种颜色")
        android.util.Log.d("NumberDisplayManager", "颜色映射详情: $colorToNumberMap")
    }
    
    /**
     * 获取颜色对应的数字
     */
    private fun getColorNumber(colorHex: String): Int {
        val normalizedColor = normalizeColorHex(colorHex)
        val number = colorToNumberMap[normalizedColor] ?: 1
        
        android.util.Log.d("NumberDisplayManager", "颜色 $normalizedColor -> 数字 $number")
        
        return number
    }
    
    /**
     * 计算区域的显示大小（考虑缩放）
     */
    private fun calculateRegionDisplaySize(region: Region, scale: Float): Float {
        val bounds = getRegionBounds(region)
        
        // 使用较小的边长作为显示大小，并应用缩放
        val rawSize = min(bounds.width(), bounds.height())
        val displaySize = rawSize * scale
        
        android.util.Log.d("NumberDisplayManager", "区域${region.id}: 原始大小=${rawSize.toInt()}px, 显示大小=${displaySize.toInt()}px")
        
        return displaySize
    }
    
    /**
     * 根据显示大小计算数字大小
     */
    private fun calculateNumberSize(displaySize: Float): Float {
        // 数字大小与区域显示大小成正比
        val ratio = displaySize / 100f // 100像素对应基础大小
        val calculatedSize = BASE_NUMBER_SIZE * sqrt(ratio) // 使用平方根避免过大
        val finalSize = calculatedSize.coerceIn(MIN_NUMBER_SIZE, MAX_NUMBER_SIZE)
        
        android.util.Log.d("NumberDisplayManager", "显示大小${displaySize.toInt()}px -> 数字大小${finalSize.toInt()}px")
        
        return finalSize
    }
    
    /**
     * 计算区域中心点
     */
    private fun calculateRegionCenter(region: Region): PointF {
        if (region.pixels.isEmpty()) return PointF(0f, 0f)
        
        // 优先使用边界框中心（性能更好）
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                return PointF(
                    (bbox[0] + bbox[2]) / 2f,
                    (bbox[1] + bbox[3]) / 2f
                )
            }
        }
        
        // 备用方案：计算像素平均位置
        var sumX = 0f
        var sumY = 0f
        
        region.pixels.forEach { pixel ->
            sumX += pixel[0]
            sumY += pixel[1]
        }
        
        return PointF(
            sumX / region.pixels.size,
            sumY / region.pixels.size
        )
    }
    
    /**
     * 获取区域边界
     */
    private fun getRegionBounds(region: Region): RectF {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                return RectF(
                    bbox[0].toFloat(),
                    bbox[1].toFloat(),
                    bbox[2].toFloat(),
                    bbox[3].toFloat()
                )
            }
        }
        
        // 从像素计算边界
        if (region.pixels.isEmpty()) return RectF()
        
        var minX = Int.MAX_VALUE
        var minY = Int.MAX_VALUE
        var maxX = Int.MIN_VALUE
        var maxY = Int.MIN_VALUE
        
        region.pixels.forEach { pixel ->
            minX = min(minX, pixel[0])
            minY = min(minY, pixel[1])
            maxX = max(maxX, pixel[0])
            maxY = max(maxY, pixel[1])
        }
        
        return RectF(minX.toFloat(), minY.toFloat(), maxX.toFloat(), maxY.toFloat())
    }
    
    /**
     * 检查区域是否在可见范围内
     */
    private fun isRegionVisible(region: Region, visibleRect: RectF): Boolean {
        val regionBounds = getRegionBounds(region)
        return RectF.intersects(regionBounds, visibleRect)
    }
    
    /**
     * 标准化颜色格式
     */
    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        if (normalized.length == 4) {
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }
    
    /**
     * 绘制数字
     */
    fun drawNumbers(canvas: Canvas, numbers: List<NumberDisplayInfo>) {
        val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }
        
        val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.WHITE
            alpha = 230
        }
        
        val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.GRAY
            style = Paint.Style.STROKE
            strokeWidth = 1f
        }
        
        numbers.forEach { numberInfo ->
            textPaint.textSize = numberInfo.textSize
            
            val numberText = numberInfo.number.toString()
            val textBounds = Rect()
            textPaint.getTextBounds(numberText, 0, numberText.length, textBounds)
            
            // 计算背景圆圈大小
            val radius = max(textBounds.width(), textBounds.height()) / 2f + 4f
            
            // 绘制背景圆圈
            canvas.drawCircle(numberInfo.centerX, numberInfo.centerY, radius, backgroundPaint)
            
            // 绘制边框
            canvas.drawCircle(numberInfo.centerX, numberInfo.centerY, radius, borderPaint)
            
            // 绘制数字
            canvas.drawText(
                numberText,
                numberInfo.centerX,
                numberInfo.centerY + textBounds.height() / 2f,
                textPaint
            )
        }
    }
}

/**
 * 数字显示信息
 */
data class NumberDisplayInfo(
    val regionId: Int,
    val centerX: Float,
    val centerY: Float,
    val number: Int,
    val textSize: Float,
    val displaySize: Float
)