package com.example.coloringproject.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import java.io.InputStream

/**
 * Bitmap工具类 - 模块1：内存优化
 * 提供内存友好的Bitmap加载方法
 */
object BitmapUtils {
    
    private const val TAG = "BitmapUtils"
    private const val MAX_BITMAP_SIZE = 2048 // 最大尺寸限制
    
    /**
     * 内存友好的Bitmap解码
     * 自动计算合适的采样率，减少内存使用
     */
    fun decodeSampledBitmap(
        inputStream: InputStream,
        reqWidth: Int = MAX_BITMAP_SIZE,
        reqHeight: Int = MAX_BITMAP_SIZE
    ): Bitmap? {
        return try {
            Log.d(TAG, "开始解码Bitmap，目标尺寸: ${reqWidth}x${reqHeight}")

            // 首先读取图片尺寸信息，不加载到内存
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }

            // 检查InputStream是否支持mark/reset
            if (!inputStream.markSupported()) {
                Log.w(TAG, "InputStream不支持mark/reset，使用直接解码")
                // 直接解码，不进行尺寸预检查
                val directOptions = BitmapFactory.Options().apply {
                    inPreferredConfig = Bitmap.Config.RGB_565
                    inDither = false
                    inSampleSize = 2 // 使用默认的2倍采样
                }
                return BitmapFactory.decodeStream(inputStream, null, directOptions)
            }

            // 标记当前位置
            val markLimit = inputStream.available()
            Log.d(TAG, "InputStream可用字节数: $markLimit")
            inputStream.mark(markLimit)

            // 读取尺寸信息
            BitmapFactory.decodeStream(inputStream, null, options)
            Log.d(TAG, "图片原始尺寸: ${options.outWidth}x${options.outHeight}")

            // 检查是否成功读取到尺寸信息
            if (options.outWidth <= 0 || options.outHeight <= 0) {
                Log.e(TAG, "无法读取图片尺寸信息，可能文件损坏")
                return null
            }

            // 重置到标记位置
            inputStream.reset()

            // 计算采样率
            options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)
            options.inJustDecodeBounds = false

            // 启用内存优化选项
            options.inPreferredConfig = Bitmap.Config.RGB_565 // 使用RGB_565减少内存
            options.inDither = false
            options.inPurgeable = true
            options.inInputShareable = true

            Log.d(TAG, "使用采样率: ${options.inSampleSize}")
            val bitmap = BitmapFactory.decodeStream(inputStream, null, options)

            if (bitmap != null) {
                val originalSize = options.outWidth * options.outHeight * 4 // 假设ARGB_8888
                val actualSize = bitmap.byteCount
                val compressionRatio = (originalSize - actualSize) * 100f / originalSize

                Log.d(TAG, "Bitmap加载成功: ${options.outWidth}x${options.outHeight} -> ${bitmap.width}x${bitmap.height}")
                Log.d(TAG, "内存优化: ${originalSize / 1024}KB -> ${actualSize / 1024}KB (节省${compressionRatio.toInt()}%)")
            } else {
                Log.e(TAG, "BitmapFactory.decodeStream返回null")
            }

            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "Bitmap解码失败", e)
            null
        }
    }
    
    /**
     * 计算合适的采样率
     * 确保图片尺寸不超过要求的最大尺寸
     */
    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val (height: Int, width: Int) = options.run { outHeight to outWidth }
        var inSampleSize = 1
        
        if (height > reqHeight || width > reqWidth) {
            val halfHeight: Int = height / 2
            val halfWidth: Int = width / 2
            
            // 计算最大的inSampleSize值，确保最终图片尺寸大于等于要求的尺寸
            while (halfHeight / inSampleSize >= reqHeight && 
                   halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        Log.d(TAG, "原始尺寸: ${width}x${height}, 目标尺寸: ${reqWidth}x${reqHeight}, 采样率: $inSampleSize")
        return inSampleSize
    }
    
    /**
     * 为缩略图优化的Bitmap加载
     * 使用更小的尺寸和更高的压缩率
     */
    fun decodeThumbnailBitmap(
        inputStream: InputStream,
        thumbnailSize: Int = 256
    ): Bitmap? {
        return decodeSampledBitmap(
            inputStream = inputStream,
            reqWidth = thumbnailSize,
            reqHeight = thumbnailSize
        )
    }
    
    /**
     * 为涂色页面优化的Bitmap加载
     * 使用适中的尺寸，平衡质量和性能
     */
    fun decodeColoringBitmap(
        inputStream: InputStream,
        maxSize: Int = 1024
    ): Bitmap? {
        return decodeSampledBitmap(
            inputStream = inputStream,
            reqWidth = maxSize,
            reqHeight = maxSize
        )
    }
    
    /**
     * 检查Bitmap是否过大
     * 用于内存使用监控
     */
    fun isBitmapTooLarge(bitmap: Bitmap?, maxSizeBytes: Int = 4 * 1024 * 1024): Boolean {
        return bitmap?.byteCount ?: 0 > maxSizeBytes
    }
    
    /**
     * 安全地回收Bitmap
     */
    fun recycleBitmap(bitmap: Bitmap?) {
        try {
            if (bitmap != null && !bitmap.isRecycled) {
                bitmap.recycle()
                Log.d(TAG, "Bitmap已回收")
            }
        } catch (e: Exception) {
            Log.w(TAG, "Bitmap回收失败", e)
        }
    }
}
