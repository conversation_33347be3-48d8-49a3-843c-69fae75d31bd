package com.example.coloringproject.utils

import android.util.Log

/**
 * 性能监控工具类
 * 用于监控涂色页面初始化和关键操作的性能
 */
object PerformanceMonitor {
    
    private const val TAG = "PerformanceMonitor"
    private val timingMap = mutableMapOf<String, Long>()
    private val phaseTimings = mutableMapOf<String, MutableList<Pair<String, Long>>>()
    
    /**
     * 开始计时
     */
    fun startTiming(operation: String) {
        timingMap[operation] = System.currentTimeMillis()
        Log.d(TAG, "⏱️ 开始计时: $operation")
    }
    
    /**
     * 结束计时并记录
     */
    fun endTiming(operation: String): Long {
        val startTime = timingMap[operation]
        if (startTime == null) {
            Log.w(TAG, "⚠️ 未找到开始时间: $operation")
            return 0L
        }
        
        val duration = System.currentTimeMillis() - startTime
        timingMap.remove(operation)
        
        Log.d(TAG, "✅ 完成计时: $operation - ${duration}ms")
        return duration
    }
    
    /**
     * 记录阶段性能
     */
    fun recordPhase(category: String, phase: String, duration: Long) {
        phaseTimings.getOrPut(category) { mutableListOf() }.add(phase to duration)
        Log.d(TAG, "📊 阶段记录: $category.$phase - ${duration}ms")
    }
    
    /**
     * 生成性能报告
     */
    fun generateReport(category: String): String {
        val phases = phaseTimings[category] ?: return "无数据"
        
        val report = StringBuilder()
        report.appendLine("=== $category 性能报告 ===")
        
        var totalTime = 0L
        phases.forEach { (phase, duration) ->
            report.appendLine("$phase: ${duration}ms")
            totalTime += duration
        }
        
        report.appendLine("总计: ${totalTime}ms")
        report.appendLine("平均: ${totalTime / phases.size}ms")
        
        // 找出最慢的阶段
        val slowestPhase = phases.maxByOrNull { it.second }
        if (slowestPhase != null) {
            report.appendLine("最慢阶段: ${slowestPhase.first} (${slowestPhase.second}ms)")
        }
        
        return report.toString()
    }
    
    /**
     * 清除记录
     */
    fun clearRecords(category: String? = null) {
        if (category != null) {
            phaseTimings.remove(category)
        } else {
            phaseTimings.clear()
            timingMap.clear()
        }
    }
    
    /**
     * 监控内存使用情况
     */
    fun logMemoryUsage(operation: String) {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val usagePercent = (usedMemory.toFloat() / maxMemory * 100).toInt()
        
        Log.d(TAG, "🧠 内存使用 [$operation]: ${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB ($usagePercent%)")
    }
    
    /**
     * 便捷的计时执行方法
     */
    inline fun <T> measureTime(operation: String, block: () -> T): T {
        startTiming(operation)
        return try {
            block()
        } finally {
            endTiming(operation)
        }
    }
    
    /**
     * 便捷的阶段计时方法
     */
    inline fun <T> measurePhase(category: String, phase: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        return try {
            block()
        } finally {
            val duration = System.currentTimeMillis() - startTime
            recordPhase(category, phase, duration)
        }
    }
}