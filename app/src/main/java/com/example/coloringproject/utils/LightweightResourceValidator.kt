package com.example.coloringproject.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 轻量级资源验证器
 * 只进行图片和JSON的匹配校验，不加载实际数据
 * 用于Library页面的快速验证和显示
 */
class LightweightResourceValidator(private val context: Context) {
    
    companion object {
        private const val TAG = "LightweightResourceValidator"
    }
    
    /**
     * 轻量级项目信息
     * 只包含验证和显示所需的基本信息
     */
    data class LightweightProject(
        val id: String,
        val displayName: String,
        val description: String? = null,
        val category: String? = null,
        val difficulty: String = "unknown",
        val resourceSource: HybridResourceManager.Companion.ResourceSource,
        val jsonPath: String,
        val outlinePath: String,
        val isValid: Boolean,
        val validationErrors: List<String> = emptyList(),
        val estimatedFileSize: Long = 0L,
        val thumbnailPath: String? = null
    )
    
    /**
     * 验证结果
     */
    data class ValidationResult(
        val isValid: Boolean,
        val errors: List<String> = emptyList(),
        val estimatedSize: Long = 0L
    )
    
    /**
     * 快速验证项目资源是否存在和匹配
     * 不加载实际数据，只检查文件存在性和基本格式
     */
    suspend fun validateProjectResources(
        projectId: String,
        resourceSource: HybridResourceManager.Companion.ResourceSource
    ): ValidationResult = withContext(Dispatchers.IO) {
        val errors = mutableListOf<String>()
        var estimatedSize = 0L
        
        try {
            when (resourceSource) {
                HybridResourceManager.Companion.ResourceSource.BUILT_IN -> {
                    // 验证Assets中的资源
                    val jsonPath = "$projectId.json"
                    val outlinePath = "$projectId.png"
                    
                    // 检查JSON文件
                    try {
                        val jsonStream = context.assets.open(jsonPath)
                        estimatedSize += jsonStream.available()
                        jsonStream.close()
                    } catch (e: Exception) {
                        errors.add("JSON文件不存在: $jsonPath")
                    }
                    
                    // 检查PNG文件
                    try {
                        val pngStream = context.assets.open(outlinePath)
                        estimatedSize += pngStream.available()
                        pngStream.close()
                    } catch (e: Exception) {
                        errors.add("PNG文件不存在: $outlinePath")
                    }
                }
                
                HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED -> {
                    // 验证下载目录中的资源
                    val downloadDir = File(context.filesDir, "downloads")
                    val jsonFile = File(downloadDir, "$projectId.json")
                    val outlineFile = File(downloadDir, "$projectId.png")
                    
                    if (!jsonFile.exists()) {
                        errors.add("下载的JSON文件不存在")
                    } else {
                        estimatedSize += jsonFile.length()
                    }
                    
                    if (!outlineFile.exists()) {
                        errors.add("下载的PNG文件不存在")
                    } else {
                        estimatedSize += outlineFile.length()
                    }
                }
                
                HybridResourceManager.Companion.ResourceSource.DOWNLOADED -> {
                    // 验证缓存中的资源
                    val cacheDir = File(context.cacheDir, "projects/$projectId")
                    val jsonFile = File(cacheDir, "data.json")
                    val outlineFile = File(cacheDir, "outline.png")
                    
                    if (!jsonFile.exists()) {
                        errors.add("缓存的JSON文件不存在")
                    } else {
                        estimatedSize += jsonFile.length()
                    }
                    
                    if (!outlineFile.exists()) {
                        errors.add("缓存的PNG文件不存在")
                    } else {
                        estimatedSize += outlineFile.length()
                    }
                }
                
                else -> {
                    errors.add("不支持的资源来源: $resourceSource")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "验证项目资源时出错: $projectId", e)
            errors.add("验证过程出错: ${e.message}")
        }
        
        ValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            estimatedSize = estimatedSize
        )
    }
    
    /**
     * 获取轻量级项目列表
     * 只进行基本验证，不加载数据
     */
    suspend fun getLightweightProjects(
        includeBuiltIn: Boolean = true,
        includeDownloaded: Boolean = true,
        includeCached: Boolean = false
    ): Result<List<LightweightProject>> = withContext(Dispatchers.IO) {
        try {
            val projects = mutableListOf<LightweightProject>()
            
            // 获取内置项目
            if (includeBuiltIn) {
                projects.addAll(getBuiltInProjects())
            }
            
            // 获取已下载项目
            if (includeDownloaded) {
                projects.addAll(getDownloadedProjects())
            }
            
            // 获取缓存项目
            if (includeCached) {
                projects.addAll(getCachedProjects())
            }
            
            Result.success(projects)
        } catch (e: Exception) {
            Log.e(TAG, "获取轻量级项目列表失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取内置项目列表
     */
    private suspend fun getBuiltInProjects(): List<LightweightProject> = withContext(Dispatchers.IO) {
        val projects = mutableListOf<LightweightProject>()
        
        try {
            // 扫描assets目录中的JSON文件
            val assetFiles = context.assets.list("") ?: emptyArray()
            val jsonFiles = assetFiles.filter { it.endsWith(".json") }
            
            for (jsonFile in jsonFiles) {
                val projectId = jsonFile.removeSuffix(".json")
                val validation = validateProjectResources(
                    projectId, 
                    HybridResourceManager.Companion.ResourceSource.BUILT_IN
                )
                
                projects.add(
                    LightweightProject(
                        id = projectId,
                        displayName = formatDisplayName(projectId),
                        description = "内置填色项目",
                        category = extractCategory(projectId),
                        difficulty = extractDifficulty(projectId),
                        resourceSource = HybridResourceManager.Companion.ResourceSource.BUILT_IN,
                        jsonPath = jsonFile,
                        outlinePath = "$projectId.png",
                        isValid = validation.isValid,
                        validationErrors = validation.errors,
                        estimatedFileSize = validation.estimatedSize
                    )
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "扫描内置项目失败", e)
        }
        
        projects
    }
    
    /**
     * 获取已下载项目列表
     */
    private suspend fun getDownloadedProjects(): List<LightweightProject> = withContext(Dispatchers.IO) {
        val projects = mutableListOf<LightweightProject>()
        
        try {
            val downloadDir = File(context.filesDir, "downloads")
            if (downloadDir.exists()) {
                val jsonFiles = downloadDir.listFiles { file -> 
                    file.isFile && file.name.endsWith(".json") 
                } ?: emptyArray()
                
                for (jsonFile in jsonFiles) {
                    val projectId = jsonFile.nameWithoutExtension
                    val validation = validateProjectResources(
                        projectId, 
                        HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED
                    )
                    
                    projects.add(
                        LightweightProject(
                            id = projectId,
                            displayName = formatDisplayName(projectId),
                            description = "已下载项目",
                            category = extractCategory(projectId),
                            difficulty = extractDifficulty(projectId),
                            resourceSource = HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED,
                            jsonPath = jsonFile.absolutePath,
                            outlinePath = File(downloadDir, "$projectId.png").absolutePath,
                            isValid = validation.isValid,
                            validationErrors = validation.errors,
                            estimatedFileSize = validation.estimatedSize
                        )
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "扫描下载项目失败", e)
        }
        
        projects
    }
    
    /**
     * 获取缓存项目列表
     */
    private suspend fun getCachedProjects(): List<LightweightProject> = withContext(Dispatchers.IO) {
        val projects = mutableListOf<LightweightProject>()
        
        try {
            val cacheDir = File(context.cacheDir, "projects")
            if (cacheDir.exists()) {
                val projectDirs = cacheDir.listFiles { file -> file.isDirectory } ?: emptyArray()
                
                for (projectDir in projectDirs) {
                    val projectId = projectDir.name
                    val validation = validateProjectResources(
                        projectId, 
                        HybridResourceManager.Companion.ResourceSource.DOWNLOADED
                    )
                    
                    projects.add(
                        LightweightProject(
                            id = projectId,
                            displayName = formatDisplayName(projectId),
                            description = "缓存项目",
                            category = extractCategory(projectId),
                            difficulty = extractDifficulty(projectId),
                            resourceSource = HybridResourceManager.Companion.ResourceSource.DOWNLOADED,
                            jsonPath = File(projectDir, "data.json").absolutePath,
                            outlinePath = File(projectDir, "outline.png").absolutePath,
                            isValid = validation.isValid,
                            validationErrors = validation.errors,
                            estimatedFileSize = validation.estimatedSize
                        )
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "扫描缓存项目失败", e)
        }
        
        projects
    }
    
    /**
     * 格式化显示名称
     */
    private fun formatDisplayName(projectId: String): String {
        return projectId.replace("-", " ").replace("_", " ")
            .split(" ")
            .joinToString(" ") { word ->
                word.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
            }
    }
    
    /**
     * 从项目ID提取分类
     */
    private fun extractCategory(projectId: String): String {
        return when {
            projectId.startsWith("Animals") -> "animals"
            projectId.startsWith("flower") -> "flowers"
            projectId.startsWith("landscape") -> "landscapes"
            projectId.startsWith("character") -> "characters"
            else -> "other"
        }
    }
    
    /**
     * 从项目ID提取难度
     */
    private fun extractDifficulty(projectId: String): String {
        return when {
            projectId.contains("easy") || projectId.endsWith("1") -> "easy"
            projectId.contains("hard") || projectId.endsWith("3") -> "hard"
            else -> "medium"
        }
    }
}
