package com.example.coloringproject.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

/**
 * 智能预加载管理器 - 模块3：差异化预加载策略
 * 基于竞品设计：Library激进预加载，Gallery智能对话框
 */
class SmartPreloadManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "SmartPreloadManager"

        @Volatile
        private var INSTANCE: SmartPreloadManager? = null

        fun getInstance(context: Context): SmartPreloadManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SmartPreloadManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    // 动态内存管理器
    private val deviceMemoryManager = DeviceMemoryManager.getInstance(context)
    
    // 预加载状态管理
    private val preloadJobs = ConcurrentHashMap<String, Job>()
    private val preloadStatus = ConcurrentHashMap<String, PreloadStatus>()
    private val preloadPriority = ConcurrentHashMap<String, PreloadPriority>()
    
    // 预加载统计
    private val totalPreloadTasks = AtomicInteger(0)
    private val completedPreloadTasks = AtomicInteger(0)
    
    // 协程作用域
    private val preloadScope = CoroutineScope(
        Dispatchers.IO + SupervisorJob() + 
        CoroutineName("SmartPreloadManager")
    )
    
    /**
     * 预加载状态枚举
     */
    enum class PreloadStatus {
        NOT_STARTED,    // 未开始
        IN_PROGRESS,    // 进行中
        COMPLETED,      // 已完成
        FAILED,         // 失败
        CANCELLED       // 已取消
    }
    
    /**
     * 预加载优先级
     */
    enum class PreloadPriority(val weight: Int) {
        LOW(1),         // 低优先级
        MEDIUM(2),      // 中优先级  
        HIGH(3),        // 高优先级
        CRITICAL(4)     // 关键优先级
    }
    
    /**
     * Library页面激进预加载策略 - 动态内存管理
     * 根据设备性能动态调整预加载数量
     */
    fun preloadLibraryProjects(
        category: String,
        projectIds: List<String>,
        maxCount: Int = -1 // -1表示使用动态配置
    ) {
        // 使用动态内存管理获取推荐的预加载数量
        val recommendedCount = if (maxCount > 0) {
            maxCount
        } else {
            deviceMemoryManager.getRecommendedPreloadCount()
        }

        val memoryConfig = deviceMemoryManager.getMemoryConfig()
        Log.d(TAG, "开始Library动态预加载: $category")
        Log.d(TAG, "设备等级: ${memoryConfig.deviceTier.description}")
        Log.d(TAG, "项目数: ${projectIds.size}, 推荐预加载: $recommendedCount")

        val prioritizedProjects = projectIds.take(recommendedCount)
        
        prioritizedProjects.forEachIndexed { index, projectId ->
            val priority = when (index) {
                0 -> PreloadPriority.CRITICAL        // 仅第1个项目关键优先级
                1, 2 -> PreloadPriority.HIGH         // 2-3个项目高优先级
                else -> PreloadPriority.MEDIUM       // 其他中优先级
            }
            
            preloadProjectComplete(projectId, priority)
        }
    }
    
    /**
     * Gallery页面智能预加载策略
     * 仅预加载缩略图和基本信息
     */
    fun preloadGalleryProjects(projectIds: List<String>) {
        Log.d(TAG, "开始Gallery智能预加载: 项目数: ${projectIds.size}")
        
        projectIds.forEach { projectId ->
            preloadProjectBasic(projectId, PreloadPriority.LOW)
        }
    }
    
    /**
     * 预加载项目完整数据（JSON + Outline图片 + Region图片）
     */
    fun preloadProjectComplete(projectId: String, priority: PreloadPriority = PreloadPriority.MEDIUM) {
        if (isPreloadInProgress(projectId)) {
            Log.d(TAG, "项目 $projectId 已在预加载中，跳过")
            return
        }

        // 紧急修复：检查内存压力
        if (isMemoryPressureHigh()) {
            Log.w(TAG, "内存压力过高，跳过项目 $projectId 的预加载")
            return
        }
        
        Log.d(TAG, "开始完整预加载项目: $projectId, 优先级: $priority")
        
        preloadStatus[projectId] = PreloadStatus.IN_PROGRESS
        preloadPriority[projectId] = priority
        totalPreloadTasks.incrementAndGet()
        
        val job = preloadScope.launch {
            try {
                val startTime = System.currentTimeMillis()
                
                // 1. 预加载JSON数据
                val enhancedAssetManager = EnhancedAssetManager(context)
                val jsonResult = enhancedAssetManager.loadColoringData("$projectId.json")
                
                if (jsonResult.isFailure) {
                    throw jsonResult.exceptionOrNull() ?: Exception("JSON加载失败")
                }
                
                // 2. 预加载Outline图片
                val outlineResult = enhancedAssetManager.loadOutlineBitmap("$projectId.png")
                
                if (outlineResult.isFailure) {
                    throw outlineResult.exceptionOrNull() ?: Exception("Outline图片加载失败")
                }
                
                // 3. 预加载Region图片（如果存在）
                try {
                    enhancedAssetManager.loadRegionBitmap("${projectId}_regions.png")
                    Log.d(TAG, "项目 $projectId region图片预加载成功")
                } catch (e: Exception) {
                    // 大多数项目没有region图片，这是正常的
                    Log.v(TAG, "项目 $projectId 没有region图片，跳过")
                }
                
                val loadTime = System.currentTimeMillis() - startTime
                Log.d(TAG, "✅ 项目 $projectId 完整预加载成功，耗时: ${loadTime}ms")
                
                preloadStatus[projectId] = PreloadStatus.COMPLETED
                completedPreloadTasks.incrementAndGet()
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ 项目 $projectId 预加载失败", e)
                preloadStatus[projectId] = PreloadStatus.FAILED
            }
        }
        
        preloadJobs[projectId] = job
    }
    
    /**
     * 预加载项目基本数据（仅缩略图）
     */
    fun preloadProjectBasic(projectId: String, priority: PreloadPriority = PreloadPriority.LOW) {
        if (isPreloadInProgress(projectId)) {
            return
        }
        
        Log.d(TAG, "开始基本预加载项目: $projectId")
        
        preloadStatus[projectId] = PreloadStatus.IN_PROGRESS
        preloadPriority[projectId] = priority
        
        val job = preloadScope.launch {
            try {
                // 仅预加载缩略图
                val enhancedAssetManager = EnhancedAssetManager(context)
                val result = enhancedAssetManager.loadOutlineBitmap("$projectId.png")
                
                if (result.isSuccess) {
                    Log.d(TAG, "✅ 项目 $projectId 基本预加载成功")
                    preloadStatus[projectId] = PreloadStatus.COMPLETED
                } else {
                    throw result.exceptionOrNull() ?: Exception("缩略图加载失败")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ 项目 $projectId 基本预加载失败", e)
                preloadStatus[projectId] = PreloadStatus.FAILED
            }
        }
        
        preloadJobs[projectId] = job
    }
    
    /**
     * 检查项目是否已预加载完成
     */
    fun isProjectPreloaded(projectId: String): Boolean {
        return preloadStatus[projectId] == PreloadStatus.COMPLETED
    }
    
    /**
     * 检查项目是否正在预加载
     */
    fun isPreloadInProgress(projectId: String): Boolean {
        return preloadStatus[projectId] == PreloadStatus.IN_PROGRESS
    }
    
    /**
     * 取消项目预加载
     */
    fun cancelPreload(projectId: String) {
        preloadJobs[projectId]?.cancel()
        preloadJobs.remove(projectId)
        preloadStatus[projectId] = PreloadStatus.CANCELLED
        Log.d(TAG, "取消项目预加载: $projectId")
    }
    
    /**
     * 取消所有预加载任务
     */
    fun cancelAllPreloads() {
        Log.d(TAG, "取消所有预加载任务")
        preloadJobs.values.forEach { it.cancel() }
        preloadJobs.clear()
        preloadStatus.clear()
        preloadPriority.clear()
        totalPreloadTasks.set(0)
        completedPreloadTasks.set(0)
    }
    
    /**
     * 获取预加载统计信息
     */
    fun getPreloadStats(): PreloadStats {
        return PreloadStats(
            totalTasks = totalPreloadTasks.get(),
            completedTasks = completedPreloadTasks.get(),
            inProgressTasks = preloadStatus.values.count { it == PreloadStatus.IN_PROGRESS },
            failedTasks = preloadStatus.values.count { it == PreloadStatus.FAILED }
        )
    }
    
    /**
     * 预加载统计数据类
     */
    data class PreloadStats(
        val totalTasks: Int,
        val completedTasks: Int,
        val inProgressTasks: Int,
        val failedTasks: Int
    ) {
        val completionRate: Float
            get() = if (totalTasks > 0) completedTasks.toFloat() / totalTasks else 0f
    }
    
    /**
     * 检查内存压力 - 使用动态内存管理
     */
    private fun isMemoryPressureHigh(): Boolean {
        return try {
            val memoryUsage = deviceMemoryManager.getCurrentMemoryUsage()
            val memoryConfig = deviceMemoryManager.getMemoryConfig()

            // 根据设备等级调整压力阈值
            val pressureThreshold = when (memoryConfig.deviceTier) {
                DeviceMemoryManager.DeviceTier.HIGH_END -> 0.75f   // 高端设备75%
                DeviceMemoryManager.DeviceTier.MID_RANGE -> 0.70f  // 中端设备70%
                DeviceMemoryManager.DeviceTier.LOW_END -> 0.60f    // 低端设备60%
            }

            val usageRatio = memoryUsage.usagePercentage / 100f
            val isHighPressure = usageRatio > pressureThreshold

            if (isHighPressure) {
                Log.w(TAG, "内存压力检测: ${memoryConfig.deviceTier.description}")
                Log.w(TAG, "使用率${memoryUsage.usagePercentage}% > 阈值${(pressureThreshold * 100).toInt()}%")
                Log.w(TAG, "内存: ${memoryUsage.usedMemory / 1024 / 1024}MB/${memoryUsage.maxMemory / 1024 / 1024}MB")
            }

            isHighPressure
        } catch (e: Exception) {
            Log.e(TAG, "内存压力检测失败", e)
            true // 检测失败时保守处理，认为压力高
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        Log.d(TAG, "清理SmartPreloadManager资源")
        cancelAllPreloads()
        preloadScope.cancel()
    }
}
