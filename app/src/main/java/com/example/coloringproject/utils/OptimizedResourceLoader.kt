package com.example.coloringproject.utils

import android.content.Context
import android.util.Log
import com.example.coloringproject.data.ColoringData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap

/**
 * 优化的资源加载器
 * 实现图片优先加载，JSON延迟加载的策略
 */
class OptimizedResourceLoader(private val context: Context) {
    
    private val hybridResourceManager = HybridResourceManager(context)
    private val thumbnailManager = ThumbnailManager(context)
    
    // JSON数据缓存，避免重复加载
    private val jsonDataCache = ConcurrentHashMap<String, ColoringData>()
    
    // 加载状态跟踪
    private val loadingStates = ConcurrentHashMap<String, LoadingState>()
    
    companion object {
        private const val TAG = "OptimizedResourceLoader"
    }
    
    /**
     * 加载状态
     */
    enum class LoadingState {
        IDLE,           // 空闲
        LOADING_THUMBNAIL,  // 加载缩略图中
        THUMBNAIL_LOADED,   // 缩略图已加载
        LOADING_DATA,       // 加载数据中
        DATA_LOADED,        // 数据已加载
        ERROR               // 错误
    }
    
    /**
     * 资源加载结果
     */
    sealed class ResourceLoadResult {
        data class ThumbnailReady(
            val projectId: String,
            val thumbnailPath: String
        ) : ResourceLoadResult()
        
        data class DataReady(
            val projectId: String,
            val coloringData: ColoringData,
            val outlinePath: String?
        ) : ResourceLoadResult()
        
        data class Error(
            val projectId: String,
            val message: String,
            val throwable: Throwable?
        ) : ResourceLoadResult()
    }
    
    /**
     * 优先加载项目缩略图 - 优化版本
     * 用于列表显示，快速展示项目预览
     */
    suspend fun loadThumbnailFirst(
        project: HybridResourceManager.HybridProject,
        onThumbnailReady: (ResourceLoadResult.ThumbnailReady) -> Unit,
        onError: (ResourceLoadResult.Error) -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            // 检查是否已经加载过
            if (loadingStates[project.id] == LoadingState.THUMBNAIL_LOADED) {
                Log.d(TAG, "Thumbnail already loaded for: ${project.id}")
                return@withContext
            }

            loadingStates[project.id] = LoadingState.LOADING_THUMBNAIL
            Log.d(TAG, "Loading thumbnail first for: ${project.id}")

            val startTime = System.currentTimeMillis()
            val thumbnailSource = getThumbnailSource(project)

            // 对于本地资源，使用快速路径
            if (project.isBuiltIn && thumbnailSource.type == ThumbnailSourceType.ASSET) {
                val thumbnailResult = thumbnailManager.getThumbnail(
                    project.id,
                    thumbnailSource.type,
                    thumbnailSource.path
                )

                if (thumbnailResult.isSuccess) {
                    val thumbnailPath = thumbnailResult.getOrThrow()
                    loadingStates[project.id] = LoadingState.THUMBNAIL_LOADED

                    val loadTime = System.currentTimeMillis() - startTime
                    Log.d(TAG, "Local thumbnail loaded for: ${project.id} in ${loadTime}ms")

                    withContext(Dispatchers.Main) {
                        onThumbnailReady(ResourceLoadResult.ThumbnailReady(project.id, thumbnailPath))
                    }
                } else {
                    handleThumbnailError(project, thumbnailResult.exceptionOrNull(), onError)
                }
            } else {
                // 对于远程资源，使用异步加载
                val thumbnailResult = thumbnailManager.getThumbnail(
                    project.id,
                    thumbnailSource.type,
                    thumbnailSource.path
                )

                if (thumbnailResult.isSuccess) {
                    val thumbnailPath = thumbnailResult.getOrThrow()
                    loadingStates[project.id] = LoadingState.THUMBNAIL_LOADED

                    val loadTime = System.currentTimeMillis() - startTime
                    Log.d(TAG, "Remote thumbnail loaded for: ${project.id} in ${loadTime}ms")

                    withContext(Dispatchers.Main) {
                        onThumbnailReady(ResourceLoadResult.ThumbnailReady(project.id, thumbnailPath))
                    }
                } else {
                    handleThumbnailError(project, thumbnailResult.exceptionOrNull(), onError)
                }
            }

        } catch (e: Exception) {
            handleThumbnailError(project, e, onError)
        }
    }

    /**
     * 处理缩略图加载错误
     */
    private suspend fun handleThumbnailError(
        project: HybridResourceManager.HybridProject,
        error: Throwable?,
        onError: (ResourceLoadResult.Error) -> Unit
    ) {
        loadingStates[project.id] = LoadingState.ERROR
        Log.e(TAG, "Failed to load thumbnail for: ${project.id}", error)

        withContext(Dispatchers.Main) {
            onError(ResourceLoadResult.Error(
                project.id,
                "Failed to load thumbnail: ${error?.message}",
                error
            ))
        }
    }
    
    /**
     * 延迟加载项目数据
     * 当用户点击项目时才加载完整的JSON数据
     */
    suspend fun loadDataOnDemand(
        project: HybridResourceManager.HybridProject,
        onDataReady: (ResourceLoadResult.DataReady) -> Unit,
        onError: (ResourceLoadResult.Error) -> Unit
    ) = withContext(Dispatchers.IO) {
        try {
            // 检查缓存
            jsonDataCache[project.id]?.let { cachedData ->

                withContext(Dispatchers.Main) {
                    onDataReady(ResourceLoadResult.DataReady(
                        project.id,
                        cachedData,
                        getOutlinePath(project)
                    ))
                }
                return@withContext
            }
            
            loadingStates[project.id] = LoadingState.LOADING_DATA

            
            when (val result = hybridResourceManager.loadProjectResource(project.id)) {
                is HybridResourceManager.ResourceLoadResult.Success -> {
                    // 缓存数据
                    jsonDataCache[project.id] = result.coloringData
                    loadingStates[project.id] = LoadingState.DATA_LOADED
                    
                    withContext(Dispatchers.Main) {
                        onDataReady(ResourceLoadResult.DataReady(
                            project.id,
                            result.coloringData,
                            getOutlinePath(project)
                        ))
                    }
                    
                    Log.d(TAG, "Data loaded for: ${project.id}")
                }
                
                is HybridResourceManager.ResourceLoadResult.RequiresDownload -> {
                    loadingStates[project.id] = LoadingState.ERROR
                    
                    withContext(Dispatchers.Main) {
                        onError(ResourceLoadResult.Error(
                            project.id,
                            "Project requires download: ${result.project.displayName}",
                            null
                        ))
                    }
                }
                
                is HybridResourceManager.ResourceLoadResult.Error -> {
                    loadingStates[project.id] = LoadingState.ERROR
                    
                    withContext(Dispatchers.Main) {
                        onError(ResourceLoadResult.Error(
                            project.id,
                            "Failed to load data: ${result.message}",
                            null
                        ))
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error loading data for: ${project.id}", e)
            loadingStates[project.id] = LoadingState.ERROR
            
            withContext(Dispatchers.Main) {
                onError(ResourceLoadResult.Error(
                    project.id,
                    "Data loading error: ${e.message}",
                    e
                ))
            }
        }
    }
    
    /**
     * 批量预加载缩略图
     * 用于提前准备列表显示所需的缩略图
     */
    suspend fun preloadThumbnails(
        projects: List<HybridResourceManager.HybridProject>,
        onProgress: (Int, Int) -> Unit = { _, _ -> }
    ): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Preloading thumbnails for ${projects.size} projects")
            
            val thumbnailSources = projects.map { project ->
                project.id to getThumbnailSource(project)
            }
            
            var loadedCount = 0
            thumbnailSources.forEachIndexed { index, (projectId, source) ->
                try {
                    val result = thumbnailManager.getThumbnail(projectId, source.type, source.path)
                    if (result.isSuccess) {
                        loadedCount++
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to preload thumbnail for: $projectId", e)
                }
                
                withContext(Dispatchers.Main) {
                    onProgress(index + 1, projects.size)
                }
            }
            

            Result.success(loadedCount)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error preloading thumbnails", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取项目加载状态
     */
    fun getLoadingState(projectId: String): LoadingState {
        return loadingStates[projectId] ?: LoadingState.IDLE
    }
    
    /**
     * 清理缓存
     */
    fun clearCache() {
        jsonDataCache.clear()
        loadingStates.clear()

    }
    
    /**
     * 获取缓存统计
     */
    fun getCacheStats(): OptimizedCacheStats {
        return OptimizedCacheStats(
            jsonCacheSize = jsonDataCache.size,
            loadingStatesSize = loadingStates.size,
            thumbnailCacheStats = thumbnailManager.getCacheStats()
        )
    }
    
    // 私有方法
    private fun getThumbnailSource(project: HybridResourceManager.HybridProject): ThumbnailSource {
        return when (project.resourceSource) {
            HybridResourceManager.Companion.ResourceSource.BUILT_IN -> {
                // 内置项目，从assets加载outline图片
                // 尝试多种可能的文件名格式
                val possibleOutlineFiles = listOf(
                    "${project.id}_outline.png",
                    "${project.id}_android_outline.png",
                    "${project.name}_outline.png",
                    "${project.name}_android_outline.png",
                    "${project.id}.png"
                )

                // 检查哪个文件存在
                val outlineFile = possibleOutlineFiles.firstOrNull { filename ->
                    try {
                        context.assets.open(filename).close()
                        true
                    } catch (e: Exception) {
                        false
                    }
                } ?: "${project.id}_outline.png" // 默认回退

                ThumbnailSource(ThumbnailSourceType.ASSET, outlineFile)
            }

            HybridResourceManager.Companion.ResourceSource.DOWNLOADED -> {
                // 已下载项目，从缓存目录加载
                val outlinePath = "${context.filesDir}/cache/projects/${project.id}/outline.png"
                ThumbnailSource(ThumbnailSourceType.FILE, outlinePath)
            }

            HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED -> {
                // 远程下载的项目，从下载目录加载
                val outlinePath = "${context.filesDir}/downloaded_projects/${project.id}/${project.id}.png"
                ThumbnailSource(ThumbnailSourceType.FILE, outlinePath)
            }

            HybridResourceManager.Companion.ResourceSource.STREAMING -> {
                // 流式项目，使用缩略图URL
                if (!project.thumbnailUrl.isNullOrEmpty()) {
                    ThumbnailSource(ThumbnailSourceType.URL, project.thumbnailUrl)
                } else {
                    // 回退到预览URL
                    ThumbnailSource(ThumbnailSourceType.URL, project.previewUrl ?: "")
                }
            }
        }
    }
    
    private fun getOutlinePath(project: HybridResourceManager.HybridProject): String? {
        return when (project.resourceSource) {
            HybridResourceManager.Companion.ResourceSource.BUILT_IN -> {
                // 尝试多种可能的文件名格式
                val possibleOutlineFiles = listOf(
                    "${project.id}_outline.png",
                    "${project.id}_android_outline.png",
                    "${project.name}_outline.png",
                    "${project.name}_android_outline.png",
                    "${project.id}.png"
                )

                // 检查哪个文件存在
                possibleOutlineFiles.firstOrNull { filename ->
                    try {
                        context.assets.open(filename).close()
                        true
                    } catch (e: Exception) {
                        false
                    }
                } ?: "${project.id}_outline.png" // 默认回退
            }

            HybridResourceManager.Companion.ResourceSource.DOWNLOADED -> {
                "${context.filesDir}/cache/projects/${project.id}/outline.png"
            }

            HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED -> {
                "${context.filesDir}/downloads/${project.id}.png"
            }

            HybridResourceManager.Companion.ResourceSource.STREAMING -> {
                project.previewUrl
            }
        }
    }
}

/**
 * 优化资源加载器的缓存统计信息
 */
data class OptimizedCacheStats(
    val jsonCacheSize: Int,
    val loadingStatesSize: Int,
    val thumbnailCacheStats: ThumbnailCacheStats
)
