package com.example.coloringproject.utils

import android.content.Context
import android.util.Log
import com.example.coloringproject.config.NetworkConfig
import com.example.coloringproject.network.ResourceDownloadManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.concurrent.TimeUnit

/**
 * 网络测试助手
 * 用于测试和诊断网络连接问题
 */
object NetworkTestHelper {
    private const val TAG = "NetworkTestHelper"
    
    /**
     * 测试结果数据类
     */
    data class NetworkTestResult(
        val isNetworkEnabled: Boolean,
        val serverUrl: String,
        val isServerReachable: Boolean,
        val apiTestResult: String,
        val downloadTestResult: String,
        val errorMessage: String? = null
    )
    
    /**
     * 执行完整的网络功能测试
     */
    suspend fun performNetworkTest(context: Context): NetworkTestResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始网络功能测试...")
            
            // 1. 检查网络功能配置
            val isNetworkEnabled = NetworkConfig.isNetworkFeatureEnabled()
            Log.d(TAG, "网络功能启用状态: $isNetworkEnabled")
            
            // 2. 获取服务器URL
            val serverUrl = com.example.coloringproject.network.NetworkConfig.getBestServerUrl(context)
            Log.d(TAG, "服务器URL: $serverUrl")
            
            // 3. 测试服务器连接
            val isServerReachable = testServerConnection(serverUrl)
            Log.d(TAG, "服务器可达性: $isServerReachable")
            
            // 4. 测试API调用
            val apiTestResult = testApiCalls(context)
            Log.d(TAG, "API测试结果: $apiTestResult")
            
            // 5. 测试下载功能
            val downloadTestResult = testDownloadFunction(context)
            Log.d(TAG, "下载测试结果: $downloadTestResult")
            
            NetworkTestResult(
                isNetworkEnabled = isNetworkEnabled,
                serverUrl = serverUrl,
                isServerReachable = isServerReachable,
                apiTestResult = apiTestResult,
                downloadTestResult = downloadTestResult
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "网络测试失败", e)
            NetworkTestResult(
                isNetworkEnabled = false,
                serverUrl = "未知",
                isServerReachable = false,
                apiTestResult = "测试失败",
                downloadTestResult = "测试失败",
                errorMessage = e.message
            )
        }
    }
    
    /**
     * 测试服务器连接
     */
    private suspend fun testServerConnection(serverUrl: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val client = OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .build()
            
            val request = Request.Builder()
                .url("$serverUrl/health")
                .build()
            
            val response = client.newCall(request).execute()
            val isSuccessful = response.isSuccessful
            
            Log.d(TAG, "服务器连接测试: ${response.code} - ${response.message}")
            response.close()
            
            isSuccessful
        } catch (e: Exception) {
            Log.e(TAG, "服务器连接测试失败", e)
            false
        }
    }
    
    /**
     * 测试API调用
     */
    private suspend fun testApiCalls(context: Context): String = withContext(Dispatchers.IO) {
        try {
            val downloadManager = ResourceDownloadManager(context)
            
            // 测试获取分类列表
            val categoriesResult = downloadManager.getCategoriesList()
            if (categoriesResult.isSuccess) {
                Log.d(TAG, "分类列表API测试成功")
                return@withContext "分类列表API: 成功"
            } else {
                val error = categoriesResult.exceptionOrNull()?.message ?: "未知错误"
                Log.e(TAG, "分类列表API测试失败: $error")
                return@withContext "分类列表API: 失败 - $error"
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "API调用测试失败", e)
            return@withContext "API调用: 失败 - ${e.message}"
        }
    }
    
    /**
     * 测试下载功能
     */
    private suspend fun testDownloadFunction(context: Context): String = withContext(Dispatchers.IO) {
        try {
            val downloadManager = ResourceDownloadManager(context)
            
            // 测试获取项目列表
            val projectsResult = downloadManager.getProjectsList(pageSize = 1)
            if (projectsResult.isSuccess) {
                Log.d(TAG, "项目列表API测试成功")
                return@withContext "项目列表API: 成功"
            } else {
                val error = projectsResult.exceptionOrNull()?.message ?: "未知错误"
                Log.e(TAG, "项目列表API测试失败: $error")
                return@withContext "项目列表API: 失败 - $error"
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "下载功能测试失败", e)
            return@withContext "下载功能: 失败 - ${e.message}"
        }
    }
    
    /**
     * 打印网络测试报告
     */
    fun printNetworkTestReport(result: NetworkTestResult) {
        Log.i(TAG, "=== 网络功能测试报告 ===")
        Log.i(TAG, "网络功能启用: ${result.isNetworkEnabled}")
        Log.i(TAG, "服务器地址: ${result.serverUrl}")
        Log.i(TAG, "服务器可达: ${result.isServerReachable}")
        Log.i(TAG, "API测试: ${result.apiTestResult}")
        Log.i(TAG, "下载测试: ${result.downloadTestResult}")
        result.errorMessage?.let {
            Log.e(TAG, "错误信息: $it")
        }
        Log.i(TAG, "========================")
    }
    
    /**
     * 获取网络状态摘要
     */
    fun getNetworkStatusSummary(context: Context): String {
        val isEnabled = NetworkConfig.isNetworkFeatureEnabled()
        val serverUrl = com.example.coloringproject.network.NetworkConfig.getBestServerUrl(context)
        
        return buildString {
            appendLine("网络功能: ${if (isEnabled) "启用" else "禁用"}")
            appendLine("服务器地址: $serverUrl")
            appendLine("配置状态: ${NetworkConfig.getFeatureStatusReport()}")
        }
    }
}
