package com.example.coloringproject.utils

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.widget.Toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * 图片保存工具类
 * 支持Android 10+ 的分区存储
 */
object ImageSaver {
    
    /**
     * 保存图片到相册
     * @param context 上下文
     * @param bitmap 要保存的图片
     * @param filename 文件名（可选）
     * @return 是否保存成功
     */
    suspend fun saveImageToGallery(
        context: Context,
        bitmap: Bitmap,
        filename: String? = null
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val displayName = filename ?: generateFilename()
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ 使用 MediaStore API
                saveImageWithMediaStore(context, bitmap, displayName)
            } else {
                // Android 9 及以下使用传统方式
                saveImageLegacy(context, bitmap, displayName)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * Android 10+ 使用 MediaStore API 保存图片
     */
    private fun saveImageWithMediaStore(
        context: Context,
        bitmap: Bitmap,
        displayName: String
    ): Boolean {
        val contentResolver: ContentResolver = context.contentResolver
        
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, displayName)
            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
            put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + "/ColoringApp")
        }
        
        val uri: Uri? = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
        
        return uri?.let { imageUri ->
            contentResolver.openOutputStream(imageUri)?.use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)
                true
            } ?: false
        } ?: false
    }
    
    /**
     * Android 9 及以下使用传统方式保存图片
     */
    private fun saveImageLegacy(
        context: Context,
        bitmap: Bitmap,
        displayName: String
    ): Boolean {
        val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        val coloringDir = File(picturesDir, "ColoringApp")
        
        if (!coloringDir.exists()) {
            coloringDir.mkdirs()
        }
        
        val imageFile = File(coloringDir, displayName)
        
        return try {
            FileOutputStream(imageFile).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)
            }
            
            // 通知媒体扫描器
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DATA, imageFile.absolutePath)
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
            }
            context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
            
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * 生成文件名
     */
    private fun generateFilename(): String {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return "coloring_artwork_$timestamp.jpg"
    }
    
    /**
     * 显示保存结果提示
     */
    fun showSaveResult(context: Context, success: Boolean, filename: String? = null) {
        val message = if (success) {
            "画作已保存到相册${filename?.let { " ($it)" } ?: ""}"
        } else {
            "保存失败，请检查存储权限"
        }
        
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 保存多种格式的画作
     * @param context 上下文
     * @param bitmap 填色后的画作
     * @param originalBitmap 原图（可选）
     * @param projectName 项目名称
     */
    suspend fun saveArtworkCollection(
        context: Context,
        bitmap: Bitmap,
        originalBitmap: Bitmap? = null,
        projectName: String = "artwork"
    ): SaveResult = withContext(Dispatchers.IO) {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val results = mutableListOf<String>()
        var successCount = 0
        
        try {
            // 1. 保存填色后的画作
            val artworkFilename = "${projectName}_colored_$timestamp.jpg"
            if (saveImageToGallery(context, bitmap, artworkFilename)) {
                results.add("填色画作")
                successCount++
            }
            
            // 2. 如果有原图，保存对比图
            originalBitmap?.let { original ->
                val comparisonBitmap = createComparisonBitmap(original, bitmap)
                val comparisonFilename = "${projectName}_comparison_$timestamp.jpg"
                if (saveImageToGallery(context, comparisonBitmap, comparisonFilename)) {
                    results.add("对比图")
                    successCount++
                }
            }
            
            SaveResult(successCount > 0, results, successCount)
            
        } catch (e: Exception) {
            e.printStackTrace()
            SaveResult(false, emptyList(), 0)
        }
    }
    
    /**
     * 创建对比图
     */
    private fun createComparisonBitmap(original: Bitmap, colored: Bitmap): Bitmap {
        val width = original.width
        val height = original.height
        val spacing = 20
        
        val comparisonBitmap = Bitmap.createBitmap(
            width * 2 + spacing, 
            height + 60, // 额外空间用于标题
            Bitmap.Config.ARGB_8888
        )
        
        val canvas = android.graphics.Canvas(comparisonBitmap)
        
        // 绘制白色背景
        canvas.drawColor(android.graphics.Color.WHITE)
        
        // 绘制原图
        canvas.drawBitmap(original, 0f, 50f, null)
        
        // 绘制填色图
        canvas.drawBitmap(colored, (width + spacing).toFloat(), 50f, null)
        
        // 添加标题
        val textPaint = android.graphics.Paint(android.graphics.Paint.ANTI_ALIAS_FLAG).apply {
            color = android.graphics.Color.BLACK
            textSize = 32f
            textAlign = android.graphics.Paint.Align.CENTER
        }
        
        canvas.drawText("原图", width / 2f, 35f, textPaint)
        canvas.drawText("填色后", width * 1.5f + spacing / 2f, 35f, textPaint)
        
        return comparisonBitmap
    }
    
    /**
     * 保存结果数据类
     */
    data class SaveResult(
        val success: Boolean,
        val savedItems: List<String>,
        val count: Int
    ) {
        fun getMessage(): String {
            return if (success) {
                "成功保存 $count 张图片: ${savedItems.joinToString(", ")}"
            } else {
                "保存失败，请检查存储权限"
            }
        }
    }
}
