package com.example.coloringproject.utils

import android.util.Log

/**
 * 端到端性能分析器
 * 专门用于分析从项目点击到涂色页完全加载的整个流程
 */
object EndToEndPerformanceAnalyzer {
    
    private const val TAG = "E2E_Performance"
    
    // 性能阶段定义
    enum class PerformancePhase(val displayName: String) {
        PROJECT_CLICK("项目点击"),
        ACTIVITY_CREATE("Activity创建"),
        LAYOUT_INFLATION("布局加载"),
        MANAGER_INIT("管理器初始化"),
        PROGRESS_LOAD("进度加载"),
        PROJECT_FILE_LOAD("项目文件加载"),
        PROJECT_SETUP("项目设置"),
        COLORING_VIEW_SETUP("ColoringView设置"),
        REGION_BITMAP_CREATE("区域位图创建"),
        INITIAL_TRANSFORM("初始变换"),
        UI_READY("界面就绪")
    }
    
    // 性能数据收集
    private val performanceData = mutableMapOf<String, MutableMap<PerformancePhase, Long>>()
    
    /**
     * 记录阶段性能数据
     */
    fun recordPhase(projectId: String, phase: PerformancePhase, duration: Long) {
        performanceData.getOrPut(projectId) { mutableMapOf() }[phase] = duration
        Log.d(TAG, "[$projectId] ${phase.displayName}: ${duration}ms")
    }
    
    /**
     * 分析性能瓶颈
     */
    fun analyzeBottlenecks(projectId: String): PerformanceAnalysis {
        val data = performanceData[projectId] ?: return PerformanceAnalysis.empty(projectId)
        
        val totalTime = data.values.sum()
        val sortedPhases = data.toList().sortedByDescending { it.second }
        
        // 找出最慢的3个阶段
        val topBottlenecks = sortedPhases.take(3)
        
        // 计算各阶段占比
        val phasePercentages = data.mapValues { (_, duration) ->
            if (totalTime > 0) (duration.toFloat() / totalTime * 100).toInt() else 0
        }
        
        // 识别异常慢的阶段（超过总时间的30%）
        val slowPhases = data.filter { (_, duration) ->
            totalTime > 0 && duration.toFloat() / totalTime > 0.3f
        }
        
        return PerformanceAnalysis(
            projectId = projectId,
            totalTime = totalTime,
            phaseData = data.toMap(),
            phasePercentages = phasePercentages,
            topBottlenecks = topBottlenecks,
            slowPhases = slowPhases,
            recommendations = generateRecommendations(data, totalTime)
        )
    }
    
    /**
     * 生成优化建议
     */
    private fun generateRecommendations(data: Map<PerformancePhase, Long>, totalTime: Long): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 检查区域位图创建时间
        data[PerformancePhase.REGION_BITMAP_CREATE]?.let { duration ->
            when {
                duration > 500 -> recommendations.add("区域位图创建过慢(${duration}ms)，考虑进一步优化像素处理算法")
                duration > 200 -> recommendations.add("区域位图创建较慢(${duration}ms)，可以考虑预处理或缓存")
                duration > 100 -> recommendations.add("区域位图创建有优化空间(${duration}ms)")
            }
        }
        
        // 检查项目文件加载时间
        data[PerformancePhase.PROJECT_FILE_LOAD]?.let { duration ->
            when {
                duration > 300 -> recommendations.add("项目文件加载过慢(${duration}ms)，检查IO操作和文件大小")
                duration > 150 -> recommendations.add("项目文件加载较慢(${duration}ms)，考虑异步预加载")
            }
        }
        
        // 检查Activity创建时间
        data[PerformancePhase.ACTIVITY_CREATE]?.let { duration ->
            if (duration > 200) {
                recommendations.add("Activity创建较慢(${duration}ms)，检查onCreate中的同步操作")
            }
        }
        
        // 检查ColoringView设置时间
        data[PerformancePhase.COLORING_VIEW_SETUP]?.let { duration ->
            if (duration > 300) {
                recommendations.add("ColoringView设置过慢(${duration}ms)，检查异步处理是否生效")
            }
        }
        
        // 总体时间评估
        when {
            totalTime > 3000 -> recommendations.add("总体启动时间过长(${totalTime}ms)，需要全面优化")
            totalTime > 2000 -> recommendations.add("总体启动时间较长(${totalTime}ms)，建议重点优化最慢的几个阶段")
            totalTime > 1000 -> recommendations.add("总体启动时间可接受(${totalTime}ms)，但仍有优化空间")
            else -> recommendations.add("总体启动时间良好(${totalTime}ms)")
        }
        
        return recommendations
    }
    
    /**
     * 生成详细报告
     */
    fun generateDetailedReport(projectId: String): String {
        val analysis = analyzeBottlenecks(projectId)
        
        val report = StringBuilder()
        report.appendLine("=== 端到端性能分析报告 ===")
        report.appendLine("项目ID: ${analysis.projectId}")
        report.appendLine("总耗时: ${analysis.totalTime}ms")
        report.appendLine()
        
        report.appendLine("=== 各阶段耗时详情 ===")
        PerformancePhase.values().forEach { phase ->
            val duration = analysis.phaseData[phase] ?: 0
            val percentage = analysis.phasePercentages[phase] ?: 0
            if (duration > 0) {
                report.appendLine("${phase.displayName}: ${duration}ms (${percentage}%)")
            }
        }
        report.appendLine()
        
        report.appendLine("=== 性能瓶颈TOP3 ===")
        analysis.topBottlenecks.forEachIndexed { index, (phase, duration) ->
            val percentage = analysis.phasePercentages[phase] ?: 0
            report.appendLine("${index + 1}. ${phase.displayName}: ${duration}ms (${percentage}%)")
        }
        report.appendLine()
        
        if (analysis.slowPhases.isNotEmpty()) {
            report.appendLine("=== 异常慢的阶段 ===")
            analysis.slowPhases.forEach { (phase, duration) ->
                val percentage = analysis.phasePercentages[phase] ?: 0
                report.appendLine("⚠️ ${phase.displayName}: ${duration}ms (${percentage}%)")
            }
            report.appendLine()
        }
        
        report.appendLine("=== 优化建议 ===")
        analysis.recommendations.forEachIndexed { index, recommendation ->
            report.appendLine("${index + 1}. $recommendation")
        }
        
        return report.toString()
    }
    
    /**
     * 清除项目数据
     */
    fun clearProjectData(projectId: String) {
        performanceData.remove(projectId)
    }
    
    /**
     * 清除所有数据
     */
    fun clearAllData() {
        performanceData.clear()
    }
    
    /**
     * 性能分析结果
     */
    data class PerformanceAnalysis(
        val projectId: String,
        val totalTime: Long,
        val phaseData: Map<PerformancePhase, Long>,
        val phasePercentages: Map<PerformancePhase, Int>,
        val topBottlenecks: List<Pair<PerformancePhase, Long>>,
        val slowPhases: Map<PerformancePhase, Long>,
        val recommendations: List<String>
    ) {
        companion object {
            fun empty(projectId: String) = PerformanceAnalysis(
                projectId = projectId,
                totalTime = 0,
                phaseData = emptyMap(),
                phasePercentages = emptyMap(),
                topBottlenecks = emptyList(),
                slowPhases = emptyMap(),
                recommendations = listOf("无性能数据")
            )
        }
    }
}