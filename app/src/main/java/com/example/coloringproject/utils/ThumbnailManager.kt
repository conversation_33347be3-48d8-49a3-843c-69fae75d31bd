package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.example.coloringproject.network.NetworkConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.URL

/**
 * 缩略图管理器
 * 负责生成、缓存和加载项目缩略图，优化列表显示性能
 */
class ThumbnailManager(private val context: Context) {
    
    companion object {
        private const val TAG = "ThumbnailManager"
        private const val THUMBNAIL_DIR = "thumbnails"
        private const val THUMBNAIL_SIZE = 200 // 缩略图尺寸
        private const val THUMBNAIL_QUALITY = 80 // 压缩质量
    }
    
    private val thumbnailCacheDir: File by lazy {
        File(context.cacheDir, THUMBNAIL_DIR).apply {
            if (!exists()) mkdirs()
        }
    }
    
    /**
     * 缩略图信息
     */
    data class ThumbnailInfo(
        val projectId: String,
        val thumbnailPath: String,
        val width: Int,
        val height: Int,
        val fileSize: Long,
        val lastModified: Long
    )
    
    /**
     * 获取项目缩略图
     * 优先从缓存加载，如果不存在则生成
     */
    suspend fun getThumbnail(
        projectId: String,
        sourceType: ThumbnailSourceType,
        sourcePath: String
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            // 1. 检查缓存是否存在
            val cachedThumbnail = getCachedThumbnailPath(projectId)
            if (File(cachedThumbnail).exists()) {
                Log.d(TAG, "Using cached thumbnail for: $projectId")
                return@withContext Result.success(cachedThumbnail)
            }
            
            // 2. 生成新的缩略图
            Log.d(TAG, "Generating thumbnail for: $projectId")
            Log.d(TAG, "Source type: $sourceType, Source path: $sourcePath")
            val bitmap = when (sourceType) {
                ThumbnailSourceType.ASSET -> {
                    Log.d(TAG, "Loading bitmap from ASSET: $sourcePath")
                    loadBitmapFromAsset(sourcePath)
                }
                ThumbnailSourceType.FILE -> {
                    Log.d(TAG, "Loading bitmap from FILE: $sourcePath")
                    loadBitmapFromFile(sourcePath)
                }
                ThumbnailSourceType.URL -> {
                    Log.d(TAG, "Loading bitmap from URL: $sourcePath")
                    loadBitmapFromUrl(sourcePath)
                }
                ThumbnailSourceType.CACHE -> {
                    Log.d(TAG, "Loading bitmap from CACHE: $sourcePath")
                    loadBitmapFromCache(sourcePath)
                }
            }
            Log.d(TAG, "Bitmap loading result for $projectId: ${bitmap != null}")
            
            if (bitmap != null) {
                val thumbnailPath = generateThumbnail(projectId, bitmap)
                bitmap.recycle()
                Result.success(thumbnailPath)
            } else {
                Result.failure(Exception("Failed to load source image"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting thumbnail for: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量预生成缩略图
     * 用于提前准备缩略图，提升用户体验
     */
    suspend fun preGenerateThumbnails(
        projects: List<Pair<String, ThumbnailSource>>
    ): Result<Int> = withContext(Dispatchers.IO) {
        try {
            var successCount = 0
            
            projects.forEach { (projectId, source) ->
                try {
                    val result = getThumbnail(projectId, source.type, source.path)
                    if (result.isSuccess) {
                        successCount++
                        Log.d(TAG, "Pre-generated thumbnail for: $projectId")
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to pre-generate thumbnail for: $projectId", e)
                }
            }
            
            Log.d(TAG, "Pre-generated $successCount/${projects.size} thumbnails")
            Result.success(successCount)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in batch thumbnail generation", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取缓存的缩略图路径
     */
    fun getCachedThumbnailPath(projectId: String): String {
        return File(thumbnailCacheDir, "${projectId}_thumb.jpg").absolutePath
    }
    
    /**
     * 检查缩略图是否存在
     */
    fun hasCachedThumbnail(projectId: String): Boolean {
        return File(getCachedThumbnailPath(projectId)).exists()
    }
    
    /**
     * 获取缩略图信息
     */
    fun getThumbnailInfo(projectId: String): ThumbnailInfo? {
        val thumbnailFile = File(getCachedThumbnailPath(projectId))
        if (!thumbnailFile.exists()) return null
        
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(thumbnailFile.absolutePath, options)
            
            ThumbnailInfo(
                projectId = projectId,
                thumbnailPath = thumbnailFile.absolutePath,
                width = options.outWidth,
                height = options.outHeight,
                fileSize = thumbnailFile.length(),
                lastModified = thumbnailFile.lastModified()
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting thumbnail info for: $projectId", e)
            null
        }
    }
    
    /**
     * 清理过期的缩略图缓存
     */
    suspend fun cleanupExpiredThumbnails(maxAgeMillis: Long = 7 * 24 * 60 * 60 * 1000L): Int = withContext(Dispatchers.IO) {
        try {
            val currentTime = System.currentTimeMillis()
            var deletedCount = 0
            
            thumbnailCacheDir.listFiles()?.forEach { file ->
                if (file.isFile && currentTime - file.lastModified() > maxAgeMillis) {
                    if (file.delete()) {
                        deletedCount++
                        Log.d(TAG, "Deleted expired thumbnail: ${file.name}")
                    }
                }
            }
            
            Log.d(TAG, "Cleaned up $deletedCount expired thumbnails")
            deletedCount
            
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up thumbnails", e)
            0
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): ThumbnailCacheStats {
        val files = thumbnailCacheDir.listFiles() ?: emptyArray()
        val totalSize = files.sumOf { it.length() }
        
        return ThumbnailCacheStats(
            totalFiles = files.size,
            totalSizeBytes = totalSize,
            totalSizeMB = totalSize / (1024f * 1024f),
            cacheDirectory = thumbnailCacheDir.absolutePath
        )
    }
    
    // 私有方法
    private suspend fun loadBitmapFromAsset(assetPath: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Attempting to load bitmap from asset: $assetPath")
            val inputStream = context.assets.open(assetPath)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            if (bitmap != null) {
                Log.d(TAG, "Successfully loaded bitmap from asset: $assetPath (${bitmap.width}x${bitmap.height})")
            } else {
                Log.w(TAG, "Failed to decode bitmap from asset: $assetPath")
            }
            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "Error loading bitmap from asset: $assetPath", e)
            // 列出assets目录中的文件以帮助调试
            try {
                val allAssets = context.assets.list("") ?: emptyArray()
                Log.d(TAG, "Available assets: ${allAssets.joinToString()}")
            } catch (listException: Exception) {
                Log.e(TAG, "Failed to list assets", listException)
            }
            null
        }
    }
    
    private suspend fun loadBitmapFromFile(filePath: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== BITMAP LOADING DEBUG ===")
            Log.d(TAG, "Attempting to load bitmap from file: $filePath")

            val file = File(filePath)
            Log.d(TAG, "File exists: ${file.exists()}")
            Log.d(TAG, "File size: ${file.length()}")
            Log.d(TAG, "File readable: ${file.canRead()}")
            Log.d(TAG, "File absolute path: ${file.absolutePath}")

            if (!file.exists()) {
                Log.e(TAG, "File does not exist: $filePath")
                return@withContext null
            }

            val bitmap = BitmapFactory.decodeFile(filePath)
            Log.d(TAG, "Bitmap decode result: ${bitmap != null}")
            if (bitmap != null) {
                Log.d(TAG, "Bitmap dimensions: ${bitmap.width}x${bitmap.height}")
            }
            Log.d(TAG, "=== END BITMAP LOADING DEBUG ===")

            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "Error loading bitmap from file: $filePath", e)
            null
        }
    }
    
    private suspend fun loadBitmapFromUrl(url: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            // 处理URL格式，确保包含协议和主机名
            val fullUrl = when {
                url.startsWith("http://") || url.startsWith("https://") -> url
                url.startsWith("/") -> {
                    // 相对路径，需要添加基础服务器URL（不是API客户端URL）
                    val baseServerUrl = NetworkConfig.getBaseServerUrl(context)
                    // 避免双斜杠问题
                    if (baseServerUrl.endsWith("/")) baseServerUrl + url.substring(1) else baseServerUrl + url
                }
                else -> {
                    // 没有前导斜杠的相对路径
                    val baseServerUrl = NetworkConfig.getBaseServerUrl(context)
                    if (baseServerUrl.endsWith("/")) baseServerUrl + url else "$baseServerUrl/$url"
                }
            }


            val inputStream = URL(fullUrl).openStream()
            BitmapFactory.decodeStream(inputStream)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading bitmap from URL: $url", e)
            null
        }
    }
    
    private suspend fun loadBitmapFromCache(cachePath: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            BitmapFactory.decodeFile(cachePath)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading bitmap from cache: $cachePath", e)
            null
        }
    }
    
    private suspend fun generateThumbnail(projectId: String, originalBitmap: Bitmap): String = withContext(Dispatchers.IO) {
        val thumbnailPath = getCachedThumbnailPath(projectId)
        
        // 计算缩放比例
        val scale = minOf(
            THUMBNAIL_SIZE.toFloat() / originalBitmap.width,
            THUMBNAIL_SIZE.toFloat() / originalBitmap.height
        )
        
        val scaledWidth = (originalBitmap.width * scale).toInt()
        val scaledHeight = (originalBitmap.height * scale).toInt()
        
        // 创建缩略图
        val thumbnail = Bitmap.createScaledBitmap(originalBitmap, scaledWidth, scaledHeight, true)
        
        // 保存到文件
        FileOutputStream(thumbnailPath).use { out ->
            thumbnail.compress(Bitmap.CompressFormat.JPEG, THUMBNAIL_QUALITY, out)
        }
        
        thumbnail.recycle()
        Log.d(TAG, "Generated thumbnail: $thumbnailPath (${scaledWidth}x${scaledHeight})")
        
        thumbnailPath
    }
}

/**
 * 缩略图来源类型
 */
enum class ThumbnailSourceType {
    ASSET,  // Assets资源
    FILE,   // 本地文件
    URL,    // 网络URL
    CACHE   // 缓存文件
}

/**
 * 缩略图来源信息
 */
data class ThumbnailSource(
    val type: ThumbnailSourceType,
    val path: String
)

/**
 * 缩略图缓存统计
 */
data class ThumbnailCacheStats(
    val totalFiles: Int,
    val totalSizeBytes: Long,
    val totalSizeMB: Float,
    val cacheDirectory: String
)
