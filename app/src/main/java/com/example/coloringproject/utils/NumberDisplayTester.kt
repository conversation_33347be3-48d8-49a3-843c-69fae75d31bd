//package com.example.coloringproject.utils
//
//import android.util.Log
//import com.example.coloringproject.view.ColoringView
//import com.example.coloringproject.view.NumberDisplayHelper
//
///**
// * 数字显示测试器
// * 用于验证数字显示优化效果
// */
//object NumberDisplayTester {
//
//    private const val TAG = "NumberDisplayTest"
//
//    /**
//     * 测试数字显示优化效果
//     */
//
//    /**
//     * 测试颜色映射
//     */
//    private fun testColorMapping(regions: List<com.example.coloringproject.data.Region>) {
//        Log.d(TAG, "--- 颜色映射测试 ---")
//
//        val colorMapping = NumberDisplayHelper.getColorMappingInfo(regions)
//
//        Log.d(TAG, "颜色映射表:")
//        colorMapping.entries.take(10).forEach { (color, number) ->
//            Log.d(TAG, "  颜色 $color -> 数字 $number")
//        }
//
//        Log.d(TAG, "总共 ${colorMapping.size} 种颜色")
//
//        // 验证数字是否连续
//        val numbers = colorMapping.values.sorted()
//        val expectedNumbers = (1..colorMapping.size).toList()
//
//        if (numbers == expectedNumbers) {
//            Log.d(TAG, "✅ 数字编号连续正确 (1-${colorMapping.size})")
//        } else {
//            Log.w(TAG, "❌ 数字编号不连续: $numbers")
//        }
//    }
//
//    /**
//     * 测试数字显示逻辑
//     */
//    private fun testNumberDisplayLogic(coloringView: ColoringView) {
//        Log.d(TAG, "--- 数字显示逻辑测试 ---")
//
//        val currentColor = coloringView.getCurrentColorHexPublic()
//        val scaleFactor = coloringView.getScaleFactorPublic()
//
//        // 测试缩放阈值
//        if (scaleFactor < 1.2f) {
//            Log.d(TAG, "缩放级别 ${scaleFactor} < 1.2，数字应该不显示")
//        } else {
//            Log.d(TAG, "缩放级别 ${scaleFactor} >= 1.2，数字应该显示")
//        }
//
//        // 测试颜色选择
//        if (currentColor != null) {
//            Log.d(TAG, "✅ 已选择颜色: $currentColor")
//
//            val coloringData = coloringView.getColoringDataPublic()!!
//            val filledRegions = coloringView.getFilledRegionsPublic()
//
//            // 统计匹配的区域
//            val matchingRegions = coloringData.regions.filter { region ->
//                normalizeColorHex(region.colorHex) == normalizeColorHex(currentColor) &&
//                !filledRegions.contains(region.id)
//            }
//
//            Log.d(TAG, "当前颜色的未填色区域: ${matchingRegions.size}个")
//
//            if (matchingRegions.isNotEmpty()) {
//                // 分析区域大小分布
//                val regionSizes = matchingRegions.map { region ->
//                    val bounds = getRegionBounds(region)
//                    val size = kotlin.math.min(bounds.width(), bounds.height()) * scaleFactor
//                    size
//                }
//
//                val avgSize = regionSizes.average()
//                val maxSize = regionSizes.maxOrNull() ?: 0f
//                val minSize = regionSizes.minOrNull() ?: 0f
//
//                Log.d(TAG, "区域显示大小分布:")
//                Log.d(TAG, "  平均: ${avgSize.toInt()}px")
//                Log.d(TAG, "  最大: ${maxSize.toInt()}px")
//                Log.d(TAG, "  最小: ${minSize.toInt()}px")
//
//                // 预测显示数字的数量
//                val expectedNumbers = regionSizes.count { it >= 25f } // MIN_REGION_SIZE_FOR_NUMBER
//                Log.d(TAG, "预期显示数字数量: ${expectedNumbers}个")
//            }
//        } else {
//            Log.w(TAG, "❌ 未选择颜色，数字不应显示")
//        }
//    }
//
//    /**
//     * 测试缩放响应
//     */
//    private fun testScaleResponse(coloringView: ColoringView) {
//        Log.d(TAG, "--- 缩放响应测试 ---")
//
//        val testScales = listOf(0.8f, 1.0f, 1.5f, 2.0f, 3.0f)
//
//        testScales.forEach { testScale ->
//            val shouldShow = testScale >= 1.2f
//            val numberSize = calculateExpectedNumberSize(testScale)
//
//            Log.d(TAG, "缩放 ${testScale}x:")
//            Log.d(TAG, "  应该显示数字: $shouldShow")
//            if (shouldShow) {
//                Log.d(TAG, "  预期数字大小范围: ${numberSize.toInt()}px")
//            }
//        }
//    }
//
//    /**
//     * 计算预期的数字大小
//     */
//    private fun calculateExpectedNumberSize(scale: Float): Float {
//        // 模拟NumberDisplayManager中的计算逻辑
//        val displaySize = 100f * scale // 假设100px的区域
//        val ratio = displaySize / 100f
//        val baseSize = 14f
//        val calculatedSize = baseSize * kotlin.math.sqrt(ratio)
//        return calculatedSize.coerceIn(8f, 28f)
//    }
//
//    /**
//     * 获取区域边界
//     */
//    private fun getRegionBounds(region: com.example.coloringproject.data.Region): android.graphics.RectF {
//        region.boundingBox?.let { bbox ->
//            if (bbox.size >= 4) {
//                return android.graphics.RectF(
//                    bbox[0].toFloat(),
//                    bbox[1].toFloat(),
//                    bbox[2].toFloat(),
//                    bbox[3].toFloat()
//                )
//            }
//        }
//
//        if (region.pixels.isEmpty()) return android.graphics.RectF()
//
//        var minX = Int.MAX_VALUE
//        var minY = Int.MAX_VALUE
//        var maxX = Int.MIN_VALUE
//        var maxY = Int.MIN_VALUE
//
//        region.pixels.forEach { pixel ->
//            minX = kotlin.math.min(minX, pixel[0])
//            minY = kotlin.math.min(minY, pixel[1])
//            maxX = kotlin.math.max(maxX, pixel[0])
//            maxY = kotlin.math.max(maxY, pixel[1])
//        }
//
//        return android.graphics.RectF(minX.toFloat(), minY.toFloat(), maxX.toFloat(), maxY.toFloat())
//    }
//
//    /**
//     * 标准化颜色格式
//     */
//    private fun normalizeColorHex(colorHex: String): String {
//        var normalized = colorHex.trim().lowercase()
//        if (!normalized.startsWith("#")) {
//            normalized = "#$normalized"
//        }
//        if (normalized.length == 4) {
//            val r = normalized[1]
//            val g = normalized[2]
//            val b = normalized[3]
//            normalized = "#$r$r$g$g$b$b"
//        }
//        return normalized
//    }
//}