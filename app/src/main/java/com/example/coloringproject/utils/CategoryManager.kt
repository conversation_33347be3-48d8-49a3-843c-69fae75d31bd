package com.example.coloringproject.utils

import android.content.Context
import android.util.Log
import com.example.coloringproject.network.ResourceDownloadManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap

/**
 * 品类管理器 - 管理项目分类和缓存
 */
class CategoryManager(
    private val context: Context,
    private val downloadManager: ResourceDownloadManager
) {
    companion object {
        private const val TAG = "CategoryManager"
        private const val DEFAULT_CACHE_SIZE = 2 // 默认缓存2个品类
    }

    // 品类数据缓存
    private val categoryCache = ConcurrentHashMap<String, CategoryData>()
    
    // 当前活跃的品类
    private var activeCategoryIds = mutableSetOf<String>()
    
    // 品类列表
    private var availableCategories = listOf<Category>()

    /**
     * 品类数据类
     */
    data class Category(
        val id: String,
        val name: String,
        val description: String,
        val thumbnailUrl: String?,
        val projectCount: Int,
        val isPopular: Boolean = false
    )

    /**
     * 品类数据缓存
     */
    data class CategoryData(
        val category: Category,
        val projects: List<HybridResourceManager.HybridProject>,
        val lastUpdated: Long,
        val isLoading: Boolean = false
    )

    /**
     * 初始化品类管理器 - 从API获取真实分类数据
     */
    suspend fun initialize(): Result<List<Category>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🚀 开始初始化CategoryManager...")
            Log.d(TAG, "🔄 正在尝试从服务器获取分类数据...")

            // 尝试从API获取分类数据
            val apiCategories = fetchCategoriesFromAPI()

            if (apiCategories.isNotEmpty()) {
                availableCategories = apiCategories
                Log.d(TAG, "✅ 成功从服务器加载分类数据: ${availableCategories.size} 个分类")
                Log.d(TAG, "📋 服务器分类列表: ${availableCategories.map { "${it.id}(${it.name})" }.joinToString(", ")}")
            } else {
                // API失败时使用默认分类
                availableCategories = getDefaultCategories()
                Log.w(TAG, "⚠️ 服务器获取分类失败，使用默认分类: ${availableCategories.size} 个分类")
                Log.d(TAG, "📋 默认分类列表: ${availableCategories.map { "${it.id}(${it.name})" }.joinToString(", ")}")
            }

            // 预加载默认品类
            preloadDefaultCategories()

            Log.d(TAG, "🎉 CategoryManager初始化完成")
            Result.success(availableCategories)
        } catch (e: Exception) {
            Log.e(TAG, "❌ CategoryManager初始化失败", e)
            // 出错时使用默认分类
            availableCategories = getDefaultCategories()
            Log.w(TAG, "⚠️ 异常情况下使用默认分类: ${availableCategories.size} 个分类")
            Result.success(availableCategories)
        }
    }

    /**
     * 从API获取分类数据
     */
    private suspend fun fetchCategoriesFromAPI(): List<Category> {
        return try {
            Log.d(TAG, "🌐 开始从服务器获取分类数据...")

            // 获取服务器URL信息
            val serverUrl = downloadManager.getServerBaseUrl()
            Log.d(TAG, "🔗 服务器地址: $serverUrl")
            Log.d(TAG, "🔗 完整分类API地址: ${serverUrl}categories")

            // 调用真实的API获取分类列表
            val result = downloadManager.getCategoriesList()

            if (result.isSuccess) {
                val apiResponse = result.getOrNull()
                Log.d(TAG, "✅ 服务器响应成功，状态: ${apiResponse?.status}")

                if (apiResponse?.status == "success" && apiResponse.data != null) {
                    val categories = apiResponse.data.categories.map { apiCategory ->
                        Category(
                            id = apiCategory.id,
                            name = apiCategory.name,
                            description = apiCategory.description,
                            thumbnailUrl = apiCategory.iconUrl,
                            projectCount = apiCategory.projectCount,
                            isPopular = apiCategory.sortOrder <= 2 // 前两个分类设为热门
                        )
                    }
                    Log.d(TAG, "🎉 成功从服务器获取分类数据: ${categories.size} 个分类")
                    categories.forEach { category ->
                        Log.d(TAG, "📂 分类: ${category.id} - ${category.name} (${category.projectCount} 个项目)")
                    }
                    return categories
                } else {
                    Log.e(TAG, "❌ 服务器返回失败响应: ${apiResponse?.status}")
                    Log.e(TAG, "❌ 从服务器获取分类数据失败 - API响应状态异常")
                }
            } else {
                val exception = result.exceptionOrNull()
                Log.e(TAG, "❌ 从服务器获取分类数据失败 - 网络请求失败: ${exception?.message}")
                Log.e(TAG, "❌ 异常详情: ${exception?.javaClass?.simpleName}")
                if (exception != null) {
                    Log.e(TAG, "❌ 异常堆栈:", exception)
                }
            }

            // API失败时返回空列表，将使用默认分类
            Log.w(TAG, "⚠️ 服务器获取分类失败，将使用默认分类")
            emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "❌ 从服务器获取分类数据时发生异常", e)
            Log.e(TAG, "❌ 异常类型: ${e.javaClass.simpleName}")
            Log.e(TAG, "❌ 异常消息: ${e.message}")
            emptyList()
        }
    }

    /**
     * 获取默认分类（备用）
     */
    private fun getDefaultCategories(): List<Category> {
        return listOf(
            Category("animals", "动物世界", "可爱的动物涂色", null, 2, true),
            Category("nature", "自然风景", "美丽的自然风景", null, 2, true),
            Category("fantasy", "奇幻世界", "神奇的奇幻世界", null, 0, false),
            Category("vehicles", "交通工具", "各种交通载具", null, 0, false),
            Category("flowers", "植物花卉", "美丽的花朵植物", null, 0, false),
            Category("buildings", "建筑景观", "壮观的建筑风景", null, 0, false)
        )
    }

    /**
     * 预加载默认品类（最受欢迎的2个）
     */
    private suspend fun preloadDefaultCategories() {
        val defaultCategories = availableCategories
            .sortedByDescending { it.isPopular }
            .take(DEFAULT_CACHE_SIZE)
        
        defaultCategories.forEach { category ->
            loadCategoryData(category.id, preload = true)
        }
    }

    /**
     * 获取品类列表
     */
    fun getAvailableCategories(): List<Category> = availableCategories

    /**
     * 获取品类数据 - 分层加载策略
     */
    suspend fun getCategoryData(categoryId: String, onProgressUpdate: ((List<HybridResourceManager.HybridProject>) -> Unit)? = null): Result<CategoryData> = withContext(Dispatchers.IO) {
        try {
            // 确保分类列表已初始化
            if (availableCategories.isEmpty()) {
                Log.d(TAG, "Categories not initialized, initializing now...")
                val initResult = initialize()
                if (initResult.isFailure) {
                    return@withContext Result.failure(initResult.exceptionOrNull() ?: Exception("Failed to initialize categories"))
                }
            }

            // 检查缓存
            val cachedData = categoryCache[categoryId]
            if (cachedData != null && !isDataExpired(cachedData)) {
                Log.d(TAG, "Returning cached data for category: $categoryId")
                return@withContext Result.success(cachedData)
            }

            // 分层加载数据
            loadCategoryDataLayered(categoryId, onProgressUpdate)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting category data for: $categoryId", e)
            Result.failure(e)
        }
    }

    /**
     * 分层加载品类数据：Assets → Cache → Network
     */
    private suspend fun loadCategoryDataLayered(categoryId: String, onProgressUpdate: ((List<HybridResourceManager.HybridProject>) -> Unit)? = null): Result<CategoryData> {
        // 查找分类信息，如果找不到则创建一个临时的
        val category = availableCategories.find { it.id == categoryId }
            ?: Category(categoryId, categoryId.replaceFirstChar { it.uppercase() }, "临时分类", null, 0, false)

        Log.d(TAG, "Loading layered data for category: $categoryId (${category.name})")
        val allProjects = mutableListOf<HybridResourceManager.HybridProject>()

        try {
            // 第一层：加载Assets中的项目（最快）
            val assetProjects = loadAssetsProjects(categoryId)
            if (assetProjects.isNotEmpty()) {
                allProjects.addAll(assetProjects)
                Log.d(TAG, "Loaded ${assetProjects.size} projects from assets for category: $categoryId")

                // 立即更新UI显示assets项目
                withContext(Dispatchers.Main) {
                    onProgressUpdate?.invoke(allProjects.toList())
                }
            }

            // 第二层：加载Cache中的项目（中等速度）
            val cacheProjects = loadCacheProjects(categoryId)
            if (cacheProjects.isNotEmpty()) {
                allProjects.addAll(cacheProjects)
                Log.d(TAG, "Loaded ${cacheProjects.size} projects from cache for category: $categoryId")

                // 更新UI显示assets + cache项目
                withContext(Dispatchers.Main) {
                    onProgressUpdate?.invoke(allProjects.toList())
                }
            }

            // 第三层：从网络加载项目（最慢）
            val networkProjects = loadNetworkProjects(categoryId)
            if (networkProjects.isNotEmpty()) {
                allProjects.addAll(networkProjects)
                Log.d(TAG, "Loaded ${networkProjects.size} projects from network for category: $categoryId")

                // 最终更新UI显示所有项目
                withContext(Dispatchers.Main) {
                    onProgressUpdate?.invoke(allProjects.toList())
                }
            }

            // 创建最终的CategoryData
            val categoryData = CategoryData(
                category = category,
                projects = allProjects,
                lastUpdated = System.currentTimeMillis(),
                isLoading = false
            )

            categoryCache[categoryId] = categoryData
            activeCategoryIds.add(categoryId)
            manageCacheSize()

            Log.d(TAG, "Completed layered loading for category: $categoryId, total: ${allProjects.size} projects")
            return Result.success(categoryData)

        } catch (e: Exception) {
            Log.e(TAG, "Error in layered loading for category: $categoryId", e)
            return Result.failure(e)
        }
    }

    /**
     * 加载品类数据（原有方法，保持兼容性）
     */
    private suspend fun loadCategoryData(categoryId: String, preload: Boolean = false): Result<CategoryData> {
        try {
            val category = availableCategories.find { it.id == categoryId }
                ?: return Result.failure(Exception("Category not found: $categoryId"))

            Log.d(TAG, "Loading data for category: $categoryId")

            // 标记为加载中
            val loadingData = CategoryData(
                category = category,
                projects = emptyList(),
                lastUpdated = System.currentTimeMillis(),
                isLoading = true
            )
            categoryCache[categoryId] = loadingData

            // 暂时使用模拟项目数据
            val projects = listOf(
                createMockProject("${categoryId}_1", "项目1", categoryId),
                createMockProject("${categoryId}_2", "项目2", categoryId),
                createMockProject("${categoryId}_3", "项目3", categoryId)
            )

                    val categoryData = CategoryData(
                        category = category,
                        projects = projects,
                        lastUpdated = System.currentTimeMillis(),
                        isLoading = false
                    )

                    categoryCache[categoryId] = categoryData
                    activeCategoryIds.add(categoryId)

                    // 管理缓存大小
                    manageCacheSize()

            Log.d(TAG, "Loaded ${projects.size} projects for category: $categoryId")
            return Result.success(categoryData)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading category data: $categoryId", e)
            return Result.failure(e)
        }
    }

    /**
     * 管理缓存大小，移除最旧的数据
     */
    private fun manageCacheSize() {
        if (categoryCache.size > DEFAULT_CACHE_SIZE * 2) {
            val oldestEntry = categoryCache.entries
                .filter { !activeCategoryIds.contains(it.key) }
                .minByOrNull { it.value.lastUpdated }
            
            oldestEntry?.let {
                categoryCache.remove(it.key)
                Log.d(TAG, "Removed cached data for category: ${it.key}")
            }
        }
    }

    /**
     * 检查数据是否过期（1小时）
     */
    private fun isDataExpired(data: CategoryData): Boolean {
        val expirationTime = 60 * 60 * 1000L // 1小时
        return System.currentTimeMillis() - data.lastUpdated > expirationTime
    }

    /**
     * 清除缓存
     */
    fun clearCache() {
        categoryCache.clear()
        activeCategoryIds.clear()
        Log.d(TAG, "Cache cleared")
    }

    /**
     * 获取缓存状态
     */
    fun getCacheStatus(): Map<String, Boolean> {
        return availableCategories.associate { category ->
            category.id to categoryCache.containsKey(category.id)
        }
    }

    /**
     * 获取下载管理器
     */
    fun getDownloadManager(): ResourceDownloadManager {
        return downloadManager
    }

    /**
     * 从Assets加载项目
     */
    private suspend fun loadAssetsProjects(categoryId: String): List<HybridResourceManager.HybridProject> {
        return try {
            val assetProjects = mutableListOf<HybridResourceManager.HybridProject>()

            // 从对应分类文件夹中加载真实的assets资源
            val assetManager = context.assets
            val categoryPath = categoryId

            Log.d(TAG, "Attempting to load assets from path: $categoryPath")

            try {
                val files = assetManager.list(categoryPath) ?: emptyArray()
                Log.d(TAG, "Files in assets/$categoryPath: ${files.joinToString()}")

                val jsonFiles = files.filter { it.endsWith(".json") }
                Log.d(TAG, "Found ${jsonFiles.size} JSON files in assets/$categoryPath: ${jsonFiles.joinToString()}")

                jsonFiles.forEach { jsonFile ->
                    val baseName = jsonFile.removeSuffix(".json")
                    val outlineFile = "${baseName}.png"

                    Log.d(TAG, "Checking for JSON: $jsonFile and PNG: $outlineFile")

                    // 检查对应的PNG文件是否存在
                    if (files.contains(outlineFile)) {
                        assetProjects.add(
                            HybridResourceManager.HybridProject(
                                id = baseName,
                                name = baseName,
                                displayName = baseName.replace("-", " ").replaceFirstChar { it.uppercase() },
                                description = "来自Assets的$categoryId 类别项目",
                                category = categoryId,
                                difficulty = "medium",
                                totalRegions = 20,
                                totalColors = 8,
                                estimatedTime = 30,
                                thumbnailUrl = null,
                                previewUrl = null,
                                resourceType = HybridResourceManager.Companion.ResourceType.LOCAL_ASSET,
                                resourceSource = HybridResourceManager.Companion.ResourceSource.BUILT_IN,
                                version = "1.0",
                                fileSize = 1024L,
                                isDownloaded = false,
                                isBuiltIn = true
                            )
                        )
                        Log.d(TAG, "✅ Added asset project: $baseName from category: $categoryId")
                    } else {
                        Log.w(TAG, "❌ Missing PNG file for: $baseName in category: $categoryId")
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "Category folder $categoryPath not found in assets, using fallback", e)
                // 如果分类文件夹不存在，尝试从根目录加载相关文件
                val rootFiles = assetManager.list("") ?: emptyArray()
                Log.d(TAG, "Root assets files: ${rootFiles.joinToString()}")

                val categoryJsonFiles = rootFiles.filter {
                    it.endsWith(".json") && it.contains(categoryId, ignoreCase = true)
                }
                Log.d(TAG, "Found ${categoryJsonFiles.size} matching files in root for $categoryId: ${categoryJsonFiles.joinToString()}")

                categoryJsonFiles.forEach { jsonFile ->
                    val baseName = jsonFile.removeSuffix(".json")
                    val outlineFile = "${baseName}.png"

                    if (rootFiles.contains(outlineFile)) {
                        assetProjects.add(createMockProject(baseName, baseName, categoryId, "ASSET"))
                        Log.d(TAG, "✅ Added fallback asset project: $baseName")
                    } else {
                        Log.w(TAG, "❌ Missing PNG file for fallback: $baseName")
                    }
                }
            }

            Log.d(TAG, "Final result: Loaded ${assetProjects.size} projects from assets for category: $categoryId")
            assetProjects
        } catch (e: Exception) {
            Log.e(TAG, "Error loading assets projects for category: $categoryId", e)
            emptyList()
        }
    }

    /**
     * 从Cache加载项目
     */
    private suspend fun loadCacheProjects(categoryId: String): List<HybridResourceManager.HybridProject> {
        return try {
            val cacheProjects = mutableListOf<HybridResourceManager.HybridProject>()

            // 模拟从cache加载的项目
            for (i in 1..1) {
                cacheProjects.add(createMockProject("${categoryId}_cache_$i", "缓存项目$i", categoryId, "CACHE"))
            }

            Log.d(TAG, "Loaded ${cacheProjects.size} projects from cache for category: $categoryId")
            cacheProjects
        } catch (e: Exception) {
            Log.e(TAG, "Error loading cache projects for category: $categoryId", e)
            emptyList()
        }
    }

    /**
     * 从网络加载项目
     */
    private suspend fun loadNetworkProjects(categoryId: String): List<HybridResourceManager.HybridProject> {
        return try {
            Log.d(TAG, "Loading network projects for category: $categoryId")

            // 调用API获取分类资源：GET /api/resources/category/en/{categoryNameEn}
            val result = downloadManager.getProjectsList(
                category = categoryId,
                difficulty = null,
                featured = null,
                page = 1,
                pageSize = 20
            )

            if (result.isSuccess) {
                val apiResponse = result.getOrNull()
                if (apiResponse?.status == "success" && apiResponse.data != null) {
                    val networkProjects = apiResponse.data.projects.map { apiProject ->
                        HybridResourceManager.HybridProject(
                            id = apiProject.id,
                            name = apiProject.name,
                            displayName = apiProject.displayName,
                            description = apiProject.description,
                            category = apiProject.category,
                            difficulty = apiProject.difficulty,
                            totalRegions = apiProject.totalRegions,
                            totalColors = apiProject.totalColors,
                            estimatedTime = apiProject.estimatedTimeMinutes,
                            thumbnailUrl = apiProject.thumbnailUrl,
                            previewUrl = apiProject.previewUrl,
                            resourceType = HybridResourceManager.Companion.ResourceType.REMOTE_ONLY,
                            resourceSource = HybridResourceManager.Companion.ResourceSource.STREAMING,
                            version = apiProject.version,
                            fileSize = apiProject.fileSize,
                            isDownloaded = false,
                            isBuiltIn = false
                        )
                    }

                    Log.d(TAG, "Loaded ${networkProjects.size} projects from network for category: $categoryId")
                    return networkProjects
                } else {
                    Log.w(TAG, "API returned unsuccessful response for category $categoryId: ${apiResponse?.status}")
                }
            } else {
                Log.w(TAG, "Failed to fetch projects for category $categoryId: ${result.exceptionOrNull()?.message}")
            }

            emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error loading network projects for category: $categoryId", e)
            emptyList()
        }
    }

    /**
     * 创建模拟项目
     */
    private fun createMockProject(id: String, name: String, categoryId: String, source: String = ""): HybridResourceManager.HybridProject {
        val resourceType = when (source) {
            "ASSET" -> HybridResourceManager.Companion.ResourceType.LOCAL_ASSET
            "CACHE" -> HybridResourceManager.Companion.ResourceType.CACHED_REMOTE
            else -> HybridResourceManager.Companion.ResourceType.REMOTE_ONLY
        }

        val resourceSource = when (source) {
            "ASSET" -> HybridResourceManager.Companion.ResourceSource.BUILT_IN
            "CACHE" -> HybridResourceManager.Companion.ResourceSource.DOWNLOADED
            else -> HybridResourceManager.Companion.ResourceSource.STREAMING
        }

        return HybridResourceManager.HybridProject(
            id = id,
            name = name,
            displayName = if (source.isNotEmpty()) "$name ($source)" else name,
            description = "这是一个$categoryId 类别的项目 (来源: $source)",
            category = categoryId,
            difficulty = "medium",
            totalRegions = 50,
            totalColors = 8,
            estimatedTime = 30,
            thumbnailUrl = null,
            previewUrl = null,
            resourceType = resourceType,
            resourceSource = resourceSource,
            version = "1.0",
            fileSize = 1024L,
            isDownloaded = source == "CACHE",
            isBuiltIn = source == "ASSET"
        )
    }
}


