package com.example.coloringproject.utils

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser

/**
 * 优化的进度解析器 - 方案B：分步解析
 * 目标：将1300ms的解析时间减少到150-250ms
 */
class OptimizedProgressParser {
    
    companion object {
        private const val TAG = "OptimizedProgressParser"
    }
    
    private val gson = Gson()
    private val jsonParser = JsonParser()
    
    /**
     * 基本项目信息（快速解析，~50-100ms）
     */
    data class BasicProjectInfo(
        val projectName: String,
        val totalRegions: Int,
        val progressPercentage: Int,
        val isCompleted: Boolean,
        val lastModified: Long,
        val filledRegionsCount: Int,
        val filledRegions: Set<Int> // 添加实际的填色区域数据
    )
    
    /**
     * 颜色区域信息（按需解析，~100-200ms）
     */
    data class ColorRegionInfo(
        val colorHex: String,
        val filledRegions: Set<Int>,
        val totalRegionsForColor: Int,
        val progressPercentage: Int
    )
    
    /**
     * 第一步：快速解析基本信息
     * 仅解析项目名称、总进度等关键字段，跳过大型数组
     */
    fun parseBasicInfo(jsonString: String): BasicProjectInfo {
        val parseStart = System.currentTimeMillis()
        
        try {
            // 使用正则表达式快速提取关键字段，避免完整JSON解析
            val projectName = extractStringField(jsonString, "projectName") ?: ""
            val totalRegions = extractIntField(jsonString, "totalRegions") ?: 0
            val progressPercentage = extractIntField(jsonString, "progressPercentage") ?: 0
            val isCompleted = extractBooleanField(jsonString, "isCompleted") ?: false
            val lastModified = extractLongField(jsonString, "lastModified") ?: System.currentTimeMillis()
            
            // 高效解析已填充区域（保留进度数据）
            val filledRegions = parseFilledRegionsOptimized(jsonString)
            val filledRegionsCount = filledRegions.size

            val parseTime = System.currentTimeMillis() - parseStart
            Log.d(TAG, "📊 [性能] 基本信息解析耗时: ${parseTime}ms (包含${filledRegionsCount}个填色区域)")

            return BasicProjectInfo(
                projectName = projectName,
                totalRegions = totalRegions,
                progressPercentage = progressPercentage,
                isCompleted = isCompleted,
                lastModified = lastModified,
                filledRegionsCount = filledRegionsCount,
                filledRegions = filledRegions
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "基本信息解析失败", e)
            throw e
        }
    }
    
    /**
     * 第二步：按需解析指定颜色的区域信息
     * 仅解析用户当前选择的颜色相关数据
     */
    fun parseColorRegions(jsonString: String, targetColorHex: String): ColorRegionInfo {
        val parseStart = System.currentTimeMillis()
        
        try {
            // 解析JSON对象（仅解析需要的部分）
            val jsonObject = jsonParser.parse(jsonString).asJsonObject
            
            // 获取coloringData
            val coloringData = jsonObject.getAsJsonObject("coloringData")
            val regions = coloringData.getAsJsonArray("regions")
            val colorPalette = coloringData.getAsJsonArray("color_palette")
            
            // 获取已填充区域
            val filledRegionsArray = jsonObject.getAsJsonArray("filledRegions")
            val filledRegions = mutableSetOf<Int>()
            filledRegionsArray?.forEach { element ->
                filledRegions.add(element.asInt)
            }
            
            // 找到目标颜色对应的区域
            val targetColorRegions = mutableSetOf<Int>()
            val targetColorFilledRegions = mutableSetOf<Int>()
            
            // 从color_palette中找到目标颜色的区域
            colorPalette?.forEach { paletteElement ->
                val palette = paletteElement.asJsonObject
                val colorHex = palette.get("hex")?.asString
                
                if (colorHex == targetColorHex) {
                    val regionsArray = palette.getAsJsonArray("regions")
                    regionsArray?.forEach { regionElement ->
                        val regionId = regionElement.asInt
                        targetColorRegions.add(regionId)
                        
                        // 检查该区域是否已填充
                        if (filledRegions.contains(regionId)) {
                            targetColorFilledRegions.add(regionId)
                        }
                    }
                }
            }
            
            val progressPercentage = if (targetColorRegions.isNotEmpty()) {
                (targetColorFilledRegions.size * 100) / targetColorRegions.size
            } else {
                0
            }
            
            val parseTime = System.currentTimeMillis() - parseStart
            Log.d(TAG, "📊 [性能] 颜色区域解析耗时: ${parseTime}ms (颜色: $targetColorHex)")
            
            return ColorRegionInfo(
                colorHex = targetColorHex,
                filledRegions = targetColorFilledRegions,
                totalRegionsForColor = targetColorRegions.size,
                progressPercentage = progressPercentage
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "颜色区域解析失败: $targetColorHex", e)
            throw e
        }
    }
    
    /**
     * 快速提取字符串字段
     */
    private fun extractStringField(jsonString: String, fieldName: String): String? {
        val regex = "\"$fieldName\"\\s*:\\s*\"([^\"]+)\"".toRegex()
        return regex.find(jsonString)?.groupValues?.get(1)
    }
    
    /**
     * 快速提取整数字段
     */
    private fun extractIntField(jsonString: String, fieldName: String): Int? {
        val regex = "\"$fieldName\"\\s*:\\s*(\\d+)".toRegex()
        return regex.find(jsonString)?.groupValues?.get(1)?.toIntOrNull()
    }
    
    /**
     * 快速提取长整数字段
     */
    private fun extractLongField(jsonString: String, fieldName: String): Long? {
        val regex = "\"$fieldName\"\\s*:\\s*(\\d+)".toRegex()
        return regex.find(jsonString)?.groupValues?.get(1)?.toLongOrNull()
    }
    
    /**
     * 快速提取布尔字段
     */
    private fun extractBooleanField(jsonString: String, fieldName: String): Boolean? {
        val regex = "\"$fieldName\"\\s*:\\s*(true|false)".toRegex()
        return regex.find(jsonString)?.groupValues?.get(1)?.toBoolean()
    }
    
    /**
     * 优化的filledRegions解析 - 高效解析数组但跳过复杂对象
     */
    private fun parseFilledRegionsOptimized(jsonString: String): Set<Int> {
        return try {
            val parseStart = System.currentTimeMillis()

            // 查找filledRegions数组的开始和结束位置
            val startIndex = jsonString.indexOf("\"filledRegions\"")
            if (startIndex == -1) return emptySet()

            val arrayStart = jsonString.indexOf("[", startIndex)
            if (arrayStart == -1) return emptySet()

            val arrayEnd = jsonString.indexOf("]", arrayStart)
            if (arrayEnd == -1) return emptySet()

            val arrayContent = jsonString.substring(arrayStart + 1, arrayEnd)

            // 高效解析数字数组
            val filledRegions = mutableSetOf<Int>()
            if (arrayContent.trim().isNotEmpty()) {
                // 使用正则表达式提取所有数字
                val numberRegex = "\\d+".toRegex()
                numberRegex.findAll(arrayContent).forEach { matchResult ->
                    val regionId = matchResult.value.toIntOrNull()
                    if (regionId != null) {
                        filledRegions.add(regionId)
                    }
                }
            }

            val parseTime = System.currentTimeMillis() - parseStart
            Log.d(TAG, "📊 [性能] filledRegions解析耗时: ${parseTime}ms (${filledRegions.size}个区域)")

            filledRegions
        } catch (e: Exception) {
            Log.e(TAG, "解析filledRegions失败", e)
            emptySet()
        }
    }

    /**
     * 快速计算已填充区域数量（备用方法）
     */
    private fun countFilledRegions(jsonString: String): Int {
        return try {
            // 查找filledRegions数组的开始和结束位置
            val startIndex = jsonString.indexOf("\"filledRegions\"")
            if (startIndex == -1) return 0

            val arrayStart = jsonString.indexOf("[", startIndex)
            if (arrayStart == -1) return 0

            val arrayEnd = jsonString.indexOf("]", arrayStart)
            if (arrayEnd == -1) return 0

            val arrayContent = jsonString.substring(arrayStart + 1, arrayEnd)

            // 简单计算逗号数量 + 1（如果不为空）
            if (arrayContent.trim().isEmpty()) {
                0
            } else {
                arrayContent.count { it == ',' } + 1
            }
        } catch (e: Exception) {
            Log.w(TAG, "计算已填充区域数量失败", e)
            0
        }
    }
    
    /**
     * 获取所有可用的颜色列表（轻量级）
     */
    fun getAvailableColors(jsonString: String): List<String> {
        return try {
            val jsonObject = jsonParser.parse(jsonString).asJsonObject
            val coloringData = jsonObject.getAsJsonObject("coloringData")
            val colorPalette = coloringData.getAsJsonArray("color_palette")
            
            val colors = mutableListOf<String>()
            colorPalette?.forEach { paletteElement ->
                val palette = paletteElement.asJsonObject
                val colorHex = palette.get("hex")?.asString
                if (colorHex != null) {
                    colors.add(colorHex)
                }
            }
            
            colors
        } catch (e: Exception) {
            Log.e(TAG, "获取颜色列表失败", e)
            emptyList()
        }
    }
}
