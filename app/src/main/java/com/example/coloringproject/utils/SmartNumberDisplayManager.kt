package com.example.coloringproject.utils

import android.graphics.*
import com.example.coloringproject.data.Region
import kotlin.math.*

/**
 * 智能数字显示管理器 - 优化版本
 * 根据区域实际大小和缩放比例智能显示数字标记
 */
class SmartNumberDisplayManager {
    
    companion object {
        private const val MIN_DISPLAY_SIZE_FOR_NUMBER = 30f // 最小显示尺寸阈值（像素）
        private const val MAX_NUMBERS_PER_SCREEN = 30 // 减少最大数字数量，提升性能
        private const val NUMBER_SIZE_MIN = 10f // 最小数字大小
        private const val NUMBER_SIZE_MAX = 24f // 最大数字大小
    }
    
    /**
     * 计算应该显示的数字标记（优化版本）
     */
    fun calculateVisibleNumbers(
        regions: List<Region>,
        currentScale: Float,
        visibleRect: RectF,
        selectedColorHex: String?,
        filledRegions: Set<Int> = emptySet()
    ): List<NumberDisplay> {
        
        val visibleNumbers = mutableListOf<NumberDisplay>()
        
        // 1. 筛选未填色的匹配颜色区域
        val candidateRegions = regions.filter { region ->
            !filledRegions.contains(region.id) && // 未填色
            (selectedColorHex == null || normalizeColorHex(region.colorHex) == normalizeColorHex(selectedColorHex)) &&
            isRegionVisible(region, visibleRect)
        }
        
        // 2. 按显示大小排序（大区域优先）
        val sortedRegions = candidateRegions
            .map { region -> Pair(region, calculateDisplaySize(region, currentScale)) }
            .filter { (_, displaySize) -> displaySize >= MIN_DISPLAY_SIZE_FOR_NUMBER }
            .sortedByDescending { (_, displaySize) -> displaySize }
            .take(MAX_NUMBERS_PER_SCREEN)
        
        // 3. 创建数字显示对象
        sortedRegions.forEach { (region, displaySize) ->
            val numberDisplay = createNumberDisplay(region, currentScale, displaySize)
            if (numberDisplay != null) {
                visibleNumbers.add(numberDisplay)
            }
        }
        
        return visibleNumbers
    }
    
    /**
     * 判断区域是否在可见范围内
     */
    private fun isRegionVisible(region: Region, visibleRect: RectF): Boolean {
        val regionBounds = getRegionBounds(region)
        return RectF.intersects(regionBounds, visibleRect)
    }
    
    /**
     * 判断是否应该为该区域显示数字
     */
//    private fun shouldShowNumberForRegion(
//        region: Region,
//        currentScale: Float,
//        selectedColorHex: String?
//    ): Boolean {
//        // 1. 检查区域面积
//        val regionArea = calculateRegionArea(region)
//        val scaledArea = regionArea * currentScale * currentScale
//
//        if (scaledArea < MIN_REGION_AREA_FOR_NUMBER) {
//            return false
//        }
//
//        // 2. 小区域需要更高的缩放比例才显示
//        if (regionArea < 500 && currentScale < MIN_SCALE_FOR_SMALL_NUMBERS) {
//            return false
//        }
//
//        // 3. 如果有选中颜色，优先显示匹配的颜色
//        if (selectedColorHex != null) {
//            return normalizeColorHex(region.colorHex) == normalizeColorHex(selectedColorHex)
//        }
//
//        // 4. 没有选中颜色时，显示所有符合条件的区域
//        return true
//    }
    
    /**
     * 创建数字显示对象（优化版本）
     */
    private fun createNumberDisplay(region: Region, currentScale: Float, displaySize: Float): NumberDisplay? {
        val center = calculateRegionCenter(region)
        
        // 根据实际显示大小计算数字大小
        val numberSize = calculateNumberSizeFromDisplaySize(displaySize)
        
        // 获取颜色编号
        val colorNumber = getColorNumber(region.colorHex)
        
        return NumberDisplay(
            regionId = region.id,
            centerX = center.x,
            centerY = center.y,
            number = colorNumber,
            textSize = numberSize,
            colorHex = region.colorHex
        )
    }
    
    /**
     * 计算区域的实际显示大小
     */
    private fun calculateDisplaySize(region: Region, currentScale: Float): Float {
        val bounds = getRegionBounds(region)
        val scaledWidth = bounds.width() * currentScale
        val scaledHeight = bounds.height() * currentScale
        
        // 返回较小的边长作为显示大小
        return min(scaledWidth, scaledHeight)
    }
    
    /**
     * 根据显示大小计算数字大小
     */
    private fun calculateNumberSizeFromDisplaySize(displaySize: Float): Float {
        // 数字大小与显示大小成正比，但有上下限
        val ratio = displaySize / 100f // 100像素对应基准大小
        val baseSize = 16f
        val calculatedSize = baseSize * ratio
        
        return calculatedSize.coerceIn(NUMBER_SIZE_MIN, NUMBER_SIZE_MAX)
    }
    
    /**
     * 计算区域面积
     */
    private fun calculateRegionArea(region: Region): Int {
        return region.pixels.size
    }
    
    /**
     * 计算区域中心点
     */
    private fun calculateRegionCenter(region: Region): PointF {
        if (region.pixels.isEmpty()) return PointF(0f, 0f)
        
        var sumX = 0f
        var sumY = 0f
        
        region.pixels.forEach { pixel ->
            sumX += pixel[0]
            sumY += pixel[1]
        }
        
        return PointF(
            sumX / region.pixels.size,
            sumY / region.pixels.size
        )
    }
    
    /**
     * 获取区域边界
     */
    private fun getRegionBounds(region: Region): RectF {
        if (region.boundingBox != null && region.boundingBox.size >= 4) {
            return RectF(
                region.boundingBox[0].toFloat(),
                region.boundingBox[1].toFloat(),
                region.boundingBox[2].toFloat(),
                region.boundingBox[3].toFloat()
            )
        }
        
        // 如果没有边界框，从像素计算
        if (region.pixels.isEmpty()) return RectF()
        
        var minX = Int.MAX_VALUE
        var minY = Int.MAX_VALUE
        var maxX = Int.MIN_VALUE
        var maxY = Int.MIN_VALUE
        
        region.pixels.forEach { pixel ->
            minX = min(minX, pixel[0])
            minY = min(minY, pixel[1])
            maxX = max(maxX, pixel[0])
            maxY = max(maxY, pixel[1])
        }
        
        return RectF(minX.toFloat(), minY.toFloat(), maxX.toFloat(), maxY.toFloat())
    }
    
    // 移除了旧的计算方法，使用新的基于显示大小的计算
    
    /**
     * 从颜色获取数字编号
     */
    private fun getColorNumber(colorHex: String): Int {
        // 这里需要根据项目的颜色映射表来实现
        // 临时实现：使用颜色的哈希值
        return kotlin.math.abs(colorHex.hashCode()) % 99 + 1
    }
    
    /**
     * 标准化颜色格式
     */
    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        if (normalized.length == 4) {
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }
}

/**
 * 数字显示数据类
 */
data class NumberDisplay(
    val regionId: Int,
    val centerX: Float,
    val centerY: Float,
    val number: Int,
    val textSize: Float,
    val colorHex: String
)