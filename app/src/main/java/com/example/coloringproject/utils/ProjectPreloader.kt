package com.example.coloringproject.utils

import android.content.Context
import android.util.Log
import com.example.coloringproject.manager.ProjectLoadManager
import kotlinx.coroutines.*

/**
 * 项目预加载器
 * 在后台预加载常用项目，减少用户等待时间
 */
class ProjectPreloader(private val context: Context) {
    
    private val TAG = "ProjectPreloader"
    private val projectLoadManager = ProjectLoadManager(context)
    private val preloadScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 预加载缓存
    private val preloadCache = mutableMapOf<String, ProjectLoadManager.LoadResult>()
    private val preloadingProjects = mutableSetOf<String>()
    
    /**
     * 预加载项目
     */
    fun preloadProject(projectId: String) {
        if (preloadCache.containsKey(projectId) || preloadingProjects.contains(projectId)) {
            Log.d(TAG, "项目已缓存或正在预加载: $projectId")
            return
        }
        
        preloadingProjects.add(projectId)
        Log.d(TAG, "开始预加载项目: $projectId")
        
        preloadScope.launch {
            try {
                val startTime = System.currentTimeMillis()
                val result = projectLoadManager.loadProject(projectId, "BUILT_IN")
                val loadTime = System.currentTimeMillis() - startTime
                
                preloadCache[projectId] = result
                preloadingProjects.remove(projectId)
                
                Log.d(TAG, "项目预加载完成: $projectId, 耗时: ${loadTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "项目预加载失败: $projectId", e)
                preloadingProjects.remove(projectId)
            }
        }
    }
    
    /**
     * 获取预加载的项目
     */
    fun getPreloadedProject(projectId: String): ProjectLoadManager.LoadResult? {
        val result = preloadCache[projectId]
        if (result != null) {
            Log.d(TAG, "使用预加载的项目: $projectId")
        }
        return result
    }
    
    // 智能预加载配置
    private val maxPreloadCount = getMaxPreloadCount()
    private val preloadPriority = listOf(
        "animal-1", "animal-2", "animal-3",      // 高优先级
        "building-1", "building-2", "building-3", // 中优先级
        "flower-1", "flower-2", "flower-3",      // 中优先级
        "mandala-1", "mandala-2", "mandala-3"    // 低优先级
    )
    
    /**
     * 根据设备性能确定最大预加载数量
     */
    private fun getMaxPreloadCount(): Int {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory() / 1024 / 1024 // MB
        
        return when {
            maxMemory >= 512 -> 12  // 高端设备：12个项目
            maxMemory >= 256 -> 8   // 中端设备：8个项目
            maxMemory >= 128 -> 5   // 低端设备：5个项目
            else -> 3               // 极低端设备：3个项目
        }.also {
            Log.d(TAG, "设备内存: ${maxMemory}MB, 最大预加载数量: $it")
        }
    }
    
    /**
     * 智能预加载常用项目
     */
    fun preloadCommonProjects() {
        Log.d(TAG, "开始智能预加载，最大数量: $maxPreloadCount")
        
        // 按优先级预加载，不超过设备限制
        preloadPriority.take(maxPreloadCount).forEachIndexed { index, projectId ->
            preloadScope.launch {
                delay(index * 200L) // 延迟预加载，避免资源竞争
                
                // 检查内存使用情况
                if (checkMemoryAvailable()) {
                    preloadProject(projectId)
                } else {
                    Log.w(TAG, "内存不足，停止预加载: $projectId")
                }
            }
        }
    }
    
    /**
     * 检查内存是否充足
     */
    private fun checkMemoryAvailable(): Boolean {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (usedMemory.toFloat() / maxMemory * 100).toInt()
        
        Log.d(TAG, "内存使用率: $memoryUsagePercent%")
        return memoryUsagePercent < 70 // 内存使用率低于70%时才继续预加载
    }
    
    /**
     * 智能清理缓存 - 基于内存压力
     */
    fun clearCache() {
        val beforeSize = preloadCache.size
        preloadCache.clear()
        preloadingProjects.clear()
        
        // 强制垃圾回收
        System.gc()
        
        Log.d(TAG, "预加载缓存已清理: ${beforeSize}个项目")
        logMemoryStatus("清理后")
    }
    
    /**
     * 智能内存管理 - 在内存压力大时清理低优先级项目
     */
    fun manageMemoryPressure() {
        if (!checkMemoryAvailable()) {
            Log.w(TAG, "内存压力大，开始清理低优先级预加载项目")
            
            // 清理低优先级项目（保留前5个高优先级）
            val highPriorityProjects = preloadPriority.take(5).toSet()
            val toRemove = preloadCache.keys.filter { !highPriorityProjects.contains(it) }
            
            toRemove.forEach { projectId ->
                preloadCache.remove(projectId)
                Log.d(TAG, "清理低优先级项目: $projectId")
            }
            
            if (toRemove.isNotEmpty()) {
                System.gc()
                Log.d(TAG, "清理了${toRemove.size}个低优先级项目")
                logMemoryStatus("内存管理后")
            }
        }
    }
    
    /**
     * 记录内存状态
     */
    private fun logMemoryStatus(stage: String) {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (usedMemory.toFloat() / maxMemory * 100).toInt()
        
        Log.d(TAG, "[$stage] 内存使用: ${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB ($memoryUsagePercent%)")
    }
    
    /**
     * 获取详细缓存状态
     */
    fun getCacheStatus(): String {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (usedMemory.toFloat() / maxMemory * 100).toInt()
        
        return buildString {
            appendLine("=== 预加载状态 ===")
            appendLine("缓存项目: ${preloadCache.size}/${maxPreloadCount}")
            appendLine("预加载中: ${preloadingProjects.size}")
            appendLine("内存使用: ${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB ($memoryUsagePercent%)")
            appendLine("缓存项目列表: ${preloadCache.keys.joinToString(", ")}")
        }
    }
    
    /**
     * 估算预加载内存占用
     */
    fun estimateMemoryUsage(): String {
        val estimatedMBPerProject = 5 // 平均每个项目5MB
        val currentEstimate = preloadCache.size * estimatedMBPerProject
        val maxEstimate = maxPreloadCount * estimatedMBPerProject
        
        return "预加载内存估算: ${currentEstimate}MB / ${maxEstimate}MB (每项目约${estimatedMBPerProject}MB)"
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        preloadScope.cancel()
        clearCache()
    }
    
    companion object {
        @Volatile
        private var INSTANCE: ProjectPreloader? = null
        
        fun getInstance(context: Context): ProjectPreloader {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ProjectPreloader(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
}