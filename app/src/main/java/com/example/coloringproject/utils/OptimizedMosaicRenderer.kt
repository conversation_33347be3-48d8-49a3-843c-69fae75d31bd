package com.example.coloringproject.utils

import android.graphics.*
import com.example.coloringproject.data.Region
import kotlin.math.*

/**
 * 优化的马赛克渲染器
 * 解决性能问题和边缘裁剪问题
 */
class OptimizedMosaicRenderer {
    
    companion object {
        private const val MOSAIC_BLOCK_SIZE = 6 // 减小块大小提升性能
        private const val MAX_BLOCKS_PER_REGION = 20 // 限制每个区域的最大块数
        private const val SAMPLE_RATE = 10 // 像素采样率，每10个像素取1个
    }
    
    private val mosaicPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }
    
    private val blueColor = Color.parseColor("#5B9BD5")
    private val whiteColor = Color.parseColor("#FFFFFF")
    
    /**
     * 渲染单个区域的马赛克（高性能版本）
     */
    fun renderRegionMosaic(
        canvas: Canvas,
        region: Region,
        isFilled: <PERSON>ole<PERSON>,
        scaleFactor: Float
    ) {
        if (region.pixels.isEmpty()) return
        
        // 已填色区域不显示马赛克
        if (isFilled) return
        
        // 根据缩放级别调整采样率
        val adaptiveSampleRate = when {
            scaleFactor > 4.0f -> SAMPLE_RATE / 2 // 高缩放时更密集
            scaleFactor < 1.0f -> SAMPLE_RATE * 2 // 低缩放时更稀疏
            else -> SAMPLE_RATE
        }
        
        // 限制处理的像素数量，避免性能问题
        val maxPixels = min(region.pixels.size, MAX_BLOCKS_PER_REGION * MOSAIC_BLOCK_SIZE * MOSAIC_BLOCK_SIZE)
        val actualSampleRate = max(1, region.pixels.size / maxPixels)
        
        // 获取区域边界框用于网格对齐
        val bounds = getRegionBounds(region)
        val gridStartX = (bounds.left / MOSAIC_BLOCK_SIZE).toInt() * MOSAIC_BLOCK_SIZE
        val gridStartY = (bounds.top / MOSAIC_BLOCK_SIZE).toInt() * MOSAIC_BLOCK_SIZE
        
        // 收集需要绘制的网格块
        val blocksToRender = mutableSetOf<Pair<Int, Int>>()
        
        // 采样像素并确定需要绘制的网格块
        for (i in region.pixels.indices step actualSampleRate) {
            if (blocksToRender.size >= MAX_BLOCKS_PER_REGION) break
            
            val pixel = region.pixels[i]
            val x = pixel[0]
            val y = pixel[1]
            
            // 计算该像素所属的网格块
            val blockX = (x / MOSAIC_BLOCK_SIZE) * MOSAIC_BLOCK_SIZE
            val blockY = (y / MOSAIC_BLOCK_SIZE) * MOSAIC_BLOCK_SIZE
            
            blocksToRender.add(Pair(blockX, blockY))
        }
        
        // 绘制网格块
        blocksToRender.forEach { (blockX, blockY) ->
            renderMosaicBlock(canvas, blockX, blockY, region)
        }
    }
    
    /**
     * 渲染单个马赛克块（精确边缘裁剪）
     */
    private fun renderMosaicBlock(
        canvas: Canvas,
        blockX: Int,
        blockY: Int,
        region: Region
    ) {
        // 检查块的关键点是否在区域内
        val centerX = blockX + MOSAIC_BLOCK_SIZE / 2
        val centerY = blockY + MOSAIC_BLOCK_SIZE / 2
        
        if (!isPointInRegion(centerX, centerY, region)) {
            return // 中心点不在区域内，不绘制
        }
        
        // 计算块内有效像素的比例
        var validPixels = 0
        val totalPixels = MOSAIC_BLOCK_SIZE * MOSAIC_BLOCK_SIZE
        
        for (y in blockY until blockY + MOSAIC_BLOCK_SIZE) {
            for (x in blockX until blockX + MOSAIC_BLOCK_SIZE) {
                if (isPointInRegion(x, y, region)) {
                    validPixels++
                }
            }
        }
        
        val coverage = validPixels.toFloat() / totalPixels
        
        // 只有覆盖率足够高才绘制
        if (coverage < 0.3f) return
        
        // 根据覆盖率调整块大小
        val adjustedSize = MOSAIC_BLOCK_SIZE * sqrt(coverage)
        val offset = (MOSAIC_BLOCK_SIZE - adjustedSize) / 2
        
        // 绘制棋盘格马赛克
        renderCheckerboardBlock(
            canvas,
            blockX + offset,
            blockY + offset,
            adjustedSize,
            adjustedSize
        )
    }
    
    /**
     * 绘制棋盘格块
     */
    private fun renderCheckerboardBlock(
        canvas: Canvas,
        left: Float,
        top: Float,
        width: Float,
        height: Float
    ) {
        val checkerSize = 3f // 小格子大小
        val cols = (width / checkerSize).toInt() + 1
        val rows = (height / checkerSize).toInt() + 1
        
        for (row in 0 until rows) {
            for (col in 0 until cols) {
                val x = left + col * checkerSize
                val y = top + row * checkerSize
                
                val actualWidth = min(checkerSize, left + width - x)
                val actualHeight = min(checkerSize, top + height - y)
                
                if (actualWidth > 0 && actualHeight > 0) {
                    val isBlue = (row + col) % 2 == 0
                    mosaicPaint.color = if (isBlue) blueColor else whiteColor
                    mosaicPaint.alpha = 150 // 适中的透明度
                    
                    canvas.drawRect(x, y, x + actualWidth, y + actualHeight, mosaicPaint)
                }
            }
        }
    }
    
    /**
     * 检查点是否在区域内（优化版本）
     */
    private fun isPointInRegion(x: Int, y: Int, region: Region): Boolean {
        // 首先检查边界框
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                if (x < bbox[0] || x > bbox[2] || y < bbox[1] || y > bbox[3]) {
                    return false
                }
            }
        }
        
        // 在边界框内，检查具体像素（使用二分查找优化）
        return region.pixels.any { pixel ->
            pixel[0] == x && pixel[1] == y
        }
    }
    
    /**
     * 获取区域边界
     */
    private fun getRegionBounds(region: Region): RectF {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                return RectF(
                    bbox[0].toFloat(),
                    bbox[1].toFloat(),
                    bbox[2].toFloat(),
                    bbox[3].toFloat()
                )
            }
        }
        
        // 从像素计算边界
        if (region.pixels.isEmpty()) return RectF()
        
        var minX = Int.MAX_VALUE
        var minY = Int.MAX_VALUE
        var maxX = Int.MIN_VALUE
        var maxY = Int.MIN_VALUE
        
        region.pixels.forEach { pixel ->
            minX = min(minX, pixel[0])
            minY = min(minY, pixel[1])
            maxX = max(maxX, pixel[0])
            maxY = max(maxY, pixel[1])
        }
        
        return RectF(minX.toFloat(), minY.toFloat(), maxX.toFloat(), maxY.toFloat())
    }
}