package com.example.coloringproject.utils

import android.graphics.*
import android.os.Build
import com.example.coloringproject.data.Region
import kotlin.math.*

/**
 * 对齐马赛克渲染器
 * 确保马赛克块保持XY坐标垂直对齐，边缘与涂色区域一致
 */
class AlignedMosaicRenderer {
    
    companion object {
        private const val MOSAIC_BLOCK_SIZE = 8 // 马赛克块大小（像素）
        private const val BORDER_WIDTH = 1f // 边框宽度
        private const val ALPHA_FILLED = 0.3f // 已填充区域的透明度
        private const val ALPHA_UNFILLED = 0.6f // 未填充区域的透明度
    }
    
    private val mosaicPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }
    
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = BORDER_WIDTH
        color = Color.parseColor("#4A90C2")
    }
    
    /**
     * 渲染对齐的马赛克提醒
     */
    fun renderAlignedMosaic(
        canvas: Canvas,
        regions: List<Region>,
        filledRegions: Set<Int>,
        matrix: Matrix,
        visibleRect: RectF
    ) {
        // 1. 计算当前缩放比例
        val scaleValues = FloatArray(9)
        matrix.getValues(scaleValues)
        val currentScale = scaleValues[Matrix.MSCALE_X]
        
        // 2. 计算适合当前缩放的马赛克块大小
        val adaptiveBlockSize = calculateAdaptiveBlockSize(currentScale)
        
        // 3. 为每个区域渲染对齐的马赛克
        regions.forEach { region ->
            val isFilled = filledRegions.contains(region.id)
            renderRegionMosaic(canvas, region, isFilled, adaptiveBlockSize, matrix, visibleRect)
        }
    }
    
    /**
     * 计算自适应马赛克块大小
     */
    private fun calculateAdaptiveBlockSize(scale: Float): Int {
        // 根据缩放比例调整马赛克块大小，保持视觉一致性
        val baseSize = MOSAIC_BLOCK_SIZE
        val scaledSize = kotlin.math.round(baseSize / scale).toInt()
        return scaledSize.coerceIn(4, 16) // 限制在合理范围内
    }
    
    /**
     * 渲染单个区域的马赛克
     */
    private fun renderRegionMosaic(
        canvas: Canvas,
        region: Region,
        isFilled: Boolean,
        blockSize: Int,
        matrix: Matrix,
        visibleRect: RectF
    ) {
        // 1. 获取区域边界
        val bounds = getRegionBounds(region)
        
        // 2. 检查是否在可见区域内
        if (!RectF.intersects(bounds, visibleRect)) {
            return
        }
        
        // 3. 计算对齐的网格起始点
        val gridStartX = (bounds.left / blockSize).toInt() * blockSize
        val gridStartY = (bounds.top / blockSize).toInt() * blockSize
        
        // 4. 创建区域像素的快速查找集合
        val pixelSet = region.pixels.map { "${it[0]},${it[1]}" }.toSet()
        
        // 5. 设置马赛克颜色和透明度
        val baseColor = Color.parseColor(region.colorHex)
        val alpha = if (isFilled) ALPHA_FILLED else ALPHA_UNFILLED
        mosaicPaint.color = Color.argb(
            (255 * alpha).toInt(),
            Color.red(baseColor),
            Color.green(baseColor),
            Color.blue(baseColor)
        )
        
        // 6. 渲染对齐的马赛克块
        var y = gridStartY
        while (y <= bounds.bottom) {
            var x = gridStartX
            while (x <= bounds.right) {
                if (shouldRenderMosaicBlock(x, y, blockSize, pixelSet)) {
                    renderMosaicBlock(canvas, x.toFloat(), y.toFloat(), blockSize.toFloat(), matrix)
                }
                x += blockSize
            }
            y += blockSize
        }
    }
    
    /**
     * 判断是否应该渲染马赛克块
     */
    private fun shouldRenderMosaicBlock(
        blockX: Int,
        blockY: Int,
        blockSize: Int,
        pixelSet: Set<String>
    ): Boolean {
        // 检查马赛克块内是否有足够的区域像素
        var pixelCount = 0
        val threshold = (blockSize * blockSize * 0.3).toInt() // 30%覆盖率阈值
        
        for (y in blockY until blockY + blockSize) {
            for (x in blockX until blockX + blockSize) {
                if (pixelSet.contains("$x,$y")) {
                    pixelCount++
                    if (pixelCount >= threshold) {
                        return true
                    }
                }
            }
        }
        
        return false
    }
    
    /**
     * 渲染单个马赛克块
     */
    private fun renderMosaicBlock(
        canvas: Canvas,
        x: Float,
        y: Float,
        size: Float,
        matrix: Matrix
    ) {
        // 1. 创建马赛克块矩形
        val blockRect = RectF(x, y, x + size, y + size)
        
        // 2. 应用变换矩阵
        matrix.mapRect(blockRect)
        
        // 3. 绘制填充的马赛克块
        canvas.drawRect(blockRect, mosaicPaint)
        
        // 4. 绘制边框（可选）
        if (size > 6) { // 只在块足够大时绘制边框
            canvas.drawRect(blockRect, borderPaint)
        }
    }
    
    /**
     * 渲染精确边缘马赛克（用于边界区域）
     */
    fun renderPreciseEdgeMosaic(
        canvas: Canvas,
        region: Region,
        isFilled: Boolean,
        matrix: Matrix
    ) {
        // 颜色设置保持不变
        val baseColor = Color.parseColor(region.colorHex)
        val alpha = if (isFilled) ALPHA_FILLED else ALPHA_UNFILLED
        mosaicPaint.color = Color.argb(
            (255 * alpha).toInt(),
            Color.red(baseColor),
            Color.green(baseColor),
            Color.blue(baseColor)
        )

        // 路径生成与变换
        val path = createRegionPath(region)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            path.transform(matrix) // 优先使用高效原生方法
        } else {
            transformPath(matrix, path, path) // 兼容旧版本
        }

        // 绘制
        canvas.drawPath(path, mosaicPaint)
    }

    // 替代 matrix.mapPath(path, path) 的实现
    fun transformPath(matrix: Matrix, srcPath: Path, dstPath: Path) {
        val points = PathMeasure(srcPath, false).let { measure ->
            FloatArray(measure.length.toInt() * 2).apply {
                measure.getPosTan(measure.length * 0.5f, this, null)
            }
        }

        // 应用矩阵变换到所有点
        matrix.mapPoints(points)

        // 重新构建路径
        dstPath.reset()
        points.forEachIndexed { index, value ->
            if (index % 2 == 0) {
                if (index == 0) dstPath.moveTo(value, points[index + 1])
                else dstPath.lineTo(value, points[index + 1])
            }
        }
        dstPath.close()
    }

    /**
     * 创建区域路径
     */
    private fun createRegionPath(region: Region): Path {
        val path = Path()
        
        if (region.pixels.isEmpty()) return path
        
        // 简化实现：使用边界框
        val bounds = getRegionBounds(region)
        path.addRect(bounds, Path.Direction.CW)
        
        return path
    }
    
    /**
     * 获取区域边界
     */
    private fun getRegionBounds(region: Region): RectF {
        if (region.boundingBox != null && region.boundingBox.size >= 4) {
            return RectF(
                region.boundingBox[0].toFloat(),
                region.boundingBox[1].toFloat(),
                region.boundingBox[2].toFloat(),
                region.boundingBox[3].toFloat()
            )
        }
        
        // 从像素计算边界
        if (region.pixels.isEmpty()) return RectF()
        
        var minX = Int.MAX_VALUE
        var minY = Int.MAX_VALUE
        var maxX = Int.MIN_VALUE
        var maxY = Int.MIN_VALUE
        
        region.pixels.forEach { pixel ->
            minX = min(minX, pixel[0])
            minY = min(minY, pixel[1])
            maxX = max(maxX, pixel[0])
            maxY = max(maxY, pixel[1])
        }
        
        return RectF(minX.toFloat(), minY.toFloat(), maxX.toFloat(), maxY.toFloat())
    }
    
    /**
     * 设置马赛克样式
     */
    fun setMosaicStyle(blockSize: Int, borderWidth: Float, borderColor: Int) {
        borderPaint.strokeWidth = borderWidth
        borderPaint.color = borderColor
    }
}