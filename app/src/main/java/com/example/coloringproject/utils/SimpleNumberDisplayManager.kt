package com.example.coloringproject.utils

import android.graphics.*
import com.example.coloringproject.data.Region
import kotlin.math.*

/**
 * 简化的数字显示管理器
 * 专注解决核心问题：正确的颜色映射和大小计算
 */
class SimpleNumberDisplayManager {

    companion object {
        private const val MIN_SCALE_FOR_NUMBERS = 1.2f
        private const val MIN_REGION_SIZE_FOR_NUMBER = 25f
        private const val BASE_NUMBER_SIZE = 14f
        private const val MAX_NUMBER_SIZE = 28f
        private const val MIN_NUMBER_SIZE = 8f
    }

    // 缓存颜色映射，避免重复计算
    private var cachedColorMapping: Map<String, Int>? = null
    private var cachedRegionsHash: Int = 0
    
    /**
     * 计算应该显示的数字（简化版本）
     */
    fun calculateNumbersToDisplay(
        regions: List<Region>,
        filledRegions: Set<Int>,
        currentScale: Float,
        selectedColorHex: String?,
        visibleRect: RectF
    ): List<NumberDisplayInfo> {
        
        android.util.Log.d("SimpleNumberDisplay", "=== 开始计算数字显示 ===")
        android.util.Log.d("SimpleNumberDisplay", "缩放级别: $currentScale")
        android.util.Log.d("SimpleNumberDisplay", "选中颜色: $selectedColorHex")
        
        // 缩放级别太小时不显示数字
        if (currentScale < MIN_SCALE_FOR_NUMBERS) {
            android.util.Log.d("SimpleNumberDisplay", "缩放级别太小，不显示数字")
            return emptyList()
        }
        
        // 创建或获取缓存的颜色到数字的映射
        val colorMapping = getOrCreateColorMapping(regions)
        android.util.Log.d("SimpleNumberDisplay", "颜色映射: $colorMapping")
        
        // 筛选所有未填色的可见区域（不限制颜色）
        val candidateRegions = regions.filter { region ->
            val isNotFilled = !filledRegions.contains(region.id)
            val isVisible = isRegionVisible(region, visibleRect)
            
            android.util.Log.d("SimpleNumberDisplay", "区域${region.id}: 颜色=${normalizeColorHex(region.colorHex)}, 未填色=$isNotFilled, 可见=$isVisible")
            
            isNotFilled && isVisible
        }
        
        android.util.Log.d("SimpleNumberDisplay", "匹配的区域数量: ${candidateRegions.size}")
        
        // 计算每个区域的显示信息
        val numbersToDisplay = mutableListOf<NumberDisplayInfo>()
        
        candidateRegions.forEach { region ->
            val displaySize = calculateRegionDisplaySize(region, currentScale)
            
            android.util.Log.d("SimpleNumberDisplay", "区域${region.id}: 显示大小=${displaySize.toInt()}px")
            
            // 只有足够大的区域才显示数字
            if (displaySize >= MIN_REGION_SIZE_FOR_NUMBER) {
                val center = calculateRegionCenter(region)
                val numberSize = calculateNumberSize(displaySize)
                val regionColor = normalizeColorHex(region.colorHex)
                val colorNumber = colorMapping[regionColor] ?: 1
                
                android.util.Log.d("SimpleNumberDisplay", "区域${region.id}: 颜色=$regionColor, 数字=$colorNumber, 数字大小=${numberSize.toInt()}px")
                
                numbersToDisplay.add(
                    NumberDisplayInfo(
                        regionId = region.id,
                        centerX = center.x,
                        centerY = center.y,
                        number = colorNumber,
                        textSize = numberSize,
                        displaySize = displaySize
                    )
                )
            } else {
                android.util.Log.d("SimpleNumberDisplay", "区域${region.id}: 太小，不显示数字")
            }
        }
        
        android.util.Log.d("SimpleNumberDisplay", "最终显示数字数量: ${numbersToDisplay.size}")
        
        return numbersToDisplay.sortedByDescending { it.displaySize }
    }
    
    /**
     * 获取或创建颜色到数字的映射（带缓存）
     */
    private fun getOrCreateColorMapping(regions: List<Region>): Map<String, Int> {
        val currentHash = regions.hashCode()

        // 如果缓存有效，直接返回
        if (cachedColorMapping != null && cachedRegionsHash == currentHash) {
            android.util.Log.d("SimpleNumberDisplay", "使用缓存的颜色映射")
            return cachedColorMapping!!
        }

        // 创建新的映射并缓存
        val mapping = createColorMapping(regions)
        cachedColorMapping = mapping
        cachedRegionsHash = currentHash

        android.util.Log.d("SimpleNumberDisplay", "创建并缓存新的颜色映射")
        return mapping
    }

    /**
     * 创建颜色到数字的映射
     */
    private fun createColorMapping(regions: List<Region>): Map<String, Int> {
        android.util.Log.d("SimpleNumberDisplay", "开始创建颜色映射，总区域数: ${regions.size}")
        
        // 收集所有原始颜色
        val rawColors = regions.map { it.colorHex }
        android.util.Log.d("SimpleNumberDisplay", "原始颜色前10个: ${rawColors.take(10)}")
        
        // 标准化颜色
        val normalizedColors = regions.map { normalizeColorHex(it.colorHex) }
        android.util.Log.d("SimpleNumberDisplay", "标准化颜色前10个: ${normalizedColors.take(10)}")
        
        val uniqueColors = normalizedColors.distinct().sorted()
        android.util.Log.d("SimpleNumberDisplay", "唯一颜色数量: ${uniqueColors.size}")
        
        val mapping = uniqueColors.mapIndexed { index, color ->
            color to (index + 1)
        }.toMap()
        
        android.util.Log.d("SimpleNumberDisplay", "颜色映射创建完成:")
        mapping.forEach { (color, number) ->
            android.util.Log.d("SimpleNumberDisplay", "  $color -> $number")
        }
        
        return mapping
    }
    
    /**
     * 计算区域的显示大小
     */
    private fun calculateRegionDisplaySize(region: Region, scale: Float): Float {
        val bounds = getRegionBounds(region)
        val rawSize = min(bounds.width(), bounds.height())
        return rawSize * scale
    }
    
    /**
     * 根据显示大小计算数字大小
     */
    private fun calculateNumberSize(displaySize: Float): Float {
        val ratio = displaySize / 100f
        val calculatedSize = BASE_NUMBER_SIZE * sqrt(ratio)
        return calculatedSize.coerceIn(MIN_NUMBER_SIZE, MAX_NUMBER_SIZE)
    }
    
    /**
     * 计算区域中心点
     */
    private fun calculateRegionCenter(region: Region): PointF {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                return PointF(
                    (bbox[0] + bbox[2]) / 2f,
                    (bbox[1] + bbox[3]) / 2f
                )
            }
        }
        
        if (region.pixels.isEmpty()) return PointF(0f, 0f)
        
        var sumX = 0f
        var sumY = 0f
        region.pixels.forEach { pixel ->
            sumX += pixel[0]
            sumY += pixel[1]
        }
        
        return PointF(sumX / region.pixels.size, sumY / region.pixels.size)
    }
    
    /**
     * 获取区域边界
     */
    private fun getRegionBounds(region: Region): RectF {
        region.boundingBox?.let { bbox ->
            if (bbox.size >= 4) {
                return RectF(
                    bbox[0].toFloat(),
                    bbox[1].toFloat(),
                    bbox[2].toFloat(),
                    bbox[3].toFloat()
                )
            }
        }
        
        if (region.pixels.isEmpty()) return RectF()
        
        var minX = Int.MAX_VALUE
        var minY = Int.MAX_VALUE
        var maxX = Int.MIN_VALUE
        var maxY = Int.MIN_VALUE
        
        region.pixels.forEach { pixel ->
            minX = min(minX, pixel[0])
            minY = min(minY, pixel[1])
            maxX = max(maxX, pixel[0])
            maxY = max(maxY, pixel[1])
        }
        
        return RectF(minX.toFloat(), minY.toFloat(), maxX.toFloat(), maxY.toFloat())
    }
    
    /**
     * 检查区域是否在可见范围内
     */
    private fun isRegionVisible(region: Region, visibleRect: RectF): Boolean {
        val regionBounds = getRegionBounds(region)
        return RectF.intersects(regionBounds, visibleRect)
    }
    
    /**
     * 标准化颜色格式
     */
    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        if (normalized.length == 4) {
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }
    
    /**
     * 绘制数字
     */
    fun drawNumbers(canvas: Canvas, numbers: List<NumberDisplayInfo>) {
        val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }
        
        val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.WHITE
            alpha = 230
        }
        
        val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.GRAY
            style = Paint.Style.STROKE
            strokeWidth = 1f
        }
        
        numbers.forEach { numberInfo ->
            textPaint.textSize = numberInfo.textSize
            
            val numberText = numberInfo.number.toString()
            val textBounds = Rect()
            textPaint.getTextBounds(numberText, 0, numberText.length, textBounds)
            
            val radius = max(textBounds.width(), textBounds.height()) / 2f + 4f
            
            canvas.drawCircle(numberInfo.centerX, numberInfo.centerY, radius, backgroundPaint)
            canvas.drawCircle(numberInfo.centerX, numberInfo.centerY, radius, borderPaint)
            canvas.drawText(
                numberText,
                numberInfo.centerX,
                numberInfo.centerY + textBounds.height() / 2f,
                textPaint
            )
        }
    }
}