package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.google.gson.Gson
import com.google.gson.JsonParser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileNotFoundException
import java.io.IOException
import java.io.InputStream

/**
 * 增强的资源管理器 - 确保JSON和PNG文件成对匹配
 * 只有成对的文件才被认为是有效的填色项目
 */
class EnhancedAssetManager(private val context: Context) {

    private val gson = Gson()
    private val TAG = "EnhancedAssetManager"

    /**
     * 验证的填色项目
     */
    data class ValidatedProject(
        val id: String,
        val name: String,
        val jsonFile: String,
        val outlineFile: String,
        val regionFile: String? = null, // 可选的区域图片
        val difficulty: String,
        val totalRegions: Int,
        val totalColors: Int,
        val estimatedTime: Int,
        val fileSize: ProjectFileSize,
        val isValid: Boolean,
        val validationErrors: List<String> = emptyList()
    )

    /**
     * 项目文件大小信息
     */
    data class ProjectFileSize(
        val jsonSizeKB: Long,
        val outlineSizeKB: Long,
        val regionSizeKB: Long? = null,
        val totalSizeKB: Long
    )

    /**
     * 获取所有验证过的项目列表
     * 只返回JSON和PNG成对匹配的有效项目
     */
    suspend fun getValidatedProjects(): Result<List<ValidatedProject>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始扫描和验证assets中的填色项目")
            
            val allAssets = context.assets.list("") ?: emptyArray()
            Log.d(TAG, "找到 ${allAssets.size} 个assets文件")
            
            val projects = mutableListOf<ValidatedProject>()
            val processedBasenames = mutableSetOf<String>()
            
            // 找到所有JSON文件
            val jsonFiles = allAssets.filter { it.endsWith(".json") }
            Log.d(TAG, "找到 ${jsonFiles.size} 个JSON文件: ${jsonFiles.joinToString()}")
            
            for (jsonFile in jsonFiles) {
                val basename = extractBasename(jsonFile)
                
                if (processedBasenames.contains(basename)) {
                    Log.d(TAG, "跳过已处理的basename: $basename")
                    continue
                }
                
                processedBasenames.add(basename)
                
                Log.d(TAG, "验证项目: $basename (JSON: $jsonFile)")
                
                // 查找匹配的PNG文件
                val matchingFiles = findMatchingFiles(basename, allAssets)
                
                // 验证项目
                val validatedProject = validateProject(basename, jsonFile, matchingFiles)
                projects.add(validatedProject)
                
                if (validatedProject.isValid) {
                    Log.i(TAG, "✅ 有效项目: ${validatedProject.name}")
                } else {
                    Log.w(TAG, "❌ 无效项目: ${validatedProject.name}, 错误: ${validatedProject.validationErrors}")
                }
            }
            
            // 按有效性和名称排序
            val sortedProjects = projects.sortedWith(
                compareByDescending<ValidatedProject> { it.isValid }
                    .thenBy { it.name }
            )
            
            val validCount = sortedProjects.count { it.isValid }
            val invalidCount = sortedProjects.size - validCount
            
            Log.i(TAG, "项目扫描完成: 总计 ${sortedProjects.size} 个项目, 有效 $validCount 个, 无效 $invalidCount 个")
            
            Result.success(sortedProjects)
            
        } catch (e: Exception) {
            Log.e(TAG, "扫描项目时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 提取文件的基础名称
     */
    private fun extractBasename(filename: String): String {
        return filename
            .removeSuffix(".json")
            .removeSuffix("_compressed")
            .removeSuffix("_android")
            .removeSuffix("_optimized")
            .removeSuffix("_ultra")
    }

    /**
     * 查找匹配的文件
     */
    private fun findMatchingFiles(basename: String, allAssets: Array<String>): MatchingFiles {
        val possibleOutlineNames = listOf(
            "${basename}_outline.png",
            "${basename}_android_outline.png",
            "${basename}_compressed_outline.png",
            "${basename}.png"
        )
        
        val possibleRegionNames = listOf(
            "${basename}_regions.png",
            "${basename}_android_regions.png",
            "${basename}_region.png"
        )
        
        val outlineFile = possibleOutlineNames.firstOrNull { name ->
            allAssets.contains(name)
        }
        
        val regionFile = possibleRegionNames.firstOrNull { name ->
            allAssets.contains(name)
        }
        
        return MatchingFiles(outlineFile, regionFile)
    }

    /**
     * 匹配的文件
     */
    private data class MatchingFiles(
        val outlineFile: String?,
        val regionFile: String?
    )

    /**
     * 验证项目的完整性
     */
    private suspend fun validateProject(
        basename: String, 
        jsonFile: String, 
        matchingFiles: MatchingFiles
    ): ValidatedProject {
        val errors = mutableListOf<String>()
        var metadata: com.example.coloringproject.data.Metadata? = null
        var fileSize: ProjectFileSize? = null
        
        // 1. 验证JSON文件
        try {
            val jsonResult = loadColoringData(jsonFile)
            if (jsonResult.isSuccess) {
                val coloringData = jsonResult.getOrNull()!!
                metadata = coloringData.metadata
                
                // 验证JSON内容
                if (coloringData.regions.isEmpty()) {
                    errors.add("JSON文件中没有区域数据")
                }
                if (coloringData.colorPalette.isEmpty()) {
                    errors.add("JSON文件中没有调色板数据")
                }
                
                Log.d(TAG, "JSON验证成功: $jsonFile (${coloringData.regions.size}个区域)")
            } else {
                errors.add("JSON文件解析失败: ${jsonResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            errors.add("JSON文件读取失败: ${e.message}")
        }
        
        // 2. 验证outline PNG文件
        if (matchingFiles.outlineFile == null) {
            errors.add("缺少outline PNG文件")
        } else {
            try {
                val bitmapResult = loadOutlineBitmap(matchingFiles.outlineFile)
                if (bitmapResult.isSuccess) {
                    val bitmap = bitmapResult.getOrNull()!!
                    Log.d(TAG, "Outline验证成功: ${matchingFiles.outlineFile} (${bitmap.width}x${bitmap.height})")
                } else {
                    errors.add("Outline PNG文件加载失败: ${bitmapResult.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                errors.add("Outline PNG文件读取失败: ${e.message}")
            }
        }
        
        // 3. 验证region PNG文件（可选）
        if (matchingFiles.regionFile != null) {
            try {
                val bitmapResult = loadRegionBitmap(matchingFiles.regionFile)
                if (bitmapResult.isSuccess) {
                    Log.d(TAG, "Region验证成功: ${matchingFiles.regionFile}")
                } else {
                    Log.w(TAG, "Region PNG文件加载失败: ${bitmapResult.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Region PNG文件读取失败: ${e.message}")
            }
        }
        
        // 4. 计算文件大小
        try {
            val jsonSize = getAssetSize(jsonFile)
            val outlineSize = matchingFiles.outlineFile?.let { getAssetSize(it) } ?: 0L
            val regionSize = matchingFiles.regionFile?.let { getAssetSize(it) }
            
            fileSize = ProjectFileSize(
                jsonSizeKB = jsonSize / 1024,
                outlineSizeKB = outlineSize / 1024,
                regionSizeKB = regionSize?.let { it / 1024 },
                totalSizeKB = (jsonSize + outlineSize + (regionSize ?: 0L)) / 1024
            )
        } catch (e: Exception) {
            Log.w(TAG, "计算文件大小失败: ${e.message}")
        }
        
        // 5. 构建项目信息
        val isValid = errors.isEmpty() && matchingFiles.outlineFile != null
        val displayName = basename.replace("_", " ").replaceFirstChar { it.uppercase() }
        
        return ValidatedProject(
            id = basename,
            name = displayName,
            jsonFile = jsonFile,
            outlineFile = matchingFiles.outlineFile ?: "",
            regionFile = matchingFiles.regionFile,
            difficulty = metadata?.difficulty ?: "unknown",
            totalRegions = metadata?.totalRegions ?: 0,
            totalColors = metadata?.totalColors ?: 0,
            estimatedTime = metadata?.estimatedTimeMinutes ?: 0,
            fileSize = fileSize ?: ProjectFileSize(0, 0, null, 0),
            isValid = isValid,
            validationErrors = errors
        )
    }

    /**
     * 加载填色数据（复用SimpleAssetManager的逻辑）
     */
    suspend fun loadColoringData(jsonFileName: String): Result<ColoringData> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Loading JSON file: $jsonFileName")

            // 检查是否是下载的文件（包含完整路径）
            val jsonString = if (jsonFileName.startsWith("/") || jsonFileName.contains("downloads")) {
                // 下载的文件，使用File读取
                val file = if (jsonFileName.startsWith("/")) {
                    File(jsonFileName)
                } else {
                    File(context.filesDir, "downloads/$jsonFileName")
                }
                if (file.exists()) {
                    file.readText()
                } else {
                    Log.e(TAG, "Downloaded file not found: ${file.absolutePath}")
                    throw FileNotFoundException("Downloaded file not found: ${file.absolutePath}")
                }
            } else {
                // Assets文件，使用完全静默的智能查找方式
                val inputStream = findAssetFileQuietly(jsonFileName)
                inputStream.bufferedReader().use { it.readText() }
            }
            
            if (jsonString.isBlank()) {
                throw IllegalArgumentException("JSON file is empty: $jsonFileName")
            }

            val jsonObject = JsonParser.parseString(jsonString).asJsonObject
            val isUltraCompressed = jsonObject.has("meta") && jsonObject.has("regions") && jsonObject.has("palette")
            val metadata = jsonObject.getAsJsonObject("metadata")
            val isOptimized = metadata?.has("compression") == true

            val coloringData = when {
                isUltraCompressed -> {
                    SimpleAssetManager(context).run {
                        val method = this::class.java.getDeclaredMethod("convertUltraCompressedToColoringData", com.google.gson.JsonObject::class.java)
                        method.isAccessible = true
                        method.invoke(this, jsonObject) as ColoringData
                    }
                }
                isOptimized -> {
                    SimpleAssetManager(context).run {
                        val method = this::class.java.getDeclaredMethod("convertOptimizedJsonToColoringData", com.google.gson.JsonObject::class.java)
                        method.isAccessible = true
                        method.invoke(this, jsonObject) as ColoringData
                    }
                }
                else -> {
                    gson.fromJson(jsonString, ColoringData::class.java)
                }
            }
            
            if (coloringData?.metadata == null || coloringData.regions.isEmpty() || coloringData.colorPalette.isEmpty()) {
                throw IllegalArgumentException("Invalid JSON structure: $jsonFileName")
            }

            Result.success(coloringData)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error loading coloring data: $jsonFileName", e)
            Result.failure(e)
        }
    }

    /**
     * 加载outline图片 - 优化版本（模块1：内存优化）
     */
    suspend fun loadOutlineBitmap(imageFileName: String): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载outline图片: $imageFileName")
            val startTime = System.currentTimeMillis()

            val bitmap = if (imageFileName.startsWith("/") || imageFileName.contains("downloads")) {
                // 下载的文件，使用File读取
                val file = if (imageFileName.startsWith("/")) {
                    File(imageFileName)
                } else {
                    File(context.filesDir, "downloads/$imageFileName")
                }
                if (file.exists()) {
                    // 使用BufferedInputStream来支持mark/reset
                    file.inputStream().buffered().use { bufferedInputStream ->
                        BitmapUtils.decodeColoringBitmap(bufferedInputStream, maxSize = 1024)
                    }
                } else {
                    Log.e(TAG, "Downloaded image file not found: ${file.absolutePath}")
                    throw FileNotFoundException("Downloaded image file not found: ${file.absolutePath}")
                }
            } else {
                // Assets文件，使用完全静默的智能查找方式
                val inputStream = findAssetFileQuietly(imageFileName)
                inputStream.buffered().use { bufferedStream ->
                    BitmapUtils.decodeColoringBitmap(bufferedStream, maxSize = 1024)
                }
            }

            if (bitmap == null) {
                throw IllegalArgumentException("Failed to decode image: $imageFileName")
            }

            val loadTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "Outline图片加载完成: $imageFileName, 耗时: ${loadTime}ms, 大小: ${bitmap.width}x${bitmap.height}")

            Result.success(bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "加载outline图片失败: $imageFileName", e)
            Result.failure(e)
        }
    }

    /**
     * 加载region图片 - 优化版本（模块1：内存优化）
     */
    suspend fun loadRegionBitmap(imageFileName: String): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载region图片: $imageFileName")
            val startTime = System.currentTimeMillis()

            val inputStream = context.assets.open(imageFileName)
            val bitmap = inputStream.use { stream ->
                BitmapUtils.decodeColoringBitmap(stream, maxSize = 1024)
            }

            if (bitmap == null) {
                throw IllegalArgumentException("Failed to decode image: $imageFileName")
            }

            val loadTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "Region图片加载完成: $imageFileName, 耗时: ${loadTime}ms, 大小: ${bitmap.width}x${bitmap.height}")

            Result.success(bitmap)
        } catch (e: IOException) {
            Log.e(TAG, "加载region图片失败: $imageFileName", e)
            Result.failure(e)
        }
    }

    /**
     * 完全静默的文件查找方法
     */
    private fun findAssetFileQuietly(fileName: String): InputStream {
        val assetManager = context.assets

        // 首先尝试直接打开文件
        try {
            return assetManager.open(fileName)
        } catch (e: FileNotFoundException) {
            // 静默处理，继续查找
        }

        // 在分类文件夹中查找
        try {
            val rootFiles = assetManager.list("") ?: emptyArray()
            for (folder in rootFiles) {
                try {
                    val subFiles = assetManager.list(folder)
                    if (subFiles != null && subFiles.contains(fileName)) {
                        return assetManager.open("$folder/$fileName")
                    }
                } catch (e: Exception) {
                    // 静默忽略错误
                }
            }
        } catch (e: Exception) {
            // 静默忽略错误
        }

        // 如果都找不到，抛出异常
        throw FileNotFoundException("Asset file not found: $fileName")
    }

    /**
     * 在分类文件夹中查找文件（静默版本，不记录日志）
     */
    private fun findAssetFileInCategoriesQuietly(fileName: String): InputStream {
        val assetManager = context.assets

        // 获取所有可能的分类文件夹
        val rootFiles = assetManager.list("") ?: emptyArray()

        for (folder in rootFiles) {
            try {
                // 检查是否是文件夹
                val subFiles = assetManager.list(folder)
                if (subFiles != null && subFiles.isNotEmpty()) {
                    // 在这个文件夹中查找目标文件
                    if (subFiles.contains(fileName)) {
                        // 静默找到文件，不记录日志
                        return assetManager.open("$folder/$fileName")
                    }
                }
            } catch (e: Exception) {
                // 忽略错误，继续查找下一个文件夹
            }
        }

        // 如果在所有分类文件夹中都找不到，抛出异常
        throw FileNotFoundException("Asset file not found: $fileName")
    }

    /**
     * 在分类文件夹中查找文件（带日志版本）
     */
    private fun findAssetFileInCategories(fileName: String): InputStream {
        val assetManager = context.assets

        // 获取所有可能的分类文件夹
        val rootFiles = assetManager.list("") ?: emptyArray()

        for (folder in rootFiles) {
            try {
                // 检查是否是文件夹
                val subFiles = assetManager.list(folder)
                if (subFiles != null && subFiles.isNotEmpty()) {
                    // 在这个文件夹中查找目标文件
                    if (subFiles.contains(fileName)) {
                        Log.d(TAG, "Found $fileName in category folder: $folder")
                        return assetManager.open("$folder/$fileName")
                    }
                }
            } catch (e: Exception) {
                // 忽略错误，继续查找下一个文件夹
            }
        }

        // 如果在所有分类文件夹中都找不到，抛出异常
        throw FileNotFoundException("Asset file not found: $fileName (searched in category folders)")
    }

    /**
     * 检查资源文件是否存在
     */
    fun assetExists(fileName: String): Boolean {
        return try {
            context.assets.open(fileName).use { true }
        } catch (e: IOException) {
            false
        }
    }

    /**
     * 获取资源文件大小
     */
    fun getAssetSize(fileName: String): Long {
        return try {
            context.assets.openFd(fileName).use { it.length }
        } catch (e: IOException) {
            -1L
        }
    }

    /**
     * 获取项目详细信息（用于显示）
     */
    fun getProjectDisplayInfo(project: ValidatedProject): String {
        val statusIcon = if (project.isValid) "✅" else "❌"
        val sizeInfo = "${project.fileSize.totalSizeKB}KB"
        val regionInfo = "${project.totalRegions}区域"
        val colorInfo = "${project.totalColors}颜色"
        
        return "$statusIcon ${project.name} ($regionInfo, $colorInfo, $sizeInfo)"
    }

    /**
     * 获取项目验证报告
     */
    fun getValidationReport(projects: List<ValidatedProject>): String {
        val validProjects = projects.filter { it.isValid }
        val invalidProjects = projects.filter { !it.isValid }
        
        val report = StringBuilder()
        report.appendLine("📊 填色项目验证报告")
        report.appendLine("===============================================" )
        report.appendLine("总项目数: ${projects.size}")
        report.appendLine("有效项目: ${validProjects.size}")
        report.appendLine("无效项目: ${invalidProjects.size}")
        report.appendLine()
        
        if (validProjects.isNotEmpty()) {
            report.appendLine("✅ 有效项目:")
            validProjects.forEach { project ->
                report.appendLine("  • ${project.name} (${project.fileSize.totalSizeKB}KB)")
            }
            report.appendLine()
        }
        
        if (invalidProjects.isNotEmpty()) {
            report.appendLine("❌ 无效项目:")
            invalidProjects.forEach { project ->
                report.appendLine("  • ${project.name}")
                project.validationErrors.forEach { error ->
                    report.appendLine("    - $error")
                }
            }
        }
        
        return report.toString()
    }
}
