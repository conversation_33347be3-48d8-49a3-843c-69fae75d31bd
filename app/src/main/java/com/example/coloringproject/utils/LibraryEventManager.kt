package com.example.coloringproject.utils

import android.util.Log
import com.example.coloringproject.interfaces.LibraryRefreshListener
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Library事件管理器
 * 管理Library刷新事件的发送和接收
 */
object LibraryEventManager {
    
    private const val TAG = "LibraryEventManager"
    
    // 使用线程安全的列表存储监听器
    private val listeners = CopyOnWriteArrayList<LibraryRefreshListener>()
    
    /**
     * 注册Library刷新监听器
     */
    fun registerListener(listener: LibraryRefreshListener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener)
            Log.d(TAG, "✅ 注册Library刷新监听器: ${listener.javaClass.simpleName}, 当前监听器数量: ${listeners.size}")
            Log.d(TAG, "📋 当前所有监听器: ${getListenerInfo()}")
        } else {
            Log.d(TAG, "⚠️ 监听器已存在，跳过注册: ${listener.javaClass.simpleName}")
        }
    }
    
    /**
     * 取消注册Library刷新监听器
     */
    fun unregisterListener(listener: LibraryRefreshListener) {
        if (listeners.remove(listener)) {
            Log.d(TAG, "取消注册Library刷新监听器: ${listener.javaClass.simpleName}, 剩余监听器: ${listeners.size}")
        }
    }

    /**
     * 获取当前监听器数量（用于调试）
     */
    fun getListenerCount(): Int {
        return listeners.size
    }

    /**
     * 获取监听器列表信息（用于调试）
     */
    fun getListenerInfo(): String {
        return listeners.joinToString(", ") { it.javaClass.simpleName }
    }
    
    /**
     * 通知项目进度更新
     */
    fun notifyProjectProgressUpdated(projectId: String, hasProgress: Boolean, progressPercentage: Int = 0) {
        // 使用统一的项目ID
        val unifiedProjectId = ProjectNameUtils.getSafeFileName(projectId)
        Log.d(TAG, "通知项目进度更新: $projectId -> $unifiedProjectId, 进度: ${progressPercentage}%, 监听器数量: ${listeners.size}")

        if (listeners.isEmpty()) {
            Log.w(TAG, "⚠️ 没有注册的监听器，进度更新通知将被忽略")
            return
        }

        listeners.forEachIndexed { index, listener ->
            try {
                Log.d(TAG, "通知监听器 $index: ${listener.javaClass.simpleName}")
                listener.onProjectProgressUpdated(unifiedProjectId, hasProgress, progressPercentage)
                Log.d(TAG, "✅ 监听器 $index 通知成功")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 通知项目进度更新失败: ${listener.javaClass.simpleName}", e)
            }
        }
    }
    
    /**
     * 通知预览图片更新
     */
    fun notifyProjectPreviewUpdated(projectId: String, previewImagePath: String?) {
        // 使用统一的项目ID
        val unifiedProjectId = ProjectNameUtils.getSafeFileName(projectId)
        Log.d(TAG, "通知预览图片更新: $projectId -> $unifiedProjectId, 路径: $previewImagePath, 监听器数量: ${listeners.size}")

        if (listeners.isEmpty()) {
            Log.w(TAG, "⚠️ 没有注册的监听器，预览图片更新通知将被忽略")
            return
        }

        listeners.forEachIndexed { index, listener ->
            try {
                Log.d(TAG, "通知监听器 $index 预览图片更新: ${listener.javaClass.simpleName}")
                listener.onProjectPreviewUpdated(unifiedProjectId, previewImagePath)
                Log.d(TAG, "✅ 监听器 $index 预览图片通知成功")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 通知预览图片更新失败: ${listener.javaClass.simpleName}", e)
            }
        }
    }
    
    /**
     * 通知项目完成
     */
    fun notifyProjectCompleted(projectId: String) {
        Log.d(TAG, "通知项目完成: $projectId")
        
        listeners.forEach { listener ->
            try {
                listener.onProjectCompleted(projectId)
            } catch (e: Exception) {
                Log.e(TAG, "通知项目完成失败: ${listener.javaClass.simpleName}", e)
            }
        }
    }
    
    /**
     * 通知刷新整个Library
     */
    fun notifyRefreshLibrary() {
        Log.d(TAG, "通知刷新整个Library")
        
        listeners.forEach { listener ->
            try {
                listener.refreshLibrary()
            } catch (e: Exception) {
                Log.e(TAG, "通知刷新Library失败: ${listener.javaClass.simpleName}", e)
            }
        }
    }
    
    /**
     * 清除所有监听器（通常在应用退出时调用）
     */
    fun clearAllListeners() {
        Log.d(TAG, "清除所有Library刷新监听器")
        listeners.clear()
    }
}
