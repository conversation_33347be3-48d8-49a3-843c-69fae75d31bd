package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.network.CategoryInfo
import com.example.coloringproject.network.ResourceDownloadManager
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 混合资源管理器
 * 结合APK打包初始资源和服务器获取资源
 */
class HybridResourceManager(private val context: Context) {
    
    private val localAssetManager = EnhancedAssetManager(context)
    private val downloadManager = ResourceDownloadManager(context)
    private val cacheManager = ResourceCacheManager(context)
    
    companion object {
        private const val TAG = "HybridResourceManager"
        
        // 资源类型
        enum class ResourceType {
            LOCAL_ASSET,    // APK打包资源
            DOWNLOADED,     // 已下载的文件
            CACHED_REMOTE,  // 已缓存的远程资源
            REMOTE_ONLY     // 仅远程资源
        }

        // 资源来源
        enum class ResourceSource {
            BUILT_IN,           // 内置资源
            DOWNLOADED,         // 已下载资源
            REMOTE_DOWNLOADED,  // 远程下载的资源
            STREAMING           // 流式资源
        }
    }
    
    /**
     * 混合项目信息
     */
    data class HybridProject(
        val id: String,
        val name: String,
        val displayName: String,
        val description: String,
        val category: String,
        val difficulty: String,
        val totalRegions: Int,
        val totalColors: Int,
        val estimatedTime: Int,
        val thumbnailUrl: String?,
        val previewUrl: String?,
        val resourceType: ResourceType,
        val resourceSource: ResourceSource,
        val version: String,
        val fileSize: Long,
        val isDownloaded: Boolean,
        val isBuiltIn: Boolean,
        val downloadProgress: Float = 0f,
        val tags: List<String> = emptyList(),
        val releaseDate: String? = null,
        val popularity: Int = 0,
        val rating: Float = 0f,
        var hasPreloadedData: Boolean = false  // 标记是否有预加载数据
    )
    
    /**
     * 资源加载结果
     */
    sealed class ResourceLoadResult {
        data class Success(
            val coloringData: ColoringData,
            val outlineBitmap: Bitmap,
            val source: ResourceSource
        ) : ResourceLoadResult()
        
        data class Error(
            val message: String,
            val cause: Throwable? = null
        ) : ResourceLoadResult()
        
        data class RequiresDownload(
            val project: HybridProject,
            val downloadUrl: String
        ) : ResourceLoadResult()
    }
    
    /**
     * 获取所有可用项目（本地+远程）
     */
    suspend fun getAllAvailableProjects(
        includeRemote: Boolean = true,
        forceRefresh: Boolean = false
    ): Result<List<HybridProject>> = withContext(Dispatchers.IO) {
        try {
            val projectsMap = mutableMapOf<String, HybridProject>()

            // 1. 获取本地APK资源
            val localProjects = getLocalProjects()
            Log.d(TAG, "Found ${localProjects.size} local projects")
            localProjects.forEach { project ->
                projectsMap[project.id] = project
                Log.d(TAG, "Added local project: ${project.id} - ${project.displayName}")
            }

            // 2. 获取已缓存的远程资源
            val cachedProjects = getCachedRemoteProjects()
            Log.d(TAG, "Found ${cachedProjects.size} cached projects")
            cachedProjects.forEach { project ->
                if (!projectsMap.containsKey(project.id)) {
                    projectsMap[project.id] = project
                    Log.d(TAG, "Added cached project: ${project.id} - ${project.displayName}")
                } else {
                    Log.d(TAG, "Skipped duplicate cached project: ${project.id}")
                }
            }

            // 3. 获取远程资源列表（如果需要）
            if (includeRemote) {
                val remoteProjects = getRemoteProjectsList(forceRefresh)
                Log.d(TAG, "Found ${remoteProjects.size} remote projects")
                remoteProjects.forEach { project ->
                    if (!projectsMap.containsKey(project.id)) {
                        projectsMap[project.id] = project
                        Log.d(TAG, "Added remote project: ${project.id} - ${project.displayName}")
                    } else {
                        Log.d(TAG, "Skipped duplicate remote project: ${project.id}")
                    }
                }
            }
            
            // 转换为列表并按优先级排序：内置 > 已下载 > 远程
            val projects = projectsMap.values.toList()
            val sortedProjects = projects.sortedWith(compareBy<HybridProject> {
                when (it.resourceSource) {
                    ResourceSource.BUILT_IN -> 0
                    ResourceSource.DOWNLOADED -> 1
                    ResourceSource.REMOTE_DOWNLOADED -> 1
                    ResourceSource.STREAMING -> 2
                }
            }.thenByDescending { it.popularity })

            Log.d(TAG, "Final project count: ${sortedProjects.size}")
            sortedProjects.forEach { project ->
                Log.d(TAG, "Final project: ${project.id} - ${project.displayName} (${project.resourceSource})")
            }

            Result.success(sortedProjects)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting available projects", e)
            Result.failure(e)
        }
    }
    
    /**
     * 加载项目资源
     */
    suspend fun loadProjectResource(projectId: String): ResourceLoadResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Loading project resource: $projectId")
            
            // 1. 首先尝试从本地资源加载（包括assets和下载的文件）
            val localResult = tryLoadFromLocal(projectId)
            if (localResult is ResourceLoadResult.Success) {
                return@withContext localResult
            }
            
            // 2. 尝试从缓存加载
            val cachedResult = tryLoadFromCache(projectId)
            if (cachedResult is ResourceLoadResult.Success) {
                return@withContext cachedResult
            }
            
            // 3. 检查是否需要下载
            val project = getProjectInfo(projectId)
            if (project != null && !project.isDownloaded) {
                val downloadUrl = downloadManager.getDownloadUrl(projectId)
                return@withContext ResourceLoadResult.RequiresDownload(project, downloadUrl)
            }

            ResourceLoadResult.Error("Project not found: $projectId")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error loading project resource: $projectId", e)
            ResourceLoadResult.Error("Failed to load project: ${e.message}", e)
        }
    }
    
    /**
     * 下载远程项目
     */
    suspend fun downloadProject(
        projectId: String,
        onProgress: (Float) -> Unit = {}
    ): Result<HybridProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Downloading project: $projectId")
            
            val downloadResult = downloadManager.downloadProject(projectId, onProgress)
            if (downloadResult.isSuccess) {
                // 更新缓存
                cacheManager.cacheProject(projectId, downloadResult.getOrNull()!!)
                
                // 返回更新后的项目信息
                val updatedProject = getProjectInfo(projectId)?.copy(
                    isDownloaded = true,
                    resourceSource = ResourceSource.DOWNLOADED,
                    resourceType = ResourceType.CACHED_REMOTE
                )
                
                if (updatedProject != null) {
                    Result.success(updatedProject)
                } else {
                    Result.failure(Exception("Failed to update project info"))
                }
            } else {
                Result.failure(downloadResult.exceptionOrNull() ?: Exception("Download failed"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading project: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 删除已下载的项目
     */
    suspend fun deleteDownloadedProject(projectId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val result = cacheManager.deleteProject(projectId)
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting project: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取项目信息
     */
    suspend fun getProjectInfo(projectId: String): HybridProject? = withContext(Dispatchers.IO) {
        // 首先检查本地
        getLocalProjects().find { it.id == projectId }
            ?: getCachedRemoteProjects().find { it.id == projectId }
            ?: getRemoteProjectInfo(projectId)
    }
    
    /**
     * 获取分类列表
     */
    suspend fun getCategories(): Result<List<CategoryInfo>> = withContext(Dispatchers.IO) {
        try {
            val apiResponse = downloadManager.getCategoriesList()
            if (apiResponse.isSuccess) {
                val response = apiResponse.getOrNull()
                if (response?.status == "success" && response.data != null) {
                    Result.success(response.data.categories)
                } else {
                    Result.failure(Exception(response?.error?.message ?: "Failed to get categories"))
                }
            } else {
                Result.failure(apiResponse.exceptionOrNull() ?: Exception("API call failed"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting categories", e)
            Result.failure(e)
        }
    }

    /**
     * 获取每日推荐
     */
    suspend fun getDailyRecommendations(): Result<com.example.coloringproject.data.DailyResponse?> = withContext(Dispatchers.IO) {
        try {
            val apiResponse = downloadManager.getDailyRecommendations()
            if (apiResponse.isSuccess) {
                val response = apiResponse.getOrNull()
                if (response?.status == "success" && response.data != null) {
                    Result.success(response.data)
                } else {
                    Result.failure(Exception(response?.error?.message ?: "Failed to get daily recommendations"))
                }
            } else {
                Result.failure(apiResponse.exceptionOrNull() ?: Exception("API call failed"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting daily recommendations", e)
            Result.failure(e)
        }
    }

    /**
     * 检查更新
     */
    suspend fun checkForUpdates(clientVersion: String): Result<ResourceDownloadManager.UpdateResponse> = withContext(Dispatchers.IO) {
        try {
            val apiResponse = downloadManager.checkUpdates(clientVersion)
            if (apiResponse.isSuccess) {
                val response = apiResponse.getOrNull()
                if (response?.status == "success" && response.data != null) {
                    Result.success(response.data)
                } else {
                    Result.failure(Exception(response?.error?.message ?: "Failed to check updates"))
                }
            } else {
                Result.failure(apiResponse.exceptionOrNull() ?: Exception("API call failed"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking for updates", e)
            Result.failure(e)
        }
    }
    
    // 私有方法实现
    private suspend fun getLocalProjects(): List<HybridProject> {
        return try {
            Log.d(TAG, "getLocalProjects() called")
            val localProjects = mutableListOf<HybridProject>()

            // 1. 获取assets中的项目
            Log.d(TAG, "Getting assets projects...")
            val validatedProjects = localAssetManager.getValidatedProjects()
            Log.d(TAG, "Assets projects result: ${validatedProjects.isSuccess}")
            validatedProjects.getOrNull()?.forEach { project ->
                localProjects.add(HybridProject(
                    id = project.id,
                    name = project.name,
                    displayName = project.name,
                    description = "内置填色项目",
                    category = "内置",
                    difficulty = project.difficulty,
                    totalRegions = project.totalRegions,
                    totalColors = project.totalColors,
                    estimatedTime = project.estimatedTime,
                    thumbnailUrl = null,
                    previewUrl = null,
                    resourceType = ResourceType.LOCAL_ASSET,
                    resourceSource = ResourceSource.BUILT_IN,
                    version = "1.0",
                    fileSize = project.fileSize.totalSizeKB * 1024,
                    isDownloaded = true,
                    isBuiltIn = true,
                    tags = listOf("内置", "离线"),
                    popularity = 100 // 内置项目优先级高
                ))
            }

            // 2. 获取下载的项目
            Log.d(TAG, "About to call getDownloadedProjects()")
            val downloadedProjects = getDownloadedProjects()
            Log.d(TAG, "getDownloadedProjects() returned ${downloadedProjects.size} projects")
            localProjects.addAll(downloadedProjects)

            Log.d(TAG, "Total local projects (assets + downloaded): ${localProjects.size}")
            localProjects
        } catch (e: Exception) {
            Log.e(TAG, "Error getting local projects", e)
            emptyList()
        }
    }

    /**
     * 获取已下载的项目
     */
    private suspend fun getDownloadedProjects(): List<HybridProject> {
        return try {
            val downloadDir = File(context.filesDir, "downloaded_projects")
            Log.d(TAG, "Checking download directory: ${downloadDir.absolutePath}")
            Log.d(TAG, "Download directory exists: ${downloadDir.exists()}")

            if (!downloadDir.exists()) {
                Log.d(TAG, "Download directory does not exist")
                return emptyList()
            }

            val allFiles = downloadDir.listFiles()
            Log.d(TAG, "Subdirectories in download directory: ${allFiles?.filter { it.isDirectory }?.map { it.name }}")

            val downloadedProjects = mutableListOf<HybridProject>()

            // 扫描每个项目子目录
            allFiles?.filter { it.isDirectory }?.forEach { projectDir ->
                Log.d(TAG, "Scanning project directory: ${projectDir.name}")
                val jsonFiles = projectDir.listFiles { file -> file.name.endsWith(".json") }
                Log.d(TAG, "JSON files in ${projectDir.name}: ${jsonFiles?.map { it.name }}")

                jsonFiles?.forEach { jsonFile ->
                    try {
                        Log.d(TAG, "Processing JSON file: ${jsonFile.name}")
                        val basename = jsonFile.nameWithoutExtension
                        val outlineFile = File(projectDir, "${basename}.png")
                        Log.d(TAG, "Looking for outline file: ${outlineFile.absolutePath}")
                        Log.d(TAG, "Outline file exists: ${outlineFile.exists()}")

                    if (outlineFile.exists()) {
                        Log.d(TAG, "Attempting to parse JSON file: ${jsonFile.absolutePath}")
                        // 尝试解析服务器格式的JSON文件
                        val serverProjectResult = parseServerProjectJson(jsonFile)
                        Log.d(TAG, "Server JSON parsing result: ${serverProjectResult.isSuccess}")
                        if (serverProjectResult.isFailure) {
                            Log.e(TAG, "Server JSON parsing failed: ${serverProjectResult.exceptionOrNull()?.message}")
                        }
                        if (serverProjectResult.isSuccess) {
                            val serverProject = serverProjectResult.getOrThrow()

                            downloadedProjects.add(HybridProject(
                                id = basename,
                                name = basename,
                                displayName = serverProject.meta.name ?: basename,
                                description = "已下载的填色项目",
                                category = "已下载",
                                difficulty = when (serverProject.meta.difficulty) {
                                    1 -> "easy"
                                    2 -> "medium"
                                    3 -> "hard"
                                    else -> "medium"
                                },
                                totalRegions = serverProject.meta.regions ?: 0,
                                totalColors = serverProject.meta.colors ?: 0,
                                estimatedTime = serverProject.meta.estimated_time ?: 30,
                                thumbnailUrl = null,
                                previewUrl = null,
                                resourceType = ResourceType.DOWNLOADED,
                                resourceSource = ResourceSource.REMOTE_DOWNLOADED,
                                version = "1.0",
                                fileSize = jsonFile.length() + outlineFile.length(),
                                isDownloaded = true,
                                isBuiltIn = false,
                                tags = listOf("已下载", "离线"),
                                popularity = 50
                            ))
                            Log.d(TAG, "Successfully parsed downloaded project: ${serverProject.meta.name}")
                        }
                    }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing downloaded project: ${jsonFile.name}", e)
                    }
                }
            }

            Log.d(TAG, "Found ${downloadedProjects.size} downloaded projects")
            downloadedProjects
        } catch (e: Exception) {
            Log.e(TAG, "Error getting downloaded projects", e)
            emptyList()
        }
    }

    private suspend fun getCachedRemoteProjects(): List<HybridProject> {
        return try {
            val cachedProjects = cacheManager.getCachedProjects()
            cachedProjects.map { cached ->
                HybridProject(
                    id = cached.projectId,
                    name = cached.name,
                    displayName = cached.displayName,
                    description = cached.description,
                    category = cached.category,
                    difficulty = cached.difficulty,
                    totalRegions = cached.totalRegions,
                    totalColors = cached.totalColors,
                    estimatedTime = cached.estimatedTime,
                    thumbnailUrl = null,
                    previewUrl = null,
                    resourceType = ResourceType.CACHED_REMOTE,
                    resourceSource = ResourceSource.DOWNLOADED,
                    version = cached.version,
                    fileSize = cached.fileSize,
                    isDownloaded = true,
                    isBuiltIn = false,
                    tags = cached.tags,
                    releaseDate = cached.releaseDate,
                    popularity = cached.popularity,
                    rating = cached.rating
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cached remote projects", e)
            emptyList()
        }
    }

    private suspend fun getRemoteProjectsList(forceRefresh: Boolean): List<HybridProject> {
        return try {
            val apiResponse = downloadManager.getProjectsList()
            if (apiResponse.isSuccess) {
                val response = apiResponse.getOrNull()
                if (response?.status == "success" && response.data != null) {
                    response.data.projects.map { remote ->
                        HybridProject(
                            id = remote.id,
                            name = remote.name,
                            displayName = remote.displayName,
                            description = remote.description,
                            category = remote.category,
                            difficulty = mapDifficultyFromNumber(remote.difficulty),
                            totalRegions = remote.totalRegions,
                            totalColors = remote.totalColors,
                            estimatedTime = remote.estimatedTimeMinutes,
                            thumbnailUrl = remote.thumbnailUrl,
                            previewUrl = remote.previewUrl,
                            resourceType = ResourceType.REMOTE_ONLY,
                            resourceSource = ResourceSource.STREAMING,
                            version = remote.version,
                            fileSize = remote.fileSize,
                            isDownloaded = false,
                            isBuiltIn = false,
                            tags = remote.tags,
                            releaseDate = remote.releaseDate,
                            popularity = remote.popularity,
                            rating = remote.rating.toFloat()
                        )
                    }
                } else {
                    Log.e(TAG, "API response error: ${response?.error?.message}")
                    emptyList()
                }
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting remote projects list", e)
            emptyList()
        }
    }

    /**
     * 将数字难度转换为字符串
     */
    private fun mapDifficultyFromNumber(difficulty: String): String {
        return when (difficulty) {
            "1", "2" -> "easy"
            "3" -> "medium"
            "4", "5" -> "hard"
            else -> difficulty.lowercase()
        }
    }

    private suspend fun tryLoadFromLocal(projectId: String): ResourceLoadResult {
        return try {
            // 1. 首先尝试从下载的文件加载
            val downloadResult = tryLoadFromDownloaded(projectId)
            if (downloadResult is ResourceLoadResult.Success) {
                return downloadResult
            }

            // 2. 然后尝试从本地assets加载
            val validatedProjects = localAssetManager.getValidatedProjects().getOrNull()
            val project = validatedProjects?.find { it.id == projectId }

            if (project != null) {
                val coloringDataResult = localAssetManager.loadColoringData(project.jsonFile)
                val outlineBitmapResult = localAssetManager.loadOutlineBitmap(project.outlineFile)

                if (coloringDataResult.isSuccess && outlineBitmapResult.isSuccess) {
                    ResourceLoadResult.Success(
                        coloringData = coloringDataResult.getOrNull()!!,
                        outlineBitmap = outlineBitmapResult.getOrNull()!!,
                        source = ResourceSource.BUILT_IN
                    )
                } else {
                    ResourceLoadResult.Error("Failed to load local asset files")
                }
            } else {
                ResourceLoadResult.Error("Project not found in local resources")
            }
        } catch (e: Exception) {
            ResourceLoadResult.Error("Error loading from local resources: ${e.message}", e)
        }
    }

    private suspend fun tryLoadFromDownloaded(projectId: String): ResourceLoadResult {
        return try {
            val downloadDir = File(context.filesDir, "downloaded_projects/$projectId")
            val jsonFile = File(downloadDir, "$projectId.json")
            val outlineFile = File(downloadDir, "$projectId.png")

            if (jsonFile.exists() && outlineFile.exists()) {
                val coloringDataResult = localAssetManager.loadColoringData(jsonFile.absolutePath)
                val outlineBitmapResult = localAssetManager.loadOutlineBitmap(outlineFile.absolutePath)

                if (coloringDataResult.isSuccess && outlineBitmapResult.isSuccess) {
                    ResourceLoadResult.Success(
                        coloringData = coloringDataResult.getOrNull()!!,
                        outlineBitmap = outlineBitmapResult.getOrNull()!!,
                        source = ResourceSource.REMOTE_DOWNLOADED
                    )
                } else {
                    ResourceLoadResult.Error("Failed to load downloaded files")
                }
            } else {
                ResourceLoadResult.Error("Downloaded files not found")
            }
        } catch (e: Exception) {
            ResourceLoadResult.Error("Error loading downloaded files: ${e.message}", e)
        }
    }

    private suspend fun tryLoadFromCache(projectId: String): ResourceLoadResult {
        return try {
            val coloringDataResult = cacheManager.loadCachedColoringData(projectId)
            val outlineBitmapResult = cacheManager.loadCachedOutlineBitmap(projectId)

            if (coloringDataResult.isSuccess && outlineBitmapResult.isSuccess) {
                ResourceLoadResult.Success(
                    coloringData = coloringDataResult.getOrNull()!!,
                    outlineBitmap = outlineBitmapResult.getOrNull()!!,
                    source = ResourceSource.DOWNLOADED
                )
            } else {
                ResourceLoadResult.Error("Failed to load from cache")
            }
        } catch (e: Exception) {
            ResourceLoadResult.Error("Error loading from cache: ${e.message}", e)
        }
    }

    private suspend fun getRemoteProjectInfo(projectId: String): HybridProject? {
        return try {
            val projectInfoResult = downloadManager.getProjectDetail(projectId)
            if (projectInfoResult.isSuccess) {
                val remote = projectInfoResult.getOrNull()!!
                HybridProject(
                    id = remote.id,
                    name = remote.name,
                    displayName = remote.displayName,
                    description = remote.description,
                    category = remote.category,
                    difficulty = remote.difficulty,
                    totalRegions = remote.totalRegions,
                    totalColors = remote.totalColors,
                    estimatedTime = remote.estimatedTimeMinutes ?: 30,
                    thumbnailUrl = remote.thumbnailUrl,
                    previewUrl = remote.previewUrl,
                    resourceType = ResourceType.REMOTE_ONLY,
                    resourceSource = ResourceSource.STREAMING,
                    version = remote.version,
                    fileSize = remote.fileSize,
                    isDownloaded = false,
                    isBuiltIn = false,
                    tags = remote.tags,
                    releaseDate = remote.releaseDate,
                    popularity = remote.popularity,
                    rating = remote.rating
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting remote project info: $projectId", e)
            null
        }
    }

    /**
     * 解析服务器格式的JSON文件
     */
    private fun parseServerProjectJson(jsonFile: File): Result<ServerProject> {
        return try {
            val jsonContent = jsonFile.readText()
            Log.d(TAG, "Parsing server JSON content: ${jsonContent.take(200)}...")

            val gson = Gson()

            // 首先尝试解析真正的涂色数据JSON格式
            try {
                val coloringProject = gson.fromJson(jsonContent, ColoringProjectData::class.java)
                if (coloringProject.meta != null) {
                    // 这是真正的涂色数据JSON
                    val serverProject = ServerProject(
                        meta = ServerProjectMeta(
                            name = "下载的涂色项目", // 从文件名或其他地方获取名称
                            category = "下载项目",
                            regions = coloringProject.meta.regions ?: 0,
                            colors = coloringProject.meta.colors ?: 0,
                            difficulty = 2, // 默认中等难度
                            estimated_time = 30, // 默认30分钟
                            version = coloringProject.meta.v ?: "1.0"
                        )
                    )

                    Log.d(TAG, "Successfully parsed coloring data JSON: regions=${coloringProject.meta.regions}, colors=${coloringProject.meta.colors}")
                    return Result.success(serverProject)
                }
            } catch (e: Exception) {
                Log.d(TAG, "Not a coloring data JSON, trying API response format...")
            }

            // 如果不是涂色数据JSON，尝试解析API响应格式
            try {
                val apiResponse = gson.fromJson(jsonContent, ApiResponseWrapper::class.java)

                if (apiResponse.status == "success" && apiResponse.data != null) {
                    // 转换为ServerProject格式
                    val serverProject = ServerProject(
                        meta = ServerProjectMeta(
                            name = apiResponse.data.displayName ?: apiResponse.data.name,
                            category = apiResponse.data.category ?: "未分类",
                            regions = apiResponse.data.totalRegions ?: 0,
                            colors = apiResponse.data.totalColors ?: 0,
                            difficulty = when (apiResponse.data.difficulty) {
                                "简单", "easy" -> 1
                                "中等", "medium" -> 2
                                "困难", "hard" -> 3
                                else -> 2
                            },
                            estimated_time = apiResponse.data.estimatedTimeMinutes ?: 30,
                            version = apiResponse.data.version ?: "1.0.0"
                        )
                    )

                    Log.d(TAG, "Successfully parsed API response: ${serverProject.meta.name}")
                    return Result.success(serverProject)
                } else {
                    Log.e(TAG, "API response status: ${apiResponse.status}, error: ${apiResponse.error}")
                    return Result.failure(Exception("Invalid API response: ${apiResponse.status}"))
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to parse as API response format", e)
            }

            // 如果两种格式都解析失败
            Result.failure(Exception("Unknown JSON format"))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse server JSON file: ${jsonFile.absolutePath}", e)
            Result.failure(e)
        }
    }

    // 数据类定义

    // 真正的涂色数据JSON格式
    data class ColoringProjectData(
        val meta: ColoringProjectMeta?,
        val regions: List<ColoringRegion>?,
        val palette: List<ColoringPalette>?
    )

    data class ColoringProjectMeta(
        val v: String?,
        val regions: Int?,
        val colors: Int?,
        val size: List<Int>?
    )

    data class ColoringRegion(
        val id: Int,
        val hex: String?,
        val pixels: Any? // 可以是不同的格式
    )

    data class ColoringPalette(
        val id: Int,
        val hex: String?
    )

    // API响应格式
    data class ApiResponseWrapper(
        val status: String,
        val data: ApiProjectData?,
        val error: String?,
        val timestamp: String?
    )

    data class ApiProjectData(
        val id: String,
        val name: String,
        val displayName: String?,
        val description: String?,
        val category: String?,
        val difficulty: String?,
        val totalRegions: Int?,
        val totalColors: Int?,
        val estimatedTimeMinutes: Int?,
        val version: String?,
        val fileSize: Long?,
        val thumbnailUrl: String?,
        val previewUrl: String?,
        val tags: List<String>?,
        val releaseDate: String?,
        val popularity: Int?,
        val rating: Double?,
        val downloadCount: Long?,
        val files: ApiProjectFiles?
    )

    data class ApiProjectFiles(
        val jsonUrl: String,
        val outlineUrl: String,
        val jsonSize: Long?,
        val outlineSize: Long?,
        val jsonChecksum: String?,
        val outlineChecksum: String?
    )

    data class ServerProject(
        val meta: ServerProjectMeta
    )

    data class ServerProjectMeta(
        val name: String,
        val category: String,
        val regions: Int,
        val colors: Int,
        val difficulty: Int,
        val estimated_time: Int,
        val version: String
    )
}
