package com.example.coloringproject.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.util.Log
import java.io.BufferedReader
import java.io.FileReader

/**
 * 设备内存管理器 - 动态内存调整方案A
 * 根据设备性能动态调整内存使用策略
 */
class DeviceMemoryManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "DeviceMemoryManager"
        
        @Volatile
        private var INSTANCE: DeviceMemoryManager? = null
        
        fun getInstance(context: Context): DeviceMemoryManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DeviceMemoryManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 设备性能等级
     */
    enum class DeviceTier(
        val maxPreloadProjects: Int,
        val maxConcurrentLoads: Int,
        val bitmapSampleSize: Int,
        val description: String
    ) {
        HIGH_END(6, 3, 1, "高端设备"),      // 6个项目，3个并发，原始质量
        MID_RANGE(3, 2, 2, "中端设备"),     // 3个项目，2个并发，1/2质量  
        LOW_END(1, 1, 4, "低端设备")        // 1个项目，1个并发，1/4质量
    }
    
    /**
     * 内存配置信息
     */
    data class MemoryConfig(
        val deviceTier: DeviceTier,
        val totalRAM: Long,
        val availableHeap: Long,
        val maxHeap: Long,
        val cpuCores: Int,
        val isLargeHeapEnabled: Boolean
    )
    
    private var cachedConfig: MemoryConfig? = null
    
    /**
     * 获取设备内存配置
     */
    fun getMemoryConfig(): MemoryConfig {
        if (cachedConfig == null) {
            cachedConfig = detectMemoryConfig()
        }
        return cachedConfig!!
    }
    
    /**
     * 检测设备内存配置
     */
    private fun detectMemoryConfig(): MemoryConfig {
        val totalRAM = getTotalRAM()
        val runtime = Runtime.getRuntime()
        val maxHeap = runtime.maxMemory()
        val availableHeap = maxHeap - (runtime.totalMemory() - runtime.freeMemory())
        val cpuCores = runtime.availableProcessors()
        
        // 检查是否启用了largeHeap
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryClass = activityManager.memoryClass
        val largeMemoryClass = activityManager.largeMemoryClass
        val isLargeHeapEnabled = largeMemoryClass > memoryClass
        
        // 根据多个指标综合判断设备等级
        val deviceTier = calculateDeviceTier(totalRAM, maxHeap, cpuCores)
        
        val config = MemoryConfig(
            deviceTier = deviceTier,
            totalRAM = totalRAM,
            availableHeap = availableHeap,
            maxHeap = maxHeap,
            cpuCores = cpuCores,
            isLargeHeapEnabled = isLargeHeapEnabled
        )
        
        logMemoryInfo(config)
        return config
    }
    
    /**
     * 计算设备性能等级
     */
    private fun calculateDeviceTier(totalRAM: Long, maxHeap: Long, cpuCores: Int): DeviceTier {
        val ramGB = totalRAM / (1024 * 1024 * 1024)
        val heapMB = maxHeap / (1024 * 1024)
        
        // 综合评分系统
        var score = 0
        
        // RAM评分 (40%权重)
        score += when {
            ramGB >= 8 -> 40
            ramGB >= 6 -> 32
            ramGB >= 4 -> 24
            ramGB >= 3 -> 16
            else -> 8
        }
        
        // 堆内存评分 (35%权重)
        score += when {
            heapMB >= 512 -> 35
            heapMB >= 256 -> 28
            heapMB >= 128 -> 21
            heapMB >= 64 -> 14
            else -> 7
        }
        
        // CPU核心数评分 (25%权重)
        score += when {
            cpuCores >= 8 -> 25
            cpuCores >= 6 -> 20
            cpuCores >= 4 -> 15
            cpuCores >= 2 -> 10
            else -> 5
        }
        
        return when {
            score >= 80 -> DeviceTier.HIGH_END
            score >= 50 -> DeviceTier.MID_RANGE
            else -> DeviceTier.LOW_END
        }
    }
    
    /**
     * 获取设备总RAM
     */
    private fun getTotalRAM(): Long {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                val memInfo = ActivityManager.MemoryInfo()
                activityManager.getMemoryInfo(memInfo)
                memInfo.totalMem
            } else {
                // 兼容旧版本，通过读取/proc/meminfo
                readMemInfoFromProc()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取总RAM失败", e)
            2L * 1024 * 1024 * 1024 // 默认2GB
        }
    }
    
    /**
     * 从/proc/meminfo读取内存信息
     */
    private fun readMemInfoFromProc(): Long {
        return try {
            val reader = BufferedReader(FileReader("/proc/meminfo"))
            val line = reader.readLine()
            reader.close()
            
            val parts = line.split("\\s+".toRegex())
            val memTotal = parts[1].toLong() * 1024 // kB to bytes
            memTotal
        } catch (e: Exception) {
            Log.e(TAG, "读取/proc/meminfo失败", e)
            2L * 1024 * 1024 * 1024 // 默认2GB
        }
    }
    
    /**
     * 获取当前内存使用情况
     */
    fun getCurrentMemoryUsage(): MemoryUsage {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val availableMemory = maxMemory - usedMemory
        
        return MemoryUsage(
            maxMemory = maxMemory,
            usedMemory = usedMemory,
            availableMemory = availableMemory,
            usagePercentage = (usedMemory.toFloat() / maxMemory.toFloat() * 100).toInt()
        )
    }
    
    /**
     * 内存使用情况
     */
    data class MemoryUsage(
        val maxMemory: Long,
        val usedMemory: Long,
        val availableMemory: Long,
        val usagePercentage: Int
    )
    
    /**
     * 检查是否应该启用激进预加载
     */
    fun shouldEnableAggressivePreload(): Boolean {
        val config = getMemoryConfig()
        val usage = getCurrentMemoryUsage()
        
        return config.deviceTier == DeviceTier.HIGH_END && usage.usagePercentage < 60
    }
    
    /**
     * 获取推荐的预加载项目数量
     */
    fun getRecommendedPreloadCount(): Int {
        val config = getMemoryConfig()
        val usage = getCurrentMemoryUsage()
        
        // 根据当前内存使用情况动态调整
        val baseCount = config.deviceTier.maxPreloadProjects
        
        return when {
            usage.usagePercentage > 80 -> maxOf(1, baseCount / 4)
            usage.usagePercentage > 60 -> maxOf(1, baseCount / 2)
            else -> baseCount
        }
    }
    
    /**
     * 记录内存信息
     */
    private fun logMemoryInfo(config: MemoryConfig) {
        Log.i(TAG, "=== 设备内存配置检测 ===")
        Log.i(TAG, "设备等级: ${config.deviceTier.description}")
        Log.i(TAG, "总RAM: ${config.totalRAM / 1024 / 1024}MB")
        Log.i(TAG, "最大堆内存: ${config.maxHeap / 1024 / 1024}MB")
        Log.i(TAG, "可用堆内存: ${config.availableHeap / 1024 / 1024}MB")
        Log.i(TAG, "CPU核心数: ${config.cpuCores}")
        Log.i(TAG, "LargeHeap启用: ${config.isLargeHeapEnabled}")
        Log.i(TAG, "推荐预加载数量: ${config.deviceTier.maxPreloadProjects}")
        Log.i(TAG, "推荐并发数: ${config.deviceTier.maxConcurrentLoads}")
        Log.i(TAG, "推荐采样率: 1/${config.deviceTier.bitmapSampleSize}")
        Log.i(TAG, "========================")
    }
}
