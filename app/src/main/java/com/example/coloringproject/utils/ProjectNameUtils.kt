package com.example.coloringproject.utils

import android.content.Intent
import android.util.Log

/**
 * 项目名称统一管理工具
 * 确保Library、Gallery和涂色页面使用相同的项目标识符
 */
object ProjectNameUtils {
    
    private const val TAG = "ProjectNameUtils"
    
    /**
     * 获取统一的项目标识符
     * 优先级：project_id > project_name > displayName > id
     * 
     * @param intent Activity的Intent（用于涂色页面）
     * @param projectId 项目ID（用于Library）
     * @param projectName 项目名称（用于Gallery）
     * @param displayName 显示名称（备用）
     * @return 统一的项目标识符
     */
    fun getUnifiedProjectId(
        intent: Intent? = null,
        projectId: String? = null,
        projectName: String? = null,
        displayName: String? = null
    ): String {
        
        // 从Intent获取参数（涂色页面场景）
        val intentProjectId = intent?.getStringExtra("project_id")
        val intentProjectName = intent?.getStringExtra("project_name")
        val fromGallery = intent?.getBooleanExtra("from_gallery", false) ?: false
        
        val finalId = when {
            // 优先使用明确传入的project_id
            !intentProjectId.isNullOrEmpty() -> intentProjectId
            !projectId.isNullOrEmpty() -> projectId
            
            // 然后使用project_name
            !intentProjectName.isNullOrEmpty() -> intentProjectName
            !projectName.isNullOrEmpty() -> projectName
            
            // 最后使用displayName或其他备用值
            !displayName.isNullOrEmpty() -> displayName
            
            else -> "unknown_project"
        }
        
        Log.d(TAG, "统一项目ID: $finalId (来源: intent_id=$intentProjectId, id=$projectId, name=$projectName, display=$displayName, fromGallery=$fromGallery)")
        return finalId
    }
    
    /**
     * 从HybridProject获取统一的项目ID
     */
    fun getUnifiedProjectId(project: HybridResourceManager.HybridProject): String {
        return getUnifiedProjectId(
            projectId = project.id,
            projectName = project.name,
            displayName = project.displayName
        )
    }
    
    /**
     * 从LightweightProject获取统一的项目ID
     */
    fun getUnifiedProjectId(project: LightweightResourceValidator.LightweightProject): String {
        return getUnifiedProjectId(
            projectId = project.id,
            displayName = project.displayName
        )
    }
    
    /**
     * 从ProjectProgress获取统一的项目ID
     */
    fun getUnifiedProjectId(project: ProjectProgress): String {
        return getUnifiedProjectId(
            projectName = project.projectName
        )
    }
    
    /**
     * 检查两个项目ID是否匹配
     * 支持多种匹配方式，提高兼容性
     */
    fun isProjectMatch(id1: String?, id2: String?): Boolean {
        if (id1.isNullOrEmpty() || id2.isNullOrEmpty()) {
            return false
        }
        
        // 直接匹配
        if (id1 == id2) {
            return true
        }
        
        // 忽略大小写匹配
        if (id1.equals(id2, ignoreCase = true)) {
            return true
        }
        
        // 移除特殊字符后匹配
        val normalized1 = id1.replace(Regex("[^a-zA-Z0-9]"), "").lowercase()
        val normalized2 = id2.replace(Regex("[^a-zA-Z0-9]"), "").lowercase()
        if (normalized1 == normalized2) {
            return true
        }
        
        return false
    }
    
    /**
     * 生成保存文件使用的文件名
     * 确保文件名安全且一致
     */
    fun getSafeFileName(projectId: String): String {
        return projectId
            .replace(Regex("[^a-zA-Z0-9_-]"), "_")
            .replace(Regex("_+"), "_")
            .trim('_')
            .takeIf { it.isNotEmpty() } ?: "unknown_project"
    }
}
