//package com.example.coloringproject.utils
//
//import android.util.Log
//import com.example.coloringproject.view.ColoringView
//
///**
// * 颜色映射测试器
// * 专门测试颜色到数字的映射是否正确
// */
//object ColorMappingTester {
//
//    private const val TAG = "ColorMappingTest"
//
//    /**
//     * 测试颜色映射
//     */
//    fun testColorMapping(coloringView: ColoringView) {
//        Log.d(TAG, "=== 开始测试颜色映射 ===")
//
//        val coloringData = coloringView.getColoringDataPublic()
//        if (coloringData == null) {
//            Log.w(TAG, "ColoringData为空，无法测试")
//            return
//        }
//
//        val regions = coloringData.regions
//        Log.d(TAG, "总区域数: ${regions.size}")
//
//        // 收集所有颜色
//        val allColors = regions.map { it.colorHex }
//        Log.d(TAG, "所有颜色数量: ${allColors.size}")
//
//        // 显示前20个颜色
//        Log.d(TAG, "前20个原始颜色:")
//        allColors.take(20).forEachIndexed { index, color ->
//            Log.d(TAG, "  [$index] $color")
//        }
//
//        // 标准化颜色
//        val normalizedColors = allColors.map { normalizeColorHex(it) }
//        Log.d(TAG, "前20个标准化颜色:")
//        normalizedColors.take(20).forEachIndexed { index, color ->
//            Log.d(TAG, "  [$index] $color")
//        }
//
//        // 唯一颜色
//        val uniqueColors = normalizedColors.distinct().sorted()
//        Log.d(TAG, "唯一颜色数量: ${uniqueColors.size}")
//        Log.d(TAG, "所有唯一颜色:")
//        uniqueColors.forEachIndexed { index, color ->
//            Log.d(TAG, "  [${index + 1}] $color")
//        }
//
//        // 创建映射
//        val colorMapping = uniqueColors.mapIndexed { index, color ->
//            color to (index + 1)
//        }.toMap()
//
//        Log.d(TAG, "颜色映射:")
//        colorMapping.forEach { (color, number) ->
//            Log.d(TAG, "  $color -> $number")
//        }
//
//        // 测试每个区域的映射
//        Log.d(TAG, "测试前10个区域的颜色映射:")
//        regions.take(10).forEach { region ->
//            val originalColor = region.colorHex
//            val normalizedColor = normalizeColorHex(originalColor)
//            val mappedNumber = colorMapping[normalizedColor] ?: -1
//
//            Log.d(TAG, "区域${region.id}: $originalColor -> $normalizedColor -> $mappedNumber")
//        }
//
//        // 统计每种颜色的区域数量
//        Log.d(TAG, "每种颜色的区域数量:")
//        val colorCounts = normalizedColors.groupingBy { it }.eachCount()
//        colorCounts.forEach { (color, count) ->
//            val number = colorMapping[color] ?: -1
//            Log.d(TAG, "  颜色$color (数字$number): $count个区域")
//        }
//
//        Log.d(TAG, "=== 颜色映射测试完成 ===")
//    }
//
//    /**
//     * 标准化颜色格式
//     */
//    private fun normalizeColorHex(colorHex: String): String {
//        var normalized = colorHex.trim().lowercase()
//        if (!normalized.startsWith("#")) {
//            normalized = "#$normalized"
//        }
//        if (normalized.length == 4) {
//            val r = normalized[1]
//            val g = normalized[2]
//            val b = normalized[3]
//            normalized = "#$r$r$g$g$b$b"
//        }
//        return normalized
//    }
//}