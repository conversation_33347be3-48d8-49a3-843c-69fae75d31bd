package com.example.coloringproject.utils

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.network.ResourceDownloadManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream

/**
 * 资源缓存管理器
 * 负责管理已下载的远程资源
 */
class ResourceCacheManager(private val context: Context) {
    
    private val gson = Gson()
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    companion object {
        private const val TAG = "ResourceCacheManager"
        private const val PREFS_NAME = "resource_cache"
        private const val KEY_CACHED_PROJECTS = "cached_projects"
        private const val KEY_CACHE_VERSION = "cache_version"
        private const val CACHE_VERSION = 1
        
        // 缓存目录
        private const val CACHE_DIR = "resource_cache"
        private const val PROJECTS_DIR = "projects"
        private const val THUMBNAILS_DIR = "thumbnails"
        private const val METADATA_FILE = "metadata.json"
    }
    
    /**
     * 缓存的项目信息
     */
    data class CachedProjectInfo(
        val projectId: String,
        val name: String,
        val displayName: String,
        val description: String,
        val category: String,
        val difficulty: String,
        val totalRegions: Int,
        val totalColors: Int,
        val estimatedTime: Int,
        val version: String,
        val fileSize: Long,
        val cacheTime: Long,
        val lastAccessTime: Long,
        val accessCount: Int,
        val tags: List<String>,
        val releaseDate: String?,
        val popularity: Int,
        val rating: Float,
        val hasJsonFile: Boolean,
        val hasOutlineFile: Boolean,
        val hasRegionFile: Boolean,
        val hasThumbnailFile: Boolean
    )
    
    /**
     * 缓存项目
     */
    suspend fun cacheProject(
        projectId: String,
        downloadedProject: ResourceDownloadManager.DownloadedProject
    ): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Caching project: $projectId")
            
            // 1. 创建项目缓存目录
            val projectCacheDir = getProjectCacheDirectory(projectId)
            if (!projectCacheDir.exists()) {
                projectCacheDir.mkdirs()
            }
            
            // 2. 复制文件到缓存目录
            val cachedFiles = mutableMapOf<String, Boolean>()
            
            // JSON文件
            val cachedJsonFile = File(projectCacheDir, "data.json")
            downloadedProject.jsonFile.copyTo(cachedJsonFile, overwrite = true)
            cachedFiles["json"] = true
            
            // Outline文件
            val cachedOutlineFile = File(projectCacheDir, "outline.png")
            downloadedProject.outlineFile.copyTo(cachedOutlineFile, overwrite = true)
            cachedFiles["outline"] = true
            
            // Region文件（可选）
            downloadedProject.regionFile?.let { regionFile ->
                val cachedRegionFile = File(projectCacheDir, "region.png")
                regionFile.copyTo(cachedRegionFile, overwrite = true)
                cachedFiles["region"] = true
            }
            
            // Thumbnail文件（可选）
            downloadedProject.thumbnailFile?.let { thumbnailFile ->
                val cachedThumbnailFile = File(projectCacheDir, "thumbnail.png")
                thumbnailFile.copyTo(cachedThumbnailFile, overwrite = true)
                cachedFiles["thumbnail"] = true
            }
            
            // 3. 加载项目元数据
            val coloringData = loadColoringDataFromFile(cachedJsonFile)
            if (coloringData == null) {
                throw Exception("Failed to load coloring data from cached file")
            }
            
            // 4. 创建缓存信息
            val cachedProjectInfo = CachedProjectInfo(
                projectId = projectId,
                name = projectId,
                displayName = projectId,
                description = "",
                category = "",
                difficulty = coloringData.metadata.difficulty,
                totalRegions = coloringData.metadata.totalRegions,
                totalColors = coloringData.metadata.totalColors,
                estimatedTime = coloringData.metadata.estimatedTimeMinutes,
                version = downloadedProject.version,
                fileSize = calculateTotalFileSize(projectCacheDir),
                cacheTime = System.currentTimeMillis(),
                lastAccessTime = System.currentTimeMillis(),
                accessCount = 0,
                tags = emptyList(),
                releaseDate = null,
                popularity = 0,
                rating = 0f,
                hasJsonFile = cachedFiles["json"] == true,
                hasOutlineFile = cachedFiles["outline"] == true,
                hasRegionFile = cachedFiles["region"] == true,
                hasThumbnailFile = cachedFiles["thumbnail"] == true
            )
            
            // 5. 保存元数据
            saveProjectMetadata(projectId, cachedProjectInfo)
            
            // 6. 更新缓存索引
            updateCacheIndex(projectId, cachedProjectInfo)
            
            Log.d(TAG, "Project cached successfully: $projectId")
            Result.success(true)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error caching project: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取缓存的项目列表
     */
    suspend fun getCachedProjects(): List<CachedProjectInfo> = withContext(Dispatchers.IO) {
        try {
            val cachedProjectsJson = prefs.getString(KEY_CACHED_PROJECTS, "[]")
            val type = object : TypeToken<List<CachedProjectInfo>>() {}.type
            gson.fromJson(cachedProjectsJson, type) ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cached projects", e)
            emptyList()
        }
    }
    
    /**
     * 获取缓存的项目信息
     */
    suspend fun getCachedProjectInfo(projectId: String): CachedProjectInfo? = withContext(Dispatchers.IO) {
        try {
            val metadataFile = File(getProjectCacheDirectory(projectId), METADATA_FILE)
            if (metadataFile.exists()) {
                val json = metadataFile.readText()
                gson.fromJson(json, CachedProjectInfo::class.java)
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cached project info: $projectId", e)
            null
        }
    }
    
    /**
     * 加载缓存的填色数据
     */
    suspend fun loadCachedColoringData(projectId: String): Result<ColoringData> = withContext(Dispatchers.IO) {
        try {
            val jsonFile = File(getProjectCacheDirectory(projectId), "data.json")
            if (jsonFile.exists()) {
                val coloringData = loadColoringDataFromFile(jsonFile)
                if (coloringData != null) {
                    // 更新访问时间
                    updateLastAccessTime(projectId)
                    Result.success(coloringData)
                } else {
                    Result.failure(Exception("Failed to parse cached coloring data"))
                }
            } else {
                Result.failure(Exception("Cached coloring data not found"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading cached coloring data: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 加载缓存的outline图片
     */
    suspend fun loadCachedOutlineBitmap(projectId: String): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            val outlineFile = File(getProjectCacheDirectory(projectId), "outline.png")
            if (outlineFile.exists()) {
                val bitmap = BitmapFactory.decodeFile(outlineFile.absolutePath)
                if (bitmap != null) {
                    Result.success(bitmap)
                } else {
                    Result.failure(Exception("Failed to decode cached outline bitmap"))
                }
            } else {
                Result.failure(Exception("Cached outline bitmap not found"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading cached outline bitmap: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 删除缓存的项目
     */
    suspend fun deleteProject(projectId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting cached project: $projectId")
            
            // 1. 删除项目文件
            val projectCacheDir = getProjectCacheDirectory(projectId)
            val deleted = projectCacheDir.deleteRecursively()
            
            // 2. 更新缓存索引
            if (deleted) {
                removeCacheIndex(projectId)
            }
            
            Log.d(TAG, "Project deletion result: $deleted for $projectId")
            deleted
            
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting cached project: $projectId", e)
            false
        }
    }
    
    /**
     * 清理过期缓存
     */
    suspend fun cleanupExpiredCache(maxAgeMillis: Long = 30L * 24 * 60 * 60 * 1000): Int = withContext(Dispatchers.IO) {
        try {
            val currentTime = System.currentTimeMillis()
            val cachedProjects = getCachedProjects()
            var cleanedCount = 0
            
            for (project in cachedProjects) {
                if (currentTime - project.lastAccessTime > maxAgeMillis) {
                    if (deleteProject(project.projectId)) {
                        cleanedCount++
                    }
                }
            }
            
            Log.d(TAG, "Cleaned up $cleanedCount expired cached projects")
            cleanedCount
            
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up expired cache", e)
            0
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    suspend fun getCacheStats(): ResourceCacheStats = withContext(Dispatchers.IO) {
        try {
            val cachedProjects = getCachedProjects()
            val totalSize = cachedProjects.sumOf { it.fileSize }
            val totalCount = cachedProjects.size
            
            ResourceCacheStats(
                totalProjects = totalCount,
                totalSizeBytes = totalSize,
                oldestCacheTime = cachedProjects.minOfOrNull { it.cacheTime } ?: 0L,
                newestCacheTime = cachedProjects.maxOfOrNull { it.cacheTime } ?: 0L
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cache stats", e)
            ResourceCacheStats(0, 0, 0, 0)
        }
    }

    /**
     * 同步获取缓存的项目列表（用于内部操作）
     */
    private fun getCachedProjectsSync(): List<CachedProjectInfo> {
        return try {
            val cachedProjectsJson = prefs.getString(KEY_CACHED_PROJECTS, "[]")
            val type = object : TypeToken<List<CachedProjectInfo>>() {}.type
            gson.fromJson(cachedProjectsJson, type) ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cached projects sync", e)
            emptyList()
        }
    }

    // 私有辅助方法
    private fun getProjectCacheDirectory(projectId: String): File {
        return File(context.filesDir, "$CACHE_DIR/$PROJECTS_DIR/$projectId")
    }
    
    private fun loadColoringDataFromFile(file: File): ColoringData? {
        return try {
            val simpleAssetManager = SimpleAssetManager(context)
            val json = file.readText()
            // 使用SimpleAssetManager的解析逻辑
            gson.fromJson(json, ColoringData::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading coloring data from file", e)
            null
        }
    }
    
    private fun calculateTotalFileSize(directory: File): Long {
        return directory.walkTopDown().filter { it.isFile }.sumOf { it.length() }
    }
    
    private fun saveProjectMetadata(projectId: String, info: CachedProjectInfo) {
        val metadataFile = File(getProjectCacheDirectory(projectId), METADATA_FILE)
        metadataFile.writeText(gson.toJson(info))
    }
    
    private fun updateCacheIndex(projectId: String, info: CachedProjectInfo) {
        val cachedProjects = getCachedProjectsSync().toMutableList()
        cachedProjects.removeAll { it.projectId == projectId }
        cachedProjects.add(info)
        
        val json = gson.toJson(cachedProjects)
        prefs.edit().putString(KEY_CACHED_PROJECTS, json).apply()
    }
    
    private fun removeCacheIndex(projectId: String) {
        val cachedProjects = getCachedProjectsSync().toMutableList()
        cachedProjects.removeAll { it.projectId == projectId }
        
        val json = gson.toJson(cachedProjects)
        prefs.edit().putString(KEY_CACHED_PROJECTS, json).apply()
    }
    
    private suspend fun updateLastAccessTime(projectId: String) {
        val info = getCachedProjectInfo(projectId)
        if (info != null) {
            val updatedInfo = info.copy(
                lastAccessTime = System.currentTimeMillis(),
                accessCount = info.accessCount + 1
            )
            saveProjectMetadata(projectId, updatedInfo)
            updateCacheIndex(projectId, updatedInfo)
        }
    }
}

/**
 * 资源缓存统计信息
 */
data class ResourceCacheStats(
    val totalProjects: Int,
    val totalSizeBytes: Long,
    val oldestCacheTime: Long,
    val newestCacheTime: Long
) {
    val totalSizeMB: Float
        get() = totalSizeBytes / (1024f * 1024f)
}
