package com.example.coloringproject.utils

import android.content.Context
import android.os.Build

/**
 * 性能配置类
 * 根据设备性能自动调整优化级别
 */
object PerformanceConfig {
    
    // 性能级别枚举
    enum class PerformanceLevel {
        HIGH_PERFORMANCE,    // 高性能模式：最快启动，可能牺牲一些功能
        BALANCED,           // 平衡模式：性能和功能的平衡
        FULL_FEATURES       // 完整功能模式：所有功能开启
    }
    
    // 当前性能级别
    private var currentLevel: PerformanceLevel = PerformanceLevel.BALANCED
    
    /**
     * 根据设备自动检测性能级别
     */
    fun detectPerformanceLevel(context: Context): PerformanceLevel {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory() / 1024 / 1024 // MB
        val processorCount = runtime.availableProcessors()
        val sdkVersion = Build.VERSION.SDK_INT
        
        return when {
            // 高端设备：大内存 + 多核心 + 新系统
            maxMemory >= 512 && processorCount >= 6 && sdkVersion >= Build.VERSION_CODES.O -> {
                PerformanceLevel.FULL_FEATURES
            }
            // 中端设备：中等配置
            maxMemory >= 256 && processorCount >= 4 -> {
                PerformanceLevel.BALANCED
            }
            // 低端设备：优先性能
            else -> {
                PerformanceLevel.HIGH_PERFORMANCE
            }
        }.also {
            currentLevel = it
            android.util.Log.d("PerformanceConfig", "检测到设备性能级别: $it (内存: ${maxMemory}MB, 核心: $processorCount, SDK: $sdkVersion)")
        }
    }
    
    /**
     * 手动设置性能级别
     */
    fun setPerformanceLevel(level: PerformanceLevel) {
        currentLevel = level
        android.util.Log.d("PerformanceConfig", "手动设置性能级别: $level")
    }
    
    /**
     * 获取当前性能级别
     */
    fun getCurrentLevel(): PerformanceLevel = currentLevel
    
    // 具体的性能配置选项
    
    /**
     * 是否启用共享元素过渡
     */
    fun enableSharedElementTransition(): Boolean = when (currentLevel) {
        PerformanceLevel.HIGH_PERFORMANCE -> false
        PerformanceLevel.BALANCED -> false  // 暂时禁用以提升性能
        PerformanceLevel.FULL_FEATURES -> true
    }
    
    /**
     * 是否启用触摸缓冲区
     */
    fun enableTouchBuffer(): Boolean = when (currentLevel) {
        PerformanceLevel.HIGH_PERFORMANCE -> false
        PerformanceLevel.BALANCED -> false  // 暂时禁用以提升性能
        PerformanceLevel.FULL_FEATURES -> true
    }
    
    /**
     * 区域位图创建的批次大小
     */
    fun getRegionBatchSize(): Int = when (currentLevel) {
        PerformanceLevel.HIGH_PERFORMANCE -> 100
        PerformanceLevel.BALANCED -> 50
        PerformanceLevel.FULL_FEATURES -> 20
    }
    
    /**
     * 是否启用异步初始化
     */
    fun enableAsyncInitialization(): Boolean = true // 所有级别都启用
    
    /**
     * 是否启用性能监控
     */
    fun enablePerformanceMonitoring(): Boolean = when (currentLevel) {
        PerformanceLevel.HIGH_PERFORMANCE -> false  // 高性能模式下禁用监控以减少开销
        PerformanceLevel.BALANCED -> true
        PerformanceLevel.FULL_FEATURES -> true
    }
    
    /**
     * 马赛克映射构建策略
     */
    fun getHintMappingStrategy(): HintMappingStrategy = when (currentLevel) {
        PerformanceLevel.HIGH_PERFORMANCE -> HintMappingStrategy.LAZY_BUILD
        PerformanceLevel.BALANCED -> HintMappingStrategy.LAZY_BUILD
        PerformanceLevel.FULL_FEATURES -> HintMappingStrategy.IMMEDIATE_BUILD
    }
    
    /**
     * 初始化超时时间（毫秒）
     */
    fun getInitializationTimeout(): Long = when (currentLevel) {
        PerformanceLevel.HIGH_PERFORMANCE -> 3000L
        PerformanceLevel.BALANCED -> 5000L
        PerformanceLevel.FULL_FEATURES -> 10000L
    }
    
    /**
     * 是否启用内存优化
     */
    fun enableMemoryOptimization(): Boolean = when (currentLevel) {
        PerformanceLevel.HIGH_PERFORMANCE -> true
        PerformanceLevel.BALANCED -> true
        PerformanceLevel.FULL_FEATURES -> false
    }
    
    enum class HintMappingStrategy {
        IMMEDIATE_BUILD,  // 立即构建
        LAZY_BUILD,       // 延迟构建
        ON_DEMAND_BUILD   // 按需构建
    }
    
    /**
     * 生成性能配置报告
     */
    fun generateConfigReport(): String {
        val report = StringBuilder()
        report.appendLine("=== 性能配置报告 ===")
        report.appendLine("当前级别: $currentLevel")
        report.appendLine("共享元素过渡: ${enableSharedElementTransition()}")
        report.appendLine("触摸缓冲区: ${enableTouchBuffer()}")
        report.appendLine("区域批次大小: ${getRegionBatchSize()}")
        report.appendLine("异步初始化: ${enableAsyncInitialization()}")
        report.appendLine("性能监控: ${enablePerformanceMonitoring()}")
        report.appendLine("马赛克映射策略: ${getHintMappingStrategy()}")
        report.appendLine("初始化超时: ${getInitializationTimeout()}ms")
        report.appendLine("内存优化: ${enableMemoryOptimization()}")
        return report.toString()
    }
}