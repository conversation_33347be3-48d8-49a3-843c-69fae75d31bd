package com.example.coloringproject.network

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import java.net.InetAddress
import java.net.NetworkInterface
import java.util.*
import java.net.URL
import java.net.HttpURLConnection
import java.io.IOException
import java.net.UnknownHostException

/**
 * 网络配置管理器
 * 自动检测和配置服务器地址
 */
object NetworkConfig {
    private const val TAG = "NetworkConfig"
    
    // 默认端口
    private const val DEFAULT_PORT = 8083
    

    /**
     * 获取服务器基础URL（不包含API路径）
     */
    fun getBaseServerUrl(context: Context): String {
        // 检测设备类型和网络环境
        val deviceIP = getDeviceIP()
        Log.d(TAG, "Device IP: $deviceIP")

        // 如果设备在192.168.110.x网段，直接使用开发机器IP
        if (deviceIP?.startsWith("192.168.110.") == true) {
            Log.d(TAG, "Device in same network segment, using known host IP")
            Log.d(TAG, "Using same network segment host IP: **************")
            Log.d(TAG, "Using default host IP: **************")
        return "http://**************:$DEFAULT_PORT"
        }

        // 如果是模拟器环境（通常设备IP为10.0.2.x）
        if (deviceIP?.startsWith("10.0.2.") == true) {
            Log.d(TAG, "Emulator detected, using emulator host address")
            return "http://********:$DEFAULT_PORT"
        }

        // 尝试智能检测
        val hostIP = getHostMachineIP(context)
        if (hostIP != null) {
            Log.d(TAG, "Found host machine IP: $hostIP")
            return "http://$hostIP:$DEFAULT_PORT"
        }

        // 默认使用已知的开发机器IP
        Log.d(TAG, "Using default known host IP")
        Log.d(TAG, "Using default host IP: **************")
        return "http://**************:$DEFAULT_PORT"
    }

    /**
     * 获取最佳的服务器API URL（包含API路径）
     */
    fun getBestServerUrl(context: Context): String {
        return "${getBaseServerUrl(context)}/api/client/"
    }
    
    /**
     * 获取设备当前IP地址
     */
    private fun getDeviceIP(): String? {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            for (networkInterface in Collections.list(interfaces)) {
                if (networkInterface.isLoopback || !networkInterface.isUp) continue

                for (address in Collections.list(networkInterface.inetAddresses)) {
                    if (!address.isLoopbackAddress && address is InetAddress && address.hostAddress?.contains(":") == false) {
                        val ip = address.hostAddress
                        if (ip != null && isValidIP(ip)) {
                            return ip
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting device IP", e)
        }
        return null
    }

    /**
     * 尝试获取开发机器的IP地址
     */
    private fun getHostMachineIP(context: Context): String? {
        try {
            // 方法1：通过WiFi管理器获取网关IP（通常是路由器IP）
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val dhcpInfo = wifiManager.dhcpInfo
            if (dhcpInfo != null && dhcpInfo.gateway != 0) {
                val gatewayIP = intToIp(dhcpInfo.gateway)
                Log.d(TAG, "Gateway IP: $gatewayIP")
                
                // 尝试常见的开发机器IP（通常在同一网段）
                val networkPrefix = gatewayIP.substringBeforeLast(".")
                val possibleIPs = listOf(
                    "$networkPrefix.100",
                    "$networkPrefix.101",
                    "$networkPrefix.102",
                    "$networkPrefix.2"
                )
                
                for (ip in possibleIPs) {
                    if (isValidIP(ip)) {
                        return ip
                    }
                }
            }
            
            // 方法2：通过网络接口获取本机IP（如果在同一设备上运行）
            val interfaces = NetworkInterface.getNetworkInterfaces()
            for (networkInterface in Collections.list(interfaces)) {
                if (networkInterface.isLoopback || !networkInterface.isUp) continue
                
                for (address in Collections.list(networkInterface.inetAddresses)) {
                    if (!address.isLoopbackAddress && address is InetAddress && address.hostAddress?.contains(":") == false) {
                        val ip = address.hostAddress
                        if (ip != null && isValidIP(ip)) {
                            Log.d(TAG, "Found network interface IP: $ip")
                            return ip
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting host machine IP", e)
        }
        
        return null
    }
    
    /**
     * 将整数IP转换为字符串格式
     */
    private fun intToIp(ip: Int): String {
        return "${ip and 0xFF}.${ip shr 8 and 0xFF}.${ip shr 16 and 0xFF}.${ip shr 24 and 0xFF}"
    }
    
    /**
     * 验证IP地址格式
     */
    private fun isValidIP(ip: String): Boolean {
        return try {
            val parts = ip.split(".")
            if (parts.size != 4) return false
            
            for (part in parts) {
                val num = part.toInt()
                if (num < 0 || num > 255) return false
            }
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 检查服务器是否可达（简单的连接测试）
     */
    suspend fun isServerReachable(baseUrl: String): Boolean {
        return try {
            Log.d(TAG, "Testing server reachability: $baseUrl")
            val url = URL(baseUrl)
            val connection = url.openConnection() as HttpURLConnection
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            connection.requestMethod = "HEAD"
            val responseCode = connection.responseCode
            val reachable = responseCode in 200..299
            Log.d(TAG, "Server reachability result for $baseUrl: ${if (reachable) "Reachable (HTTP $responseCode)" else "Not reachable (HTTP $responseCode)"}")
            reachable
        } catch (e: UnknownHostException) {
            Log.e(TAG, "Server host not found: $baseUrl", e)
            false
        } catch (e: IOException) {
            Log.e(TAG, "Network error while testing reachability to $baseUrl", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error testing server reachability for $baseUrl", e)
            false
        }
    }
}