package com.example.coloringproject.network

import android.content.Context
import android.util.Log
import com.example.coloringproject.data.DailyRecommendation
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrl
import okio.buffer
import okio.sink
import java.io.File
import java.io.IOException


/**
 * API响应数据类
 */


import java.util.concurrent.TimeUnit

/**
 * 资源下载管理器
 * 负责从服务器下载填色项目资源
 */
class ResourceDownloadManager(private val context: Context) {

    data class ApiResponse<T>(
        val status: String,
        val data: T?,
        val error: ErrorInfo? = null,
        val timestamp: String
    )

    /**
     * 错误信息数据类
     */
    data class ErrorInfo(
        val code: String,
        val message: String,
        val details: String
    )

    /**
     * 更新响应数据类（匹配后端API）
     */
    data class UpdateResponse(
        val hasUpdates: Boolean,
        val appUpdate: AppUpdateInfo?,
        val contentUpdates: ContentUpdateInfo
    )

    /**
     * 应用更新信息
     */
    data class AppUpdateInfo(
        val latestVersion: String,
        val currentVersion: String,
        val downloadUrl: String,
        val releaseNotes: String,
        val forceUpdate: Boolean,
        val releaseDate: String,
        val fileSize: Long,
        val updateType: String
    )

    /**
     * 内容更新信息
     */
    data class ContentUpdateInfo(
        val newProjectsCount: Int,
        val updatedProjectsCount: Int,
        val lastContentUpdate: String,
        val updateSummary: String
    )
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    // 动态获取最佳服务器URL
    private val baseUrl: String by lazy {
        NetworkConfig.getBestServerUrl(context)
    }

    companion object {
        private const val TAG = "ResourceDownloadManager"

        // API端点
        private const val PROJECTS_ENDPOINT = "projects"
        private const val CATEGORIES_ENDPOINT = "categories"
        private const val DAILY_ENDPOINT = "daily"
        private const val UPDATES_ENDPOINT = "updates"
        private const val DOWNLOAD_ENDPOINT = "download"

        // 本地存储路径
        private const val DOWNLOAD_DIR = "downloaded_projects"
        private const val TEMP_DIR = "temp_downloads"
    }
    
    /**
     * 下载项目数据
     */
    data class DownloadedProject(
        val projectId: String,
        val jsonFile: File,
        val outlineFile: File,
        val regionFile: File? = null,
        val thumbnailFile: File? = null,
        val version: String,
        val downloadTime: Long = System.currentTimeMillis()
    )
    
    /**
     * 下载进度回调
     */
    interface DownloadProgressCallback {
        fun onProgress(bytesDownloaded: Long, totalBytes: Long, percentage: Float)
        fun onComplete(downloadedProject: DownloadedProject)
        fun onError(error: Throwable)
    }
    
    /**
     * 获取项目列表
     */
    suspend fun getProjectsList(
        category: String? = null,
        difficulty: String? = null,
        featured: Boolean? = null,
        page: Int = 1,
        pageSize: Int = 20
    ): Result<ApiResponse<ProjectListResponse>> = withContext(Dispatchers.IO) {
        try {
            val urlBuilder = "$baseUrl$PROJECTS_ENDPOINT".toHttpUrl().newBuilder()
                .addQueryParameter("page", page.toString())
                .addQueryParameter("page_size", pageSize.toString())

            category?.let {
                urlBuilder.addQueryParameter("category", it)
            }

            difficulty?.let {
                urlBuilder.addQueryParameter("difficulty", it)
            }

            featured?.let {
                urlBuilder.addQueryParameter("featured", it.toString())
            }

            val request = Request.Builder()
                .url(urlBuilder.build())
                .addHeader("Accept", "application/json")
                .addHeader("User-Agent", "ColoringApp-Android/1.0")
                .build()

            val response = client.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val apiResponse = parseProjectListResponse(responseBody)
                    Result.success(apiResponse)
                } else {
                    Result.failure(IOException("Empty response body"))
                }
            } else {
                Result.failure(IOException("HTTP ${response.code}: ${response.message}"))
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error getting projects list", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取分类列表
     */
    suspend fun getCategoriesList(): Result<ApiResponse<CategoriesResponse>> = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("$baseUrl$CATEGORIES_ENDPOINT")
                .addHeader("Accept", "application/json")
                .addHeader("User-Agent", "ColoringApp-Android/1.0")
                .build()

            val response = client.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val apiResponse = parseCategoriesResponse(responseBody)
                    Result.success(apiResponse)
                } else {
                    Result.failure(IOException("Empty response body"))
                }
            } else {
                Result.failure(IOException("HTTP ${response.code}: ${response.message}"))
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error getting categories list", e)
            Result.failure(e)
        }
    }

    /**
     * 获取每日推荐
     */
    suspend fun getDailyRecommendations(): Result<ApiResponse<com.example.coloringproject.data.DailyResponse>> = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("$baseUrl$DAILY_ENDPOINT")
                .addHeader("Accept", "application/json")
                .addHeader("User-Agent", "ColoringApp-Android/1.0")
                .build()

            val response = client.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val apiResponse = parseDailyResponse(responseBody)
                    Result.success(apiResponse)
                } else {
                    Result.failure(IOException("Empty response body"))
                }
            } else {
                Result.failure(IOException("HTTP ${response.code}: ${response.message}"))
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error getting daily recommendations", e)
            Result.failure(e)
        }
    }

    /**
     * 检查更新
     */
    suspend fun checkUpdates(
        clientVersion: String,
        lastSync: String? = null
    ): Result<ApiResponse<UpdateResponse>> = withContext(Dispatchers.IO) {
        try {
            val urlBuilder = "$baseUrl$UPDATES_ENDPOINT".toHttpUrl().newBuilder()
                .addQueryParameter("client_version", clientVersion)

            lastSync?.let {
                urlBuilder.addQueryParameter("last_sync", it)
            }

            val request = Request.Builder()
                .url(urlBuilder.build())
                .addHeader("Accept", "application/json")
                .addHeader("User-Agent", "ColoringApp-Android/1.0")
                .build()

            val response = client.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val apiResponse = parseUpdateResponse(responseBody)
                    Result.success(apiResponse)
                } else {
                    Result.failure(IOException("Empty response body"))
                }
            } else {
                Result.failure(IOException("HTTP ${response.code}: ${response.message}"))
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error checking updates", e)
            Result.failure(e)
        }
    }

    /**
     * 获取项目详情
     */
    suspend fun getProjectDetail(projectId: String): Result<ProjectDetailInfo> = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url("$baseUrl$PROJECTS_ENDPOINT/$projectId")
                .addHeader("Accept", "application/json")
                .addHeader("User-Agent", "ColoringApp-Android/1.0")
                .build()

            val response = client.newCall(request).execute()

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    // 添加原始JSON调试日志
                    Log.d(TAG, "=== RAW JSON RESPONSE FOR PROJECT $projectId ===")
                    Log.d(TAG, responseBody)
                    Log.d(TAG, "=== END RAW JSON RESPONSE ===")

                    val projectDetail = parseProjectDetailResponse(responseBody)

                    // 添加解析后的对象调试
                    Log.d(TAG, "=== PARSED PROJECT DETAIL ===")
                    Log.d(TAG, "Parsed ID: ${projectDetail.id}")
                    Log.d(TAG, "Parsed Name: ${projectDetail.name}")
                    Log.d(TAG, "Parsed Files: ${projectDetail.files}")
                    Log.d(TAG, "=== END PARSED PROJECT DETAIL ===")

                    Result.success(projectDetail)
                } else {
                    Result.failure(IOException("Empty response body"))
                }
            } else {
                Result.failure(IOException("HTTP ${response.code}: ${response.message}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting project detail", e)
            Result.failure(e)
        }
    }

    /**
     * 下载项目
     */
    suspend fun downloadProject(
        projectId: String,
        onProgress: (Float) -> Unit = {}
    ): Result<DownloadedProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting download for project: $projectId")

            // 1. 获取项目信息
            val projectInfoResult = getProjectDetail(projectId)
            if (projectInfoResult.isFailure) {
                return@withContext Result.failure(
                    projectInfoResult.exceptionOrNull() ?: Exception("Failed to get project info")
                )
            }

            val projectInfo = projectInfoResult.getOrNull()!!

            // 添加详细的JSON调试日志
            Log.d(TAG, "=== PROJECT INFO DEBUG ===")
            Log.d(TAG, "Project ID: ${projectInfo.id}")
            Log.d(TAG, "Project Name: ${projectInfo.name}")
            Log.d(TAG, "Project DisplayName: ${projectInfo.displayName}")
            Log.d(TAG, "Project Files Object: ${projectInfo.files}")
            Log.d(TAG, "Files is null: ${projectInfo.files == null}")

            if (projectInfo.files != null) {
                Log.d(TAG, "JSON URL: ${projectInfo.files.jsonUrl}")
                Log.d(TAG, "Outline URL: ${projectInfo.files.outlineUrl}")
                Log.d(TAG, "JSON Size: ${projectInfo.files.jsonSize}")
                Log.d(TAG, "Outline Size: ${projectInfo.files.outlineSize}")
            }
            Log.d(TAG, "=== END PROJECT INFO DEBUG ===")

            // 2. 创建下载目录
            val downloadDir = getDownloadDirectory(projectId)
            val tempDir = getTempDirectory(projectId)

            Log.d(TAG, "=== DIRECTORY CREATION DEBUG ===")
            Log.d(TAG, "Download directory path: ${downloadDir.absolutePath}")
            Log.d(TAG, "Temp directory path: ${tempDir.absolutePath}")
            Log.d(TAG, "Download directory exists before creation: ${downloadDir.exists()}")
            Log.d(TAG, "Temp directory exists before creation: ${tempDir.exists()}")

            // 检查父目录权限
            val parentDir = downloadDir.parentFile
            Log.d(TAG, "Parent directory: ${parentDir?.absolutePath}")
            Log.d(TAG, "Parent directory exists: ${parentDir?.exists()}")
            Log.d(TAG, "Parent directory writable: ${parentDir?.canWrite()}")

            if (!downloadDir.exists()) {
                val downloadDirCreated = downloadDir.mkdirs()
                Log.d(TAG, "Download directory creation result: $downloadDirCreated")
                Log.d(TAG, "Download directory exists after creation: ${downloadDir.exists()}")
                Log.d(TAG, "Download directory writable: ${downloadDir.canWrite()}")
            }

            if (!tempDir.exists()) {
                val tempDirCreated = tempDir.mkdirs()
                Log.d(TAG, "Temp directory creation result: $tempDirCreated")
                Log.d(TAG, "Temp directory exists after creation: ${tempDir.exists()}")
                Log.d(TAG, "Temp directory writable: ${tempDir.canWrite()}")
            }
            Log.d(TAG, "=== END DIRECTORY CREATION DEBUG ===")

            // 3. 下载文件
            val downloadedFiles = mutableMapOf<String, File>()
            val fileBaseUrl = NetworkConfig.getBaseServerUrl(context)

            // 构建下载文件列表
            val filesToDownload = mutableMapOf<String, String>()

            // 检查files是否为null
            if (projectInfo.files == null) {
                Log.e(TAG, "Project files is null for project: $projectId")
                throw Exception("Project files information is missing")
            }

            if (projectInfo.files.jsonUrl.isNotEmpty()) {
                filesToDownload["json"] = if (projectInfo.files.jsonUrl.startsWith("http")) {
                    projectInfo.files.jsonUrl
                } else {
                    // 构建正确的文件下载URL
                    var jsonUrl = projectInfo.files.jsonUrl
                    Log.d(TAG, "Original jsonUrl: $jsonUrl")

                    // 强制修复服务器端的路径问题：移除错误的/uploads前缀
                    if (jsonUrl.startsWith("/uploads/")) {
                        jsonUrl = jsonUrl.removePrefix("/uploads")
                        Log.d(TAG, "Fixed jsonUrl path from /uploads to: $jsonUrl")
                    } else {
                        Log.d(TAG, "jsonUrl does not start with /uploads/, no fix needed")
                    }

                    // 额外的强制修复：确保使用正确的URL格式
                    if (jsonUrl.contains("animal-3_outline.json")) {
                        jsonUrl = "/default/animal-3_outline.json"
                        Log.d(TAG, "Force fixed animal-3 jsonUrl to: $jsonUrl")
                    }

                    if (jsonUrl.startsWith("/api/")) {
                        // 已经包含/api/前缀，直接拼接
                        "$fileBaseUrl$jsonUrl"
                    } else {
                        // 需要添加/api/files前缀
                        "$fileBaseUrl/api/files$jsonUrl"
                    }
                }
            }
            if (projectInfo.files.outlineUrl.isNotEmpty()) {
                filesToDownload["outline"] = if (projectInfo.files.outlineUrl.startsWith("http")) {
                    projectInfo.files.outlineUrl
                } else {
                    // 构建正确的文件下载URL
                    if (projectInfo.files.outlineUrl.startsWith("/api/")) {
                        // 已经包含/api/前缀，直接拼接
                        "$fileBaseUrl${projectInfo.files.outlineUrl}"
                    } else {
                        // 需要添加/api/files前缀
                        "$fileBaseUrl/api/files${projectInfo.files.outlineUrl}"
                    }
                }
            }

            val totalFiles = filesToDownload.size
            var completedFiles = 0

            Log.d(TAG, "Downloading $totalFiles files for project $projectId")

            for ((fileType, downloadUrl) in filesToDownload) {
                Log.d(TAG, "Downloading $fileType from: $downloadUrl")
                val tempFile = File(tempDir, "${projectId}_${fileType}")
                // 使用标准的文件扩展名，以便OptimizedResourceLoader能正确找到
                val finalFileName = when (fileType) {
                    "json" -> "$projectId.json"
                    "outline" -> "$projectId.png"
                    else -> "${projectId}_${fileType}"
                }
                val finalFile = File(downloadDir, finalFileName)

                val downloadResult = downloadFile(downloadUrl, tempFile) { progress ->
                    val overallProgress = (completedFiles + progress) / totalFiles
                    onProgress(overallProgress)
                }

                if (downloadResult.isSuccess) {
                    // 移动到最终位置
                    Log.d(TAG, "=== FILE MOVE DEBUG ===")
                    Log.d(TAG, "Moving file from: ${tempFile.absolutePath}")
                    Log.d(TAG, "Moving file to: ${finalFile.absolutePath}")
                    Log.d(TAG, "Temp file exists: ${tempFile.exists()}")
                    Log.d(TAG, "Temp file size: ${tempFile.length()}")
                    Log.d(TAG, "Final file exists before move: ${finalFile.exists()}")
                    Log.d(TAG, "Final directory exists: ${finalFile.parentFile?.exists()}")
                    Log.d(TAG, "Final directory writable: ${finalFile.parentFile?.canWrite()}")

                    val moveResult = tempFile.renameTo(finalFile)
                    Log.d(TAG, "File move result: $moveResult")
                    Log.d(TAG, "Final file exists after move: ${finalFile.exists()}")
                    Log.d(TAG, "Final file size: ${finalFile.length()}")
                    Log.d(TAG, "=== END FILE MOVE DEBUG ===")

                    if (moveResult && finalFile.exists()) {
                        downloadedFiles[fileType] = finalFile
                        completedFiles++
                        Log.d(TAG, "Successfully downloaded $fileType for project $projectId")
                    } else {
                        Log.e(TAG, "Failed to move file for $fileType")
                        tempDir.deleteRecursively()
                        return@withContext Result.failure(Exception("Failed to move $fileType file"))
                    }
                } else {
                    // 清理临时文件
                    tempDir.deleteRecursively()
                    return@withContext Result.failure(
                        downloadResult.exceptionOrNull() ?: Exception("Failed to download $fileType")
                    )
                }
            }

            // 4. 清理临时目录
            tempDir.deleteRecursively()

            // 5. 创建下载结果
            val downloadedProject = DownloadedProject(
                projectId = projectId,
                jsonFile = downloadedFiles["json"] ?: throw Exception("JSON file not downloaded"),
                outlineFile = downloadedFiles["outline"] ?: throw Exception("Outline file not downloaded"),
                regionFile = downloadedFiles["region"],
                thumbnailFile = downloadedFiles["thumbnail"],
                version = projectInfo.version
            )

            Log.d(TAG, "Download completed for project: $projectId")
            Result.success(downloadedProject)

        } catch (e: Exception) {
            Log.e(TAG, "Error downloading project: $projectId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 下载单个文件
     */
    private suspend fun downloadFile(
        url: String,
        targetFile: File,
        onProgress: (Float) -> Unit = {}
    ): Result<File> = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url(url)
                .build()
            
            val response = client.newCall(request).execute()
            
            if (response.isSuccessful) {
                val body = response.body
                if (body != null) {
                    val contentLength = body.contentLength()
                    val sink = targetFile.sink().buffer()
                    
                    var totalBytesRead = 0L
                    val buffer = ByteArray(8192)
                    
                    body.source().use { source ->
                        sink.use { bufferedSink ->
                            var bytesRead: Long
                            while (source.read(bufferedSink.buffer, 8192).also { bytesRead = it } != -1L) {
                                totalBytesRead += bytesRead
                                
                                if (contentLength > 0) {
                                    val progress = totalBytesRead.toFloat() / contentLength
                                    onProgress(progress)
                                }
                            }
                        }
                    }
                    
                    Result.success(targetFile)
                } else {
                    Result.failure(IOException("Empty response body"))
                }
            } else {
                Result.failure(IOException("HTTP ${response.code}: ${response.message}"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading file: $url", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取下载URL
     */
    fun getDownloadUrl(projectId: String): String {
        return "$baseUrl$DOWNLOAD_ENDPOINT/$projectId"
    }

    /**
     * 获取基础URL
     */
    fun getServerBaseUrl(): String {
        return baseUrl
    }
    
    /**
     * 获取下载目录
     */
    private fun getDownloadDirectory(projectId: String): File {
        return File(context.filesDir, "$DOWNLOAD_DIR/$projectId")
    }
    
    /**
     * 获取临时目录
     */
    private fun getTempDirectory(projectId: String): File {
        return File(context.cacheDir, "$TEMP_DIR/$projectId")
    }
    
    /**
     * 解析项目列表响应
     */
    private fun parseProjectListResponse(json: String): ApiResponse<ProjectListResponse> {
        return try {
            val gson = Gson()
            val type = object : TypeToken<ApiResponse<ProjectListResponse>>() {}.type
            gson.fromJson(json, type)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing project list response", e)
            ApiResponse(
                status = "error",
                data = null,
                error = ErrorInfo("PARSE_ERROR", "Failed to parse response", ""),
                timestamp = ""
            )
        }
    }

    /**
     * 解析项目详情响应
     */
    private fun parseProjectDetailResponse(json: String): ProjectDetailInfo {
        return try {
            val gson = Gson()
            val type = object : TypeToken<ApiResponse<ProjectDetailInfo>>() {}.type
            val apiResponse: ApiResponse<ProjectDetailInfo> = gson.fromJson(json, type)

            Log.d(TAG, "=== API RESPONSE PARSING ===")
            Log.d(TAG, "Status: ${apiResponse.status}")
            Log.d(TAG, "Data is null: ${apiResponse.data == null}")
            Log.d(TAG, "Error: ${apiResponse.error}")

            if (apiResponse.status == "success" && apiResponse.data != null) {
                Log.d(TAG, "Successfully parsed project detail")
                apiResponse.data
            } else {
                Log.e(TAG, "API response error: ${apiResponse.error?.message}")
                throw Exception("API Error: ${apiResponse.error?.message ?: "Unknown error"}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing project detail response", e)
            throw e
        }
    }

    /**
     * 解析分类列表响应
     */
    private fun parseCategoriesResponse(json: String): ApiResponse<CategoriesResponse> {
        return try {
            val gson = Gson()
            val type = object : TypeToken<ApiResponse<CategoriesResponse>>() {}.type
            gson.fromJson(json, type)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing categories response", e)
            ApiResponse(
                status = "error",
                data = null,
                error = ErrorInfo("PARSE_ERROR", "Failed to parse response", ""),
                timestamp = ""
            )
        }
    }

    /**
     * 解析每日推荐响应
     */
    private fun parseDailyResponse(json: String): ApiResponse<com.example.coloringproject.data.DailyResponse> {
        return try {
            val gson = Gson()
            val type = object : TypeToken<ApiResponse<com.example.coloringproject.data.DailyResponse>>() {}.type
            gson.fromJson(json, type)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing daily response", e)
            ApiResponse(
                status = "error",
                data = null,
                error = ErrorInfo("PARSE_ERROR", "Failed to parse response", ""),
                timestamp = ""
            )
        }
    }

    /**
     * 解析更新检查响应
     */
    private fun parseUpdateResponse(json: String): ApiResponse<UpdateResponse> {
        return try {
            val gson = Gson()
            val type = object : TypeToken<ApiResponse<UpdateResponse>>() {}.type
            gson.fromJson(json, type)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing update response", e)
            ApiResponse(
                status = "error",
                data = null,
                error = ErrorInfo("PARSE_ERROR", "Failed to parse response", ""),
                timestamp = ""
            )
        }
    }
}

/**
 * API统一响应格式
 */
//data class ApiResponse<T>(
//    val status: String,
//    val data: T?,
//    val error: ErrorInfo?,
//    val timestamp: String
//)
//
//data class ErrorInfo(
//    val code: String,
//    val message: String,
//    val timestamp: String
//)

/**
 * 项目列表响应
 */
data class ProjectListResponse(
    val projects: List<RemoteProjectInfo>,
    val pagination: PaginationInfo
)

data class PaginationInfo(
    val currentPage: Int,
    val totalPages: Int,
    val totalCount: Int,
    val pageSize: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean
)

/**
 * 远程项目信息 - 匹配后端API格式
 */
data class RemoteProjectInfo(
    val id: String,
    val name: String,
    val displayName: String,
    val description: String,
    val category: String,
    val difficulty: String,
    val totalRegions: Int,
    val totalColors: Int,
    val estimatedTimeMinutes: Int,
    val version: String,
    val fileSize: Long,
    val thumbnailUrl: String?,
    val previewUrl: String?,
    val tags: List<String>,
    val releaseDate: String,
    val popularity: Int,
    val rating: Double,
    val downloadCount: Long,
    val files: ProjectFilesInfo
)

data class ProjectFilesInfo(
    val jsonUrl: String,
    val outlineUrl: String,
    val jsonSize: Long,
    val outlineSize: Long,
    val jsonChecksum: String,
    val outlineChecksum: String
)

/**
 * 分类列表响应
 */
data class CategoriesResponse(
    val categories: List<CategoryInfo>
)

data class CategoryInfo(
    val id: String,
    val name: String,
    val description: String,
    val iconUrl: String?,
    val projectCount: Int,
    val sortOrder: Int
)

/**
 * 每日推荐响应
 */
data class DailyResponse(
    val todayChallenge: TodayChallengeInfo?,
    val featuredProjects: List<FeaturedProjectInfo>,
    val trendingProjects: List<TrendingProjectInfo>
)

data class TodayChallengeInfo(
    val id: String,
    val projectId: String,
    val displayName: String,
    val description: String,
    val rewardPoints: Int,
    val expiresAt: String
)

data class FeaturedProjectInfo(
    val id: String,
    val displayName: String,
    val category: String,
    val difficulty: String,
    val thumbnailUrl: String?,
    val reason: String
)

data class TrendingProjectInfo(
    val id: String,
    val displayName: String,
    val category: String,
    val difficulty: String,
    val thumbnailUrl: String?,
    val reason: String
)



data class AppUpdateInfo(
    val latestVersion: String,
    val currentVersion: String,
    val isRequired: Boolean,
    val downloadUrl: String,
    val releaseNotes: String
)

data class ContentUpdateInfo(
    val newProjectsCount: Int,
    val updatedProjectsCount: Int,
    val lastContentUpdate: String
)

/**
 * 项目详情信息
 */
data class ProjectDetailInfo(
    val id: String,
    val name: String,
    val displayName: String,
    val description: String,
    val category: String,
    val difficulty: String,
    val totalRegions: Int,
    val totalColors: Int,
    val estimatedTimeMinutes: Int?,
    val thumbnailUrl: String?,
    val previewUrl: String?,
    val version: String,
    val fileSize: Long,
    val files: ProjectFiles?,
    val tags: List<String>,
    val releaseDate: String?,
    val popularity: Int,
    val rating: Float,
    val downloadCount: Int
)

/**
 * 项目文件信息
 */
data class ProjectFiles(
    @com.google.gson.annotations.SerializedName("jsonUrl")
    val jsonUrl: String,
    @com.google.gson.annotations.SerializedName("outlineUrl")
    val outlineUrl: String,
    @com.google.gson.annotations.SerializedName("jsonSize")
    val jsonSize: Long,
    @com.google.gson.annotations.SerializedName("outlineSize")
    val outlineSize: Long,
    @com.google.gson.annotations.SerializedName("jsonChecksum")
    val jsonChecksum: String?,
    @com.google.gson.annotations.SerializedName("outlineChecksum")
    val outlineChecksum: String?
)
