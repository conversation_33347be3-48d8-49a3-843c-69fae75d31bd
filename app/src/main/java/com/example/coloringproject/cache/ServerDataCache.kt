package com.example.coloringproject.cache

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.coloringproject.utils.LightweightResourceValidator
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 服务器数据缓存管理器
 * 负责缓存服务器项目数据，避免重复下载
 */
class ServerDataCache(private val context: Context) {
    
    companion object {
        private const val TAG = "ServerDataCache"
        private const val CACHE_PREFS = "server_data_cache"
        private const val CACHE_DIR = "server_cache"
        
        // 缓存过期时间（24小时）
        private const val CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000L
        
        // 缓存版本键
        private const val KEY_CACHE_VERSION = "cache_version"
        private const val KEY_LAST_UPDATE = "last_update_"
        private const val KEY_DATA_VERSION = "data_version_"
        private const val KEY_PROJECT_COUNT = "project_count_"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(CACHE_PREFS, Context.MODE_PRIVATE)
    private val gson = Gson()
    private val cacheDir = File(context.cacheDir, CACHE_DIR)
    
    init {
        // 确保缓存目录存在
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
    }
    
    /**
     * 缓存项目数据信息
     */
    data class CachedProjectData(
        val categoryId: String,
        val projects: List<LightweightResourceValidator.LightweightProject>,
        val cacheTime: Long,
        val dataVersion: String? = null,
        val serverTimestamp: String? = null
    )
    
    /**
     * 检查分类数据是否需要更新
     */
    suspend fun shouldUpdateCategory(
        categoryId: String, 
        serverTimestamp: String? = null,
        serverProjectCount: Int = 0
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val cacheFile = getCacheFile(categoryId)
            
            // 如果缓存文件不存在，需要更新
            if (!cacheFile.exists()) {
                Log.d(TAG, "分类 $categoryId 缓存不存在，需要更新")
                return@withContext true
            }
            
            // 检查缓存是否过期
            val lastUpdate = prefs.getLong(KEY_LAST_UPDATE + categoryId, 0)
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastUpdate > CACHE_EXPIRE_TIME) {
                Log.d(TAG, "分类 $categoryId 缓存已过期，需要更新")
                return@withContext true
            }
            
            // 检查服务器时间戳是否有变化
            if (serverTimestamp != null) {
                val cachedTimestamp = prefs.getString(KEY_DATA_VERSION + categoryId, null)
                if (cachedTimestamp != serverTimestamp) {
                    Log.d(TAG, "分类 $categoryId 服务器数据有更新，需要更新")
                    return@withContext true
                }
            }
            
            // 检查项目数量是否有变化
            if (serverProjectCount > 0) {
                val cachedCount = prefs.getInt(KEY_PROJECT_COUNT + categoryId, -1)
                if (cachedCount != serverProjectCount) {
                    Log.d(TAG, "分类 $categoryId 项目数量有变化 ($cachedCount -> $serverProjectCount)，需要更新")
                    return@withContext true
                }
            }
            
            Log.d(TAG, "分类 $categoryId 缓存有效，无需更新")
            false
        } catch (e: Exception) {
            Log.e(TAG, "检查分类 $categoryId 缓存状态失败", e)
            true // 出错时选择更新
        }
    }
    
    /**
     * 获取缓存的项目数据
     */
    suspend fun getCachedProjects(categoryId: String): List<LightweightResourceValidator.LightweightProject>? = withContext(Dispatchers.IO) {
        try {
            val cacheFile = getCacheFile(categoryId)
            if (!cacheFile.exists()) {
                Log.d(TAG, "分类 $categoryId 缓存文件不存在")
                return@withContext null
            }
            
            val json = cacheFile.readText()
            val cachedData = gson.fromJson<CachedProjectData>(json, CachedProjectData::class.java)
            
            Log.d(TAG, "成功读取分类 $categoryId 缓存数据: ${cachedData.projects.size} 个项目")
            cachedData.projects
        } catch (e: Exception) {
            Log.e(TAG, "读取分类 $categoryId 缓存数据失败", e)
            null
        }
    }
    
    /**
     * 缓存项目数据
     */
    suspend fun cacheProjects(
        categoryId: String,
        projects: List<LightweightResourceValidator.LightweightProject>,
        serverTimestamp: String? = null
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val cacheData = CachedProjectData(
                categoryId = categoryId,
                projects = projects,
                cacheTime = System.currentTimeMillis(),
                serverTimestamp = serverTimestamp
            )
            
            val cacheFile = getCacheFile(categoryId)
            val json = gson.toJson(cacheData)
            cacheFile.writeText(json)
            
            // 更新SharedPreferences中的元数据
            prefs.edit().apply {
                putLong(KEY_LAST_UPDATE + categoryId, cacheData.cacheTime)
                putInt(KEY_PROJECT_COUNT + categoryId, projects.size)
                serverTimestamp?.let { 
                    putString(KEY_DATA_VERSION + categoryId, it)
                }
                apply()
            }
            
            Log.d(TAG, "成功缓存分类 $categoryId 数据: ${projects.size} 个项目")
            true
        } catch (e: Exception) {
            Log.e(TAG, "缓存分类 $categoryId 数据失败", e)
            false
        }
    }
    
    /**
     * 清除指定分类的缓存
     */
    suspend fun clearCategoryCache(categoryId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val cacheFile = getCacheFile(categoryId)
            val deleted = if (cacheFile.exists()) cacheFile.delete() else true
            
            // 清除SharedPreferences中的元数据
            prefs.edit().apply {
                remove(KEY_LAST_UPDATE + categoryId)
                remove(KEY_DATA_VERSION + categoryId)
                remove(KEY_PROJECT_COUNT + categoryId)
                apply()
            }
            
            Log.d(TAG, "清除分类 $categoryId 缓存: $deleted")
            deleted
        } catch (e: Exception) {
            Log.e(TAG, "清除分类 $categoryId 缓存失败", e)
            false
        }
    }
    
    /**
     * 清除所有缓存
     */
    suspend fun clearAllCache(): Boolean = withContext(Dispatchers.IO) {
        try {
            // 删除缓存目录下的所有文件
            val deleted = if (cacheDir.exists()) {
                cacheDir.listFiles()?.all { it.delete() } ?: true
            } else true
            
            // 清除SharedPreferences
            prefs.edit().clear().apply()
            
            Log.d(TAG, "清除所有缓存: $deleted")
            deleted
        } catch (e: Exception) {
            Log.e(TAG, "清除所有缓存失败", e)
            false
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): Map<String, Any> {
        val stats = mutableMapOf<String, Any>()
        
        try {
            val cacheFiles = cacheDir.listFiles() ?: emptyArray()
            stats["cached_categories"] = cacheFiles.size
            stats["total_cache_size"] = cacheFiles.sumOf { it.length() }
            
            val categoryStats = mutableMapOf<String, Map<String, Any>>()
            for (file in cacheFiles) {
                val categoryId = file.nameWithoutExtension
                val lastUpdate = prefs.getLong(KEY_LAST_UPDATE + categoryId, 0)
                val projectCount = prefs.getInt(KEY_PROJECT_COUNT + categoryId, 0)
                val dataVersion = prefs.getString(KEY_DATA_VERSION + categoryId, "unknown")
                
                categoryStats[categoryId] = mapOf<String, Any>(
                    "last_update" to lastUpdate,
                    "project_count" to projectCount,
                    "data_version" to (dataVersion ?: "unknown"),
                    "file_size" to file.length(),
                    "is_expired" to (System.currentTimeMillis() - lastUpdate > CACHE_EXPIRE_TIME)
                )
            }
            stats["categories"] = categoryStats
            
        } catch (e: Exception) {
            Log.e(TAG, "获取缓存统计信息失败", e)
            stats["error"] = e.message ?: "Unknown error"
        }
        
        return stats
    }
    
    /**
     * 获取缓存文件
     */
    private fun getCacheFile(categoryId: String): File {
        return File(cacheDir, "$categoryId.json")
    }
}
