package com.example.coloringproject.test

import android.content.Context
import android.util.Log
import java.io.FileNotFoundException
import java.io.InputStream

/**
 * 简单的项目调试工具
 * 用于快速诊断项目加载问题
 */
object SimpleProjectDebugger {
    
    private const val TAG = "SimpleProjectDebugger"
    
    /**
     * 检查项目文件是否存在
     */
    fun checkProjectFiles(context: Context, projectId: String): FileCheckResult {
        Log.d(TAG, "检查项目文件: $projectId")
        
        val result = FileCheckResult(projectId)
        
        // 使用智能查找检查JSON文件
        try {
            val inputStream = findAssetFileQuietly(context, "$projectId.json")
            inputStream.use {
                result.jsonExists = true
                result.jsonSize = it.available()
            }
        } catch (e: Exception) {
            result.jsonError = e.message
            Log.w(TAG, "JSON文件检查失败: $projectId.json", e)
        }

        // 使用智能查找检查PNG文件
        try {
            val inputStream = findAssetFileQuietly(context, "$projectId.png")
            inputStream.use {
                result.pngExists = true
                result.pngSize = it.available()
            }
        } catch (e: Exception) {
            result.pngError = e.message
            Log.w(TAG, "PNG文件检查失败: $projectId.png", e)
        }
        
        Log.d(TAG, "文件检查结果: $result")
        return result
    }
    
    /**
     * 获取所有assets中的项目文件
     */
    fun listAllProjectFiles(context: Context): List<String> {
        val projectFiles = mutableListOf<String>()
        
        try {
            val assetFiles = context.assets.list("") ?: emptyArray()
            
            val jsonFiles = assetFiles.filter { it.endsWith(".json") }
            val pngFiles = assetFiles.filter { it.endsWith(".png") }
            
            Log.d(TAG, "发现JSON文件: $jsonFiles")
            Log.d(TAG, "发现PNG文件: $pngFiles")
            
            // 找到成对的文件
            jsonFiles.forEach { jsonFile ->
                val projectId = jsonFile.removeSuffix(".json")
                val pngFile = "$projectId.png"
                
                if (pngFiles.contains(pngFile)) {
                    projectFiles.add(projectId)
                    Log.d(TAG, "找到完整项目: $projectId")
                } else {
                    Log.w(TAG, "项目缺少PNG文件: $projectId")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "列出项目文件失败", e)
        }
        
        return projectFiles
    }

    /**
     * 静默查找Asset文件
     */
    private fun findAssetFileQuietly(context: Context, fileName: String): InputStream {
        val assetManager = context.assets

        // 首先尝试直接打开文件
        try {
            return assetManager.open(fileName)
        } catch (e: FileNotFoundException) {
            // 静默处理，继续查找
        }

        // 在分类文件夹中查找
        try {
            val rootFiles = assetManager.list("") ?: emptyArray()
            for (folder in rootFiles) {
                try {
                    val subFiles = assetManager.list(folder)
                    if (subFiles != null && subFiles.contains(fileName)) {
                        return assetManager.open("$folder/$fileName")
                    }
                } catch (e: Exception) {
                    // 静默忽略错误
                }
            }
        } catch (e: Exception) {
            // 静默忽略错误
        }

        // 如果都找不到，抛出异常
        throw FileNotFoundException("Asset file not found: $fileName")
    }

    /**
     * 验证特定项目
     */
    fun validateProject(context: Context, projectId: String): ProjectValidationResult {
        Log.d(TAG, "验证项目: $projectId")
        
        val fileCheck = checkProjectFiles(context, projectId)
        val result = ProjectValidationResult(
            projectId = projectId,
            filesExist = fileCheck.jsonExists && fileCheck.pngExists,
            fileCheckResult = fileCheck
        )
        
        if (result.filesExist) {
            // 尝试使用智能查找进行文件读取测试
            try {
                val inputStream = findAssetFileQuietly(context, "$projectId.json")
                val content = inputStream.readBytes()
                result.jsonReadable = content.isNotEmpty()
                result.jsonContentSize = content.size
            } catch (e: Exception) {
                result.jsonError = e.message
                Log.e(TAG, "JSON文件读取失败: $projectId", e)
            }

            try {
                val inputStream = findAssetFileQuietly(context, "$projectId.png")
                val content = inputStream.readBytes()
                result.pngReadable = content.isNotEmpty()
                result.pngContentSize = content.size
            } catch (e: Exception) {
                result.pngError = e.message
                Log.e(TAG, "PNG文件读取失败: $projectId", e)
            }
        }
        
        result.isValid = result.filesExist && result.jsonReadable && result.pngReadable
        
        Log.d(TAG, "项目验证结果: $result")
        return result
    }
    
    /**
     * 文件检查结果
     */
    data class FileCheckResult(
        val projectId: String,
        var jsonExists: Boolean = false,
        var jsonSize: Int = 0,
        var jsonError: String? = null,
        var pngExists: Boolean = false,
        var pngSize: Int = 0,
        var pngError: String? = null
    ) {
        val isComplete: Boolean
            get() = jsonExists && pngExists
            
        override fun toString(): String {
            return "FileCheckResult(projectId='$projectId', jsonExists=$jsonExists, pngExists=$pngExists, jsonSize=$jsonSize, pngSize=$pngSize)"
        }
    }
    
    /**
     * 项目验证结果
     */
    data class ProjectValidationResult(
        val projectId: String,
        var filesExist: Boolean = false,
        var jsonReadable: Boolean = false,
        var jsonContentSize: Int = 0,
        var jsonError: String? = null,
        var pngReadable: Boolean = false,
        var pngContentSize: Int = 0,
        var pngError: String? = null,
        var isValid: Boolean = false,
        val fileCheckResult: FileCheckResult
    ) {
        
        fun getErrorSummary(): String {
            val errors = mutableListOf<String>()
            
            if (!fileCheckResult.jsonExists) {
                errors.add("JSON文件不存在")
            } else if (!jsonReadable) {
                errors.add("JSON文件无法读取: $jsonError")
            }
            
            if (!fileCheckResult.pngExists) {
                errors.add("PNG文件不存在")
            } else if (!pngReadable) {
                errors.add("PNG文件无法读取: $pngError")
            }
            
            return if (errors.isEmpty()) {
                "项目验证通过"
            } else {
                errors.joinToString("; ")
            }
        }
        
        override fun toString(): String {
            return "ProjectValidationResult(projectId='$projectId', isValid=$isValid, filesExist=$filesExist, jsonReadable=$jsonReadable, pngReadable=$pngReadable)"
        }
    }
}
