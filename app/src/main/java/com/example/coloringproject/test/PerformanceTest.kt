package com.example.coloringproject.test

import android.content.Context
import android.util.Log
import com.example.coloringproject.utils.PerformanceMonitor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 性能测试工具类
 * 用于测试涂色页面初始化的性能改进效果
 */
object PerformanceTest {
    
    private const val TAG = "PerformanceTest"
    
    /**
     * 测试项目加载性能
     */
    suspend fun testProjectLoadPerformance(context: Context, projectId: String): TestResult = withContext(Dispatchers.IO) {
        Log.d(TAG, "开始项目加载性能测试: $projectId")
        
        val results = mutableListOf<Long>()
        val testRounds = 3
        
        repeat(testRounds) { round ->
            Log.d(TAG, "测试轮次: ${round + 1}/$testRounds")
            
            // 清理内存
            System.gc()
            Thread.sleep(100)
            
            val startTime = System.currentTimeMillis()
            
            try {
                // 模拟项目加载流程
                val loadManager = com.example.coloringproject.manager.ProjectLoadManager(context)
                val result = loadManager.loadProject(projectId, "BUILT_IN")
                
                when (result) {
                    is com.example.coloringproject.manager.ProjectLoadManager.LoadResult.Success -> {
                        val duration = System.currentTimeMillis() - startTime
                        results.add(duration)
                        Log.d(TAG, "轮次 ${round + 1} 完成: ${duration}ms")
                    }
                    else -> {
                        Log.w(TAG, "轮次 ${round + 1} 失败: $result")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "轮次 ${round + 1} 异常", e)
            }
        }
        
        TestResult(
            projectId = projectId,
            testRounds = testRounds,
            results = results,
            averageTime = if (results.isNotEmpty()) results.average() else 0.0,
            minTime = results.minOrNull() ?: 0L,
            maxTime = results.maxOrNull() ?: 0L
        )
    }
    
    /**
     * 测试ColoringView初始化性能
     */
    fun testColoringViewPerformance(): String {
        val report = StringBuilder()
        report.appendLine("=== ColoringView 性能测试 ===")
        
        // 模拟不同大小的项目数据
        val testSizes = listOf(100, 500, 1000, 2000)
        
        testSizes.forEach { regionCount ->
            report.appendLine("\n测试区域数量: $regionCount")
            
            val startTime = System.currentTimeMillis()
            
            // 模拟区域位图创建
            val pixels = IntArray(1000 * 1000) // 1000x1000 像素
            repeat(regionCount) { regionId ->
                // 模拟为每个区域设置像素
                val pixelCount = (50..200).random()
                repeat(pixelCount) {
                    val index = (0 until pixels.size).random()
                    pixels[index] = regionId
                }
            }
            
            val duration = System.currentTimeMillis() - startTime
            report.appendLine("区域位图创建耗时: ${duration}ms")
            
            // 计算内存使用
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            report.appendLine("内存使用: ${usedMemory / 1024 / 1024}MB")
        }
        
        return report.toString()
    }
    
    /**
     * 生成性能对比报告
     */
    fun generateComparisonReport(beforeResults: List<TestResult>, afterResults: List<TestResult>): String {
        val report = StringBuilder()
        report.appendLine("=== 性能优化对比报告 ===")
        
        if (beforeResults.size != afterResults.size) {
            report.appendLine("⚠️ 测试数据不匹配")
            return report.toString()
        }
        
        var totalImprovement = 0.0
        var improvedCount = 0
        
        beforeResults.zip(afterResults).forEach { (before, after) ->
            if (before.projectId == after.projectId) {
                val improvement = ((before.averageTime - after.averageTime) / before.averageTime * 100)
                
                report.appendLine("\n项目: ${before.projectId}")
                report.appendLine("优化前: ${before.averageTime.toInt()}ms (${before.minTime}-${before.maxTime}ms)")
                report.appendLine("优化后: ${after.averageTime.toInt()}ms (${after.minTime}-${after.maxTime}ms)")
                report.appendLine("改进: ${improvement.toInt()}%")
                
                if (improvement > 0) {
                    totalImprovement += improvement
                    improvedCount++
                }
            }
        }
        
        if (improvedCount > 0) {
            report.appendLine("\n=== 总结 ===")
            report.appendLine("平均改进: ${(totalImprovement / improvedCount).toInt()}%")
            report.appendLine("改进项目数: $improvedCount/${beforeResults.size}")
        }
        
        return report.toString()
    }
    
    /**
     * 测试结果数据类
     */
    data class TestResult(
        val projectId: String,
        val testRounds: Int,
        val results: List<Long>,
        val averageTime: Double,
        val minTime: Long,
        val maxTime: Long
    ) {
        override fun toString(): String {
            return "TestResult(project=$projectId, avg=${averageTime.toInt()}ms, range=$minTime-${maxTime}ms, rounds=$testRounds)"
        }
    }
}