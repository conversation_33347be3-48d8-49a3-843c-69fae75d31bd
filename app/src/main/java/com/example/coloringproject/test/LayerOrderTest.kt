package com.example.coloringproject.test

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.Log

/**
 * 图层顺序测试工具
 * 用于验证线稿、填色、马赛克层的正确绘制顺序
 */
object LayerOrderTest {
    
    private const val TAG = "LayerOrderTest"
    
    /**
     * 测试绘制层级顺序
     */
    fun testDrawingLayerOrder() {
        Log.d(TAG, "=== 绘制层级顺序测试 ===")
        
        // 模拟绘制过程
        val testBitmap = createTestBitmap(100, 100)
        val canvas = Canvas(testBitmap)
        
        // 1. 底层：线稿
        Log.d(TAG, "第1层：绘制线稿（底层）")
        drawTestOutline(canvas)
        
        // 2. 中层：填色区域
        Log.d(TAG, "第2层：绘制填色区域（中层，应遮挡线稿）")
        drawTestFilledRegions(canvas)
        
        // 3. 顶层：马赛克提醒
        Log.d(TAG, "第3层：绘制马赛克提醒（顶层，应可见）")
        drawTestHints(canvas)
        
        // 验证结果
        validateLayerOrder(testBitmap)
        
        Log.d(TAG, "=== 绘制层级顺序测试完成 ===")
    }
    
    /**
     * 创建测试用的bitmap
     */
    private fun createTestBitmap(width: Int, height: Int): Bitmap {
        return Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888).apply {
            eraseColor(Color.WHITE)
        }
    }
    
    /**
     * 绘制测试线稿
     */
    private fun drawTestOutline(canvas: Canvas) {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLACK
            strokeWidth = 2f
            style = Paint.Style.STROKE
        }
        
        // 绘制一个简单的矩形轮廓
        canvas.drawRect(10f, 10f, 50f, 50f, paint)
        Log.d(TAG, "线稿绘制完成：黑色矩形轮廓")
    }
    
    /**
     * 绘制测试填色区域
     */
    private fun drawTestFilledRegions(canvas: Canvas) {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.RED
            style = Paint.Style.FILL
        }
        
        // 绘制填充矩形，应该遮挡线稿
        canvas.drawRect(15f, 15f, 45f, 45f, paint)
        Log.d(TAG, "填色区域绘制完成：红色填充矩形（应遮挡线稿）")
    }
    
    /**
     * 绘制测试马赛克提醒
     */
    private fun drawTestHints(canvas: Canvas) {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = Color.BLUE
            alpha = 128 // 半透明
            style = Paint.Style.FILL
        }
        
        // 绘制半透明蓝色圆形，应该在最顶层
        canvas.drawCircle(30f, 30f, 8f, paint)
        Log.d(TAG, "马赛克提醒绘制完成：半透明蓝色圆形（应在最顶层）")
    }
    
    /**
     * 验证图层顺序
     */
    private fun validateLayerOrder(bitmap: Bitmap) {
        Log.d(TAG, "开始验证图层顺序...")
        
        // 检查中心点的颜色（应该是蓝色，因为马赛克在最顶层）
        val centerColor = bitmap.getPixel(30, 30)
        val centerColorName = getColorName(centerColor)
        
        // 检查填色区域的颜色（应该是红色，遮挡了线稿）
        val filledColor = bitmap.getPixel(25, 25)
        val filledColorName = getColorName(filledColor)
        
        // 检查线稿区域的颜色（应该是黑色，在未填色区域可见）
        val outlineColor = bitmap.getPixel(12, 12)
        val outlineColorName = getColorName(outlineColor)
        
        Log.d(TAG, "中心点颜色: $centerColorName (期望: 蓝色系)")
        Log.d(TAG, "填色区域颜色: $filledColorName (期望: 红色)")
        Log.d(TAG, "线稿区域颜色: $outlineColorName (期望: 黑色)")
        
        // 验证结果
        val isCenterBlueish = Color.blue(centerColor) > Color.red(centerColor)
        val isFilledRed = Color.red(filledColor) > 200
        val isOutlineBlack = Color.red(outlineColor) < 50 && Color.green(outlineColor) < 50 && Color.blue(outlineColor) < 50
        
        Log.d(TAG, "验证结果:")
        Log.d(TAG, "  马赛克在顶层: ${if (isCenterBlueish) "✓" else "✗"}")
        Log.d(TAG, "  填色遮挡线稿: ${if (isFilledRed) "✓" else "✗"}")
        Log.d(TAG, "  线稿在底层: ${if (isOutlineBlack) "✓" else "✗"}")
        
        val allCorrect = isCenterBlueish && isFilledRed && isOutlineBlack
        Log.d(TAG, "整体验证: ${if (allCorrect) "通过" else "失败"}")
    }
    
    /**
     * 获取颜色名称（用于调试）
     */
    private fun getColorName(color: Int): String {
        val r = Color.red(color)
        val g = Color.green(color)
        val b = Color.blue(color)
        val a = Color.alpha(color)
        
        return when {
            r > 200 && g < 100 && b < 100 -> "红色"
            r < 100 && g < 100 && b > 200 -> "蓝色"
            r < 50 && g < 50 && b < 50 -> "黑色"
            r > 200 && g > 200 && b > 200 -> "白色"
            else -> "混合色(R:$r,G:$g,B:$b,A:$a)"
        }
    }
    
    /**
     * 测试ColoringView的实际绘制顺序
     */
    fun testColoringViewLayerOrder(
        hasOutline: Boolean,
        hasFilledRegions: Boolean,
        hasHints: Boolean
    ) {
        Log.d(TAG, "=== ColoringView图层测试 ===")
        Log.d(TAG, "测试条件:")
        Log.d(TAG, "  线稿: ${if (hasOutline) "有" else "无"}")
        Log.d(TAG, "  填色区域: ${if (hasFilledRegions) "有" else "无"}")
        Log.d(TAG, "  马赛克提醒: ${if (hasHints) "有" else "无"}")
        
        Log.d(TAG, "期望的绘制顺序:")
        if (hasOutline) Log.d(TAG, "  1. 线稿（底层）")
        if (hasFilledRegions) Log.d(TAG, "  2. 填色区域（中层，遮挡线稿）")
        if (hasHints) Log.d(TAG, "  3. 马赛克提醒（顶层，始终可见）")
        
        Log.d(TAG, "=== ColoringView图层测试完成 ===")
    }
    
    /**
     * 验证填色是否正确遮挡线稿
     */
    fun validateFilledRegionsCoverOutline(
        outlineBitmap: Bitmap?,
        filledRegionsBitmap: Bitmap?
    ): Boolean {
        if (outlineBitmap == null || filledRegionsBitmap == null) {
            Log.w(TAG, "无法验证：缺少必要的bitmap")
            return false
        }
        
        Log.d(TAG, "验证填色区域是否正确遮挡线稿...")
        
        var coveredPixels = 0
        var totalFilledPixels = 0
        
        // 检查填色区域的像素
        for (x in 0 until minOf(outlineBitmap.width, filledRegionsBitmap.width)) {
            for (y in 0 until minOf(outlineBitmap.height, filledRegionsBitmap.height)) {
                val filledPixel = filledRegionsBitmap.getPixel(x, y)
                
                if (Color.alpha(filledPixel) > 0) {
                    totalFilledPixels++
                    
                    val outlinePixel = outlineBitmap.getPixel(x, y)
                    if (Color.alpha(outlinePixel) > 0) {
                        coveredPixels++
                    }
                }
            }
        }
        
        val coverageRatio = if (totalFilledPixels > 0) {
            coveredPixels.toFloat() / totalFilledPixels
        } else {
            0f
        }
        
        Log.d(TAG, "遮挡统计: $coveredPixels/$totalFilledPixels 像素被遮挡 (${(coverageRatio * 100).toInt()}%)")
        
        return coverageRatio > 0.1f // 至少10%的填色区域应该遮挡线稿
    }
}
