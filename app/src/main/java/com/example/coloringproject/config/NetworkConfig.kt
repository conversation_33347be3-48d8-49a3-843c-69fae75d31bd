package com.example.coloringproject.config

/**
 * 网络功能配置
 * 用于控制应用的网络功能开关
 */
object NetworkConfig {
    
    /**
     * 是否启用网络功能
     * 设置为false时，应用将只从assets中加载资源
     */
    const val NETWORK_ENABLED = true

    /**
     * 是否启用资源下载
     */
    const val DOWNLOAD_ENABLED = true

    /**
     * 是否启用远程API调用
     */
    const val API_ENABLED = true
    
    /**
     * 是否启用缓存功能
     */
    const val CACHE_ENABLED = true
    
    /**
     * 检查网络功能是否可用
     */
    fun isNetworkFeatureEnabled(): Boolean {
        return NETWORK_ENABLED
    }
    
    /**
     * 检查下载功能是否可用
     */
    fun isDownloadEnabled(): Boolean {
        return NETWORK_ENABLED && DOWNLOAD_ENABLED
    }
    
    /**
     * 检查API功能是否可用
     */
    fun isApiEnabled(): Bo<PERSON>an {
        return NETWORK_ENABLED && API_ENABLED
    }
    
    /**
     * 获取资源加载策略描述
     */
    fun getResourceLoadingStrategy(): String {
        return if (NETWORK_ENABLED) {
            "混合模式：优先本地资源，支持网络下载"
        } else {
            "离线模式：仅从assets加载资源"
        }
    }
    
    /**
     * 获取功能状态报告
     */
    fun getFeatureStatusReport(): String {
        return buildString {
            appendLine("=== 网络功能状态 ===")
            appendLine("网络功能: ${if (NETWORK_ENABLED) "启用" else "禁用"}")
            appendLine("资源下载: ${if (isDownloadEnabled()) "启用" else "禁用"}")
            appendLine("远程API: ${if (isApiEnabled()) "启用" else "禁用"}")
            appendLine("本地缓存: ${if (CACHE_ENABLED) "启用" else "禁用"}")
            appendLine("加载策略: ${getResourceLoadingStrategy()}")
            appendLine("===================")
        }
    }
}
