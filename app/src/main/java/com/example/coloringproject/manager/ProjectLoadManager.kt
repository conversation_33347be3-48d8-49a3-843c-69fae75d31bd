package com.example.coloringproject.manager

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.example.coloringproject.BuildConfig
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.network.ResourceDownloadManager
import com.example.coloringproject.utils.EnhancedAssetManager
import com.example.coloringproject.utils.HybridResourceManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 项目加载管理器
 * 负责处理各种来源的项目加载逻辑
 */
class ProjectLoadManager(private val context: Context) {
    
    private val TAG = "ProjectLoadManager"
    private val enhancedAssetManager = EnhancedAssetManager(context)
    private val hybridResourceManager = HybridResourceManager(context)
    
    sealed class LoadResult {
        data class Success(val coloringData: ColoringData, val outlineBitmap: Bitmap) : LoadResult()
        data class Error(val message: String, val exception: Throwable? = null) : LoadResult()
        data class RequiresDownload(val projectId: String, val downloadUrl: String) : LoadResult()
    }
    
    /**
     * 根据项目ID和来源加载项目 - 优化版本，支持预加载
     */
    suspend fun loadProject(projectId: String, projectSource: String?): LoadResult {
        return withContext(Dispatchers.IO) {
            try {
                // 首先检查预加载缓存
                if (projectSource == "BUILT_IN") {
                    val preloader = com.example.coloringproject.utils.ProjectPreloader.getInstance(context)
                    val preloadedResult = preloader.getPreloadedProject(projectId)
                    if (preloadedResult != null) {
                        Log.d(TAG, "使用预加载的项目: $projectId")
                        return@withContext preloadedResult
                    }
                }
                
                when (projectSource) {
                    "BUILT_IN" -> loadBuiltInProject(projectId)
                    "REMOTE_DOWNLOADED" -> loadDownloadedProject(projectId)
                    "STREAMING" -> loadStreamingProject(projectId)
                    else -> loadWithHybridManager(projectId)
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载项目失败: $projectId", e)
                LoadResult.Error("加载项目失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 加载内置项目 - 性能优化版本
     */
    private suspend fun loadBuiltInProject(projectId: String): LoadResult {
        Log.d(TAG, "开始加载内置项目: $projectId")
        val startTime = System.currentTimeMillis()
        
        // 暂时禁用Debug验证以提升性能
        val enableDebugValidation = false // 设为false以获得最佳性能
        if (BuildConfig.DEBUG && enableDebugValidation) {
            val validationStartTime = System.currentTimeMillis()
            val validationResult = com.example.coloringproject.test.SimpleProjectDebugger.validateProject(context, projectId)
            val validationTime = System.currentTimeMillis() - validationStartTime
            Log.d(TAG, "项目验证耗时: ${validationTime}ms")
            
            if (!validationResult.isValid) {
                return LoadResult.Error("项目验证失败: ${validationResult.getErrorSummary()}")
            }
        }
        
        // 并行加载JSON和PNG文件
        val jsonStartTime = System.currentTimeMillis()
        val coloringDataResult = enhancedAssetManager.loadColoringData("$projectId.json")
        val jsonTime = System.currentTimeMillis() - jsonStartTime
        Log.d(TAG, "JSON加载耗时: ${jsonTime}ms")
        
        val pngStartTime = System.currentTimeMillis()
        val outlineResult = enhancedAssetManager.loadOutlineBitmap("$projectId.png")
        val pngTime = System.currentTimeMillis() - pngStartTime
        Log.d(TAG, "PNG加载耗时: ${pngTime}ms")
        
        val totalTime = System.currentTimeMillis() - startTime
        Log.d(TAG, "内置项目加载总耗时: ${totalTime}ms")
        
        return if (coloringDataResult.isSuccess && outlineResult.isSuccess) {
            LoadResult.Success(coloringDataResult.getOrNull()!!, outlineResult.getOrNull()!!)
        } else {
            val error = coloringDataResult.exceptionOrNull() ?: outlineResult.exceptionOrNull()
            LoadResult.Error("加载内置项目失败", error)
        }
    }
    
    /**
     * 加载已下载项目
     */
    private suspend fun loadDownloadedProject(projectId: String): LoadResult {
        val downloadDir = File(context.filesDir, "downloaded_projects/$projectId")
        val jsonFile = File(downloadDir, "$projectId.json")
        val pngFile = File(downloadDir, "$projectId.png")
        
        return if (jsonFile.exists() && pngFile.exists()) {
            loadFromFiles(jsonFile, pngFile)
        } else {
            LoadResult.Error("下载的项目文件不存在")
        }
    }
    
    /**
     * 加载流式项目（支持缓存）
     */
    private suspend fun loadStreamingProject(projectId: String): LoadResult {
        val downloadDir = File(context.filesDir, "downloaded_projects/$projectId")
        val jsonFile = File(downloadDir, "$projectId.json")
        val pngFile = File(downloadDir, "$projectId.png")
        
        // 检查缓存
        if (jsonFile.exists() && pngFile.exists()) {
            Log.d(TAG, "发现缓存文件，直接加载: $projectId")
            return loadFromFiles(jsonFile, pngFile)
        }
        
        // 需要下载
        return try {
            downloadDir.mkdirs()
            val downloadManager = ResourceDownloadManager(context)
            val downloadResult = downloadManager.downloadProject(projectId)
            
            downloadResult.fold(
                onSuccess = { downloadedProject ->
                    loadFromFiles(downloadedProject.jsonFile, downloadedProject.outlineFile)
                },
                onFailure = { error ->
                    LoadResult.Error("下载项目失败: ${error.message}", error)
                }
            )
        } catch (e: Exception) {
            LoadResult.Error("下载项目时出错: ${e.message}", e)
        }
    }
    
    /**
     * 使用混合管理器加载
     */
    private suspend fun loadWithHybridManager(projectId: String): LoadResult {
        return when (val result = hybridResourceManager.loadProjectResource(projectId)) {
            is HybridResourceManager.ResourceLoadResult.Success -> {
                LoadResult.Success(result.coloringData, result.outlineBitmap)
            }
            is HybridResourceManager.ResourceLoadResult.RequiresDownload -> {
                LoadResult.RequiresDownload(projectId, result.downloadUrl)
            }
            is HybridResourceManager.ResourceLoadResult.Error -> {
                LoadResult.Error(result.message)
            }
        }
    }
    
    /**
     * 从文件加载项目数据
     */
    private suspend fun loadFromFiles(jsonFile: File, pngFile: File): LoadResult {
        val coloringDataResult = enhancedAssetManager.loadColoringData(jsonFile.absolutePath)
        val outlineBitmapResult = enhancedAssetManager.loadOutlineBitmap(pngFile.absolutePath)
        
        return if (coloringDataResult.isSuccess && outlineBitmapResult.isSuccess) {
            LoadResult.Success(coloringDataResult.getOrNull()!!, outlineBitmapResult.getOrNull()!!)
        } else {
            // 删除损坏的缓存文件
            jsonFile.delete()
            pngFile.delete()
            val error = coloringDataResult.exceptionOrNull() ?: outlineBitmapResult.exceptionOrNull()
            LoadResult.Error("加载文件失败，缓存已清理", error)
        }
    }
    
    /**
     * 获取验证过的项目列表
     */
    suspend fun getValidatedProjects(): Result<List<EnhancedAssetManager.ValidatedProject>> {
        return enhancedAssetManager.getValidatedProjects()
    }
}