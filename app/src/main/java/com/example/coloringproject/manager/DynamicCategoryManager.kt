package com.example.coloringproject.manager

import android.content.Context
import android.util.Log
import androidx.fragment.app.Fragment
import com.example.coloringproject.config.ProjectListConfig
import com.example.coloringproject.ui.TypedProjectListFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 动态分类管理器
 * 负责动态发现assets分类并创建对应的Fragment
 */
class DynamicCategoryManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "DynamicCategoryManager"
        
        @Volatile
        private var INSTANCE: DynamicCategoryManager? = null
        
        fun getInstance(context: Context): DynamicCategoryManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DynamicCategoryManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val projectDataManager = ProjectDataManager.getInstance(context)
    
    // 缓存发现的分类
    private var discoveredCategories: List<String>? = null
    private var categoryConfigs: Map<String, ProjectListConfig>? = null
    
    /**
     * 分类信息
     */
    data class CategoryInfo(
        val id: String,
        val displayName: String,
        val config: ProjectListConfig,
        val fragment: TypedProjectListFragment
    )
    
    /**
     * 动态发现并创建所有分类
     */
    suspend fun discoverAndCreateCategories(): Result<List<CategoryInfo>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始动态发现分类...")
            
            // 获取所有可用分类
            val categoriesResult = projectDataManager.getAvailableCategories()
            if (categoriesResult.isFailure) {
                return@withContext Result.failure(categoriesResult.exceptionOrNull() ?: Exception("获取分类失败"))
            }
            
            val categories = categoriesResult.getOrNull() ?: emptyList()
            discoveredCategories = categories
            
            Log.d(TAG, "发现分类: ${categories.joinToString(", ")}")
            
            // 创建配置
            val configs = ProjectListConfig.createConfigsForCategories(categories)
            categoryConfigs = configs
            
            // 创建分类信息列表
            val categoryInfos = categories.map { categoryName ->
                val config = configs[categoryName]!!
                val fragment = TypedProjectListFragment.newInstanceForCategory(categoryName)
                
                CategoryInfo(
                    id = categoryName,
                    displayName = config.title,
                    config = config,
                    fragment = fragment
                )
            }
            
            Log.d(TAG, "成功创建 ${categoryInfos.size} 个分类")
            Result.success(categoryInfos)
            
        } catch (e: Exception) {
            Log.e(TAG, "动态发现分类失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取特定分类的Fragment
     */
    fun getFragmentForCategory(categoryName: String): TypedProjectListFragment {
        return TypedProjectListFragment.newInstanceForCategory(categoryName)
    }
    
    /**
     * 获取特定分类的配置
     */
    fun getConfigForCategory(categoryName: String): ProjectListConfig {
        return categoryConfigs?.get(categoryName) 
            ?: ProjectListConfig.createDynamicCategoryConfig(categoryName)
    }
    
    /**
     * 获取所有已发现的分类
     */
    fun getDiscoveredCategories(): List<String> {
        return discoveredCategories ?: emptyList()
    }
    
    /**
     * 刷新分类（重新扫描assets）
     */
    suspend fun refreshCategories(): Result<List<CategoryInfo>> {
        // 清除缓存
        discoveredCategories = null
        categoryConfigs = null
        
        // 重新发现
        return discoverAndCreateCategories()
    }
    
    /**
     * 检查分类是否存在
     */
    fun isCategoryAvailable(categoryName: String): Boolean {
        return discoveredCategories?.contains(categoryName) ?: false
    }
    
    /**
     * 获取分类统计信息
     */
    fun getCategoryStats(): String {
        val categories = discoveredCategories ?: emptyList()
        return "已发现 ${categories.size} 个分类: ${categories.joinToString(", ")}"
    }
}
