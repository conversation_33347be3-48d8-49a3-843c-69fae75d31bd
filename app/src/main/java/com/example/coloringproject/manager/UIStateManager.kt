package com.example.coloringproject.manager

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.coloringproject.R
import com.example.coloringproject.adapter.ImmersiveColorAdapter
import com.example.coloringproject.data.ColorPalette
import com.example.coloringproject.databinding.ActivitySimpleMainBinding
import com.example.coloringproject.model.ColorInfo
import com.example.coloringproject.utils.EnhancedAssetManager

/**
 * UI状态管理器
 * 负责管理Activity的UI状态、显示逻辑等
 */
class UIStateManager(
    private val activity: AppCompatActivity,
    private val binding: ActivitySimpleMainBinding
) {
    
    private val TAG = "UIStateManager"
    
    // UI组件
    private lateinit var immersiveColorAdapter: ImmersiveColorAdapter
    
    // 状态变量
    private var hintCount = 10000
    private var hintsEnabled = true
    
    // 回调接口
    interface UIInteractionListener {
        fun onColorSelected(color: ColorPalette)
        fun onHintUsed()
        fun onZoomIn()
        fun onZoomOut()
        fun onZoomFit()
        fun onBackPressed()
        fun onSaveImage()
        fun onResetProject()
        fun onProjectSelectionRequested()
    }
    
    private var listener: UIInteractionListener? = null
    
    fun setUIInteractionListener(listener: UIInteractionListener) {
        this.listener = listener
    }
    
    /**
     * 初始化UI组件
     */
    fun initializeUI() {
        setupViews()
        setupColorAdapter()
        setupColoringView()
        showLoading()
    }
    
    /**
     * 设置颜色列表
     */
    fun updateColorList(colorInfoList: List<ColorInfo>, preserveProgress: Boolean = false) {
        immersiveColorAdapter.updateColors(colorInfoList, preserveProgress)
    }
    
    /**
     * 选择颜色（更新UI显示）
     */
    fun selectColor(color: ColorPalette, colorIndex: Int) {
        Log.d(TAG, "UI选择颜色: ${color.name} (index: $colorIndex)")
        
        // 更新适配器选中状态
        val colorInfo = ColorInfo(
            id = colorIndex,
            name = color.name,
            hexColor = color.colorHex
        )
        immersiveColorAdapter.setSelectedColor(colorInfo)
        
        // 设置当前颜色到ColoringView，这会触发马赛克显示
        binding.coloringView.setCurrentColor(color.colorHex)
        
        // 更新当前颜色显示
        updateCurrentColorDisplay(color)
        
        // 确保马赛克提醒开启并设置透明度
        binding.coloringView.setShowHints(true)
        binding.coloringView.setHintAlpha(0.4f)
        
        Log.d(TAG, "颜色选择UI更新完成: ${color.name}")
    }
    
    /**
     * 更新当前颜色显示
     */
    fun updateCurrentColorDisplay(color: ColorPalette, progress: Pair<Int, Int>? = null) {
        // 更新颜色圆圈
        val drawable = GradientDrawable().apply {
            shape = GradientDrawable.OVAL
            setColor(Color.parseColor(color.colorHex))
            setStroke(4, Color.GRAY)
        }
        binding.currentColorView.background = drawable
        
        // 更新颜色名称和进度
        val progressText = if (progress != null) {
            "${progress.first}/${progress.second}"
        } else {
            ""
        }
        binding.tvCurrentColorName.text = if (progressText.isNotEmpty()) {
            "${color.name} ($progressText)"
        } else {
            color.name
        }
    }
    
    /**
     * 更新总体进度
     */
    fun updateTotalProgress(filled: Int, total: Int) {
        binding.tvProgress.text = "$filled / $total"
        
        val progressPercentage = if (total > 0) (filled * 100 / total) else 0
        binding.progressBar.progress = progressPercentage
    }
    
    /**
     * 更新颜色进度
     */
    fun updateColorProgress(colorIndex: Int, progress: Int) {
        immersiveColorAdapter.updateColorProgress(colorIndex, progress)
    }
    
    /**
     * 更新提醒次数显示
     */
    fun updateHintCountDisplay() {
        binding.tvHintCount.text = hintCount.toString()
        binding.tvHintCount.visibility = if (hintCount > 0) View.VISIBLE else View.GONE
    }
    
    /**
     * 显示状态
     */
    fun showLoading() {
        binding.loadingIndicator.visibility = View.VISIBLE
        binding.tvStatus.visibility = View.GONE
        binding.recyclerViewColors.visibility = View.GONE
    }
    
    fun showReady() {
        binding.loadingIndicator.visibility = View.GONE
        binding.tvStatus.visibility = View.GONE
        showColoringUI()
    }
    
    fun showError(message: String) {
        binding.loadingIndicator.visibility = View.GONE
        binding.tvStatus.visibility = View.VISIBLE
        binding.tvStatus.text = message
        binding.recyclerViewColors.visibility = View.GONE
    }
    
    fun showColoringUI() {
        binding.recyclerViewColors.visibility = View.VISIBLE
    }
    
    /**
     * 显示项目选择器
     */
    fun showProjectSelector(projects: List<EnhancedAssetManager.ValidatedProject>, onProjectSelected: (EnhancedAssetManager.ValidatedProject) -> Unit) {
        com.example.coloringproject.ui.ProjectSelectionDialog.showProjectSelection(activity, projects, onProjectSelected)
    }
    
    /**
     * 显示完成对话框
     */
    fun showCompletionDialog(onSaveRequested: () -> Unit) {
        AlertDialog.Builder(activity)
            .setTitle("🎉 恭喜完成！")
            .setMessage("您已经完成了这个填色项目！\n\n是否要保存作品到相册？")
            .setPositiveButton("保存到相册") { _, _ ->
                onSaveRequested()
            }
            .setNegativeButton("稍后保存", null)
            .setCancelable(false)
            .show()
    }
    
    /**
     * 显示项目加载错误
     */
    fun showProjectLoadError(title: String, message: String, onRetry: () -> Unit) {
        AlertDialog.Builder(activity)
            .setTitle("⚠️ $title")
            .setMessage(message)
            .setPositiveButton("确定", null)
            .setNeutralButton("重试") { _, _ -> onRetry() }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 显示Toast消息
     */
    fun showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
        Toast.makeText(activity, message, duration).show()
    }
    
    /**
     * 使用提醒
     */
    fun useHint(): Boolean {
        return if (hintCount > 0) {
            hintCount--
            updateHintCountDisplay()
            binding.coloringView.showNextColorHint()
            true
        } else {
            false
        }
    }
    
    /**
     * 切换提醒状态
     */
    fun toggleHints() {
        hintsEnabled = !hintsEnabled
        binding.coloringView.setShowHints(hintsEnabled)
    }
    
    /**
     * 更新项目信息显示
     */
    fun updateProjectInfo(projectName: String, totalRegions: Int) {
        binding.tvStatus.text = "项目已加载: ${totalRegions}个区域"
        activity.supportActionBar?.title = "填色项目: $projectName"
    }
    
    // 私有方法
    
    private fun setupViews() {
        with(binding) {
            // 设置返回按钮
            btnBack.setOnClickListener {
                listener?.onBackPressed()
            }
            
            // 设置商店按钮
            btnShop.setOnClickListener {
                // TODO: 打开商店页面
            }
            
            // 设置涂色提醒按钮
            btnHint.setOnClickListener {
                if (hintCount > 0) {
                    listener?.onHintUsed()
                }
            }
            
            // 设置缩放控制
            btnZoomIn.setOnClickListener {
                listener?.onZoomIn()
            }
            
            btnZoomOut.setOnClickListener {
                listener?.onZoomOut()
            }
            
            btnZoomFit.setOnClickListener {
                listener?.onZoomFit()
            }
            
            // 设置测试功能按钮
            var isTestButtonsVisible = false
            btnHint.setOnLongClickListener {
                isTestButtonsVisible = !isTestButtonsVisible
                testButtonsGroup.visibility = if (isTestButtonsVisible) View.VISIBLE else View.GONE
                true
            }
            
            // 更新提醒次数显示
            updateHintCountDisplay()
        }
    }
    
    private fun setupColorAdapter() {
        immersiveColorAdapter = ImmersiveColorAdapter(emptyList()) { colorInfo ->
            // 转换为ColorPalette格式
            val colorPalette = ColorPalette(
                id = colorInfo.id,
                colorHex = colorInfo.hexColor,
                colorRgb = listOf(
                    (colorInfo.colorRgb shr 16) and 0xFF,
                    (colorInfo.colorRgb shr 8) and 0xFF,
                    colorInfo.colorRgb and 0xFF
                ),
                name = colorInfo.name,
                usageCount = colorInfo.usageCount
            )
            listener?.onColorSelected(colorPalette)
        }
        
        binding.recyclerViewColors.apply {
            layoutManager = LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false)
            adapter = immersiveColorAdapter
        }
    }
    
    private fun setupColoringView() {
        // ColoringView的事件处理将在Activity中设置
        // 这里只做基础配置
        hintsEnabled = true
        binding.coloringView.setShowHints(hintsEnabled)
        binding.coloringView.setHintAlpha(0.4f)
    }
}