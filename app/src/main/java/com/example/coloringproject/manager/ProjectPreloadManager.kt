package com.example.coloringproject.manager

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.util.LruCache
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.ColorPalette
import com.example.coloringproject.utils.EnhancedAssetManager
import kotlinx.coroutines.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 项目预加载管理器
 * 负责在应用启动时预加载热门项目，提升用户体验
 */
class ProjectPreloadManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ProjectPreloadManager"
        private const val CACHE_SIZE = 10 // 缓存10个项目
        private const val PRELOAD_COUNT = 5 // 预加载5个热门项目
        
        @Volatile
        private var INSTANCE: ProjectPreloadManager? = null
        
        fun getInstance(context: Context): ProjectPreloadManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ProjectPreloadManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 预加载的项目数据
     */
    data class PreloadedProjectData(
        val projectId: String,
        val coloringData: ColoringData,
        val outlineBitmap: Bitmap,
        val thumbnailBitmap: Bitmap?,
        val processedPalette: List<ColorPalette>,
        val loadTime: Long = System.currentTimeMillis()
    ) {
        fun isExpired(): Boolean {
            // 缓存30分钟后过期
            return System.currentTimeMillis() - loadTime > 30 * 60 * 1000
        }
    }
    
    // LRU缓存，自动管理内存
    private val preloadedProjects = LruCache<String, PreloadedProjectData>(CACHE_SIZE)
    
    // 预加载协程作用域
    private val preloadScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 资源管理器
    private val enhancedAssetManager = EnhancedAssetManager(context)
    
    /**
     * 启动时预加载热门项目
     */
    suspend fun preloadPopularProjects(): Boolean {
        return try {
            Log.d(TAG, "开始预加载热门项目...")

            // 获取实际存在的项目列表
            val popularProjects = getPopularProjectIds()
            Log.d(TAG, "热门项目列表: $popularProjects")

            if (popularProjects.isEmpty()) {
                Log.w(TAG, "没有找到可预加载的项目")
                return false
            }

            // 并行预加载项目
            val jobs = popularProjects.map { projectId ->
                preloadScope.async {
                    try {
                        preloadProject(projectId)
                        Log.d(TAG, "预加载完成: $projectId")
                        true
                    } catch (e: Exception) {
                        Log.e(TAG, "预加载失败: $projectId", e)
                        false
                    }
                }
            }

            // 等待所有预加载任务完成
            val results = jobs.awaitAll()
            val successCount = results.count { it }

            Log.d(TAG, "预加载完成: $successCount/${popularProjects.size}")
            successCount > 0

        } catch (e: Exception) {
            Log.e(TAG, "预加载过程异常", e)
            false
        }
    }
    
    /**
     * 预加载单个项目
     */
    private suspend fun preloadProject(projectId: String) {
        // 检查是否已缓存且未过期
        val existing = preloadedProjects.get(projectId)
        if (existing != null && !existing.isExpired()) {
            Log.d(TAG, "项目已缓存: $projectId")
            return
        }

        Log.d(TAG, "开始预加载项目: $projectId")
        val startTime = System.currentTimeMillis()

        try {
            // 并行加载多个资源
            val coloringDataDeferred = CoroutineScope(Dispatchers.IO).async { loadColoringData(projectId) }
            val outlineBitmapDeferred = CoroutineScope(Dispatchers.IO).async { loadOutlineBitmap(projectId) }
            val thumbnailDeferred = CoroutineScope(Dispatchers.IO).async { loadThumbnail(projectId) }

            // 等待所有资源加载完成
            val coloringData = coloringDataDeferred.await()
            val outlineBitmap = outlineBitmapDeferred.await()
            val thumbnail = thumbnailDeferred.await()

            // 预处理调色板
            val processedPalette = preprocessColorPalette(coloringData)

            // 创建预加载数据
            val preloadedData = PreloadedProjectData(
                projectId = projectId,
                coloringData = coloringData,
                outlineBitmap = outlineBitmap,
                thumbnailBitmap = thumbnail,
                processedPalette = processedPalette
            )

            // 存入缓存
            preloadedProjects.put(projectId, preloadedData)

            val loadTime = System.currentTimeMillis() - startTime
            Log.d(TAG, "项目预加载完成并缓存: $projectId, 耗时: ${loadTime}ms")

        } catch (e: Exception) {
            val loadTime = System.currentTimeMillis() - startTime
            Log.e(TAG, "项目预加载失败: $projectId, 耗时: ${loadTime}ms", e)
            throw e
        }
    }
    
    /**
     * 获取预加载的项目数据
     */
    fun getPreloadedProject(projectId: String): PreloadedProjectData? {
        val data = preloadedProjects.get(projectId)
        return if (data != null && !data.isExpired()) {
            Log.d(TAG, "使用预加载数据: $projectId")
            data
        } else {
            if (data?.isExpired() == true) {
                preloadedProjects.remove(projectId)
                Log.d(TAG, "预加载数据已过期，移除: $projectId")
            }
            null
        }
    }
    
    /**
     * 智能预加载：根据用户浏览行为预加载可见项目
     */
    fun smartPreload(visibleProjectIds: List<String>) {
        Log.d(TAG, "智能预加载: $visibleProjectIds")
        
        visibleProjectIds.forEach { projectId ->
            if (preloadedProjects.get(projectId) == null) {
                preloadScope.launch {
                    try {
                        preloadProject(projectId)
                    } catch (e: Exception) {
                        Log.e(TAG, "智能预加载失败: $projectId", e)
                    }
                }
            }
        }
    }
    
    /**
     * 获取热门项目ID列表
     */
    private suspend fun getPopularProjectIds(): List<String> {
        return try {
            // 从EnhancedAssetManager获取实际存在的项目列表
            val result = enhancedAssetManager.getValidatedProjects()
            if (result.isSuccess) {
                val projects = result.getOrNull()!!
                // 取前5个项目作为热门项目
                val popularProjects = projects.take(PRELOAD_COUNT).map { it.id }
                Log.d(TAG, "从assets获取的热门项目: $popularProjects")
                popularProjects
            } else {
                Log.w(TAG, "获取项目列表失败，使用默认列表")
                getDefaultProjectIds()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取热门项目列表异常", e)
            getDefaultProjectIds()
        }
    }

    /**
     * 获取默认项目列表（备用）
     */
    private fun getDefaultProjectIds(): List<String> {
        // 备用的默认项目列表
        return listOf(
            "longzhu3",
            "animal1",
            "building1"
        )
    }
    
    /**
     * 加载项目的涂色数据
     */
    private suspend fun loadColoringData(projectId: String): ColoringData {
        // 尝试多种可能的文件名格式
        val possibleJsonFiles = listOf(
            "${projectId}.json",
            "${projectId}_data.json",
            "projects/${projectId}.json"
        )

        for (jsonFile in possibleJsonFiles) {
            try {
                val result = enhancedAssetManager.loadColoringData(jsonFile)
                if (result.isSuccess) {
                    Log.d(TAG, "成功加载JSON: $jsonFile for project: $projectId")
                    return result.getOrNull()!!
                }
            } catch (e: Exception) {
                Log.d(TAG, "尝试加载JSON失败: $jsonFile")
            }
        }

        throw Exception("加载涂色数据失败，尝试了所有可能的文件名: $projectId")
    }

    /**
     * 加载项目的轮廓图片
     */
    private suspend fun loadOutlineBitmap(projectId: String): Bitmap {
        // 尝试多种可能的文件名格式
        val possibleOutlineFiles = listOf(
            "${projectId}_outline.png",
            "${projectId}.png",
            "outlines/${projectId}.png",
            "images/${projectId}_outline.png"
        )

        for (outlineFile in possibleOutlineFiles) {
            try {
                val result = enhancedAssetManager.loadOutlineBitmap(outlineFile)
                if (result.isSuccess) {
                    Log.d(TAG, "成功加载轮廓图: $outlineFile for project: $projectId")
                    return result.getOrNull()!!
                }
            } catch (e: Exception) {
                Log.d(TAG, "尝试加载轮廓图失败: $outlineFile")
            }
        }

        throw Exception("加载轮廓图片失败，尝试了所有可能的文件名: $projectId")
    }
    
    /**
     * 加载项目缩略图
     */
    private suspend fun loadThumbnail(projectId: String): Bitmap? {
        return try {
            // 尝试多个可能的缩略图路径
            val possiblePaths = listOf(
                "thumbnails/${projectId}.png",
                "thumbnails/${projectId}.jpg",
                "${projectId}_thumbnail.png"
            )
            
            for (path in possiblePaths) {
                try {
                    val inputStream = context.assets.open(path)
                    val bitmap = android.graphics.BitmapFactory.decodeStream(inputStream)
                    inputStream.close()
                    if (bitmap != null) {
                        return bitmap
                    }
                } catch (e: Exception) {
                    // 继续尝试下一个路径
                }
            }
            null
        } catch (e: Exception) {
            Log.w(TAG, "加载缩略图失败: $projectId", e)
            null
        }
    }
    
    /**
     * 预处理调色板
     */
    private fun preprocessColorPalette(coloringData: ColoringData): List<ColorPalette> {
        // TODO: 实现调色板预处理逻辑
        // 这里可以预先计算颜色的使用统计、分组等
        return coloringData.colorPalette
    }
    
    /**
     * 获取缓存状态信息
     */
    fun getCacheInfo(): String {
        val cacheSize = preloadedProjects.size()
        val projects = mutableListOf<String>()
        
        // 遍历缓存获取项目列表
        for (i in 0 until cacheSize) {
            val key = preloadedProjects.snapshot().keys.elementAtOrNull(i)
            if (key != null) {
                projects.add(key)
            }
        }
        
        return "缓存项目数: $cacheSize, 项目: $projects"
    }
    
    /**
     * 清理过期缓存
     */
    fun cleanupExpiredCache() {
        val snapshot = preloadedProjects.snapshot()
        snapshot.forEach { (key, value) ->
            if (value.isExpired()) {
                preloadedProjects.remove(key)
                Log.d(TAG, "清理过期缓存: $key")
            }
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        preloadScope.cancel()
        preloadedProjects.evictAll()
        Log.d(TAG, "预加载管理器已释放")
    }
}
