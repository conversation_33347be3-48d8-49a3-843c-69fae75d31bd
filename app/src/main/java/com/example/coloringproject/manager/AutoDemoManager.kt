package com.example.coloringproject.manager

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.ColorPalette

/**
 * 自动演示管理器
 * 负责处理自动填色演示功能
 */
class AutoDemoManager {
    
    private val TAG = "AutoDemoManager"
    
    // 自动演示相关
    private var isAutoDemo = false
    private val autoHandler = Handler(Looper.getMainLooper())
    private var autoRunnable: Runnable? = null
    private var currentRegionIndex = 0
    
    // 回调接口
    interface AutoDemoListener {
        fun onColorSelected(color: ColorPalette)
        fun onRegionFilled(regionId: Int)
        fun onDemoCompleted()
        fun onProgressUpdate(current: Int, total: Int)
    }
    
    private var listener: AutoDemoListener? = null
    
    fun setAutoDemoListener(listener: AutoDemoListener) {
        this.listener = listener
    }
    
    /**
     * 开始自动演示
     */
    fun startAutoDemo(coloringData: ColoringData) {
        if (isAutoDemo) {
            Log.d(TAG, "演示已在进行中")
            return
        }
        
        isAutoDemo = true
        currentRegionIndex = 0
        
        Log.d(TAG, "开始自动演示: ${coloringData.regions.size}个区域")
        
        // 开始演示
        scheduleNextAutoFill(coloringData)
    }
    
    /**
     * 停止自动演示
     */
    fun stopAutoDemo() {
        if (!isAutoDemo) {
            return
        }
        
        isAutoDemo = false
        autoRunnable?.let { autoHandler.removeCallbacks(it) }
        autoRunnable = null
        
        Log.d(TAG, "自动演示已停止")
    }
    
    /**
     * 是否正在演示
     */
    fun isRunning(): Boolean = isAutoDemo
    
    /**
     * 获取演示进度
     */
    fun getDemoProgress(): Pair<Int, Int> {
        return Pair(currentRegionIndex, 0) // 总数需要从外部传入
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopAutoDemo()
    }
    
    // 私有方法
    
    private fun scheduleNextAutoFill(coloringData: ColoringData) {
        if (!isAutoDemo) {
            return
        }
        
        autoRunnable = Runnable {
            performAutoFill(coloringData)
        }
        
        // 10ms后执行下一次填色（可调整速度）
        autoHandler.postDelayed(autoRunnable!!, 10)
    }
    
    private fun performAutoFill(coloringData: ColoringData) {
        if (!isAutoDemo) {
            Log.d(TAG, "演示已停止，跳过填色")
            return
        }
        
        if (currentRegionIndex >= coloringData.regions.size) {
            Log.d(TAG, "所有区域已完成，结束演示")
            completeAutoDemo(coloringData.regions.size)
            return
        }
        
        val region = coloringData.regions[currentRegionIndex]
        
        Log.d(TAG, "自动填色区域 ${region.id} (${currentRegionIndex + 1}/${coloringData.regions.size})")
        
        // 找到对应的调色板颜色
        val paletteColor = coloringData.colorPalette.find {
            it.colorHex.equals(region.colorHex, ignoreCase = true)
        }
        
        if (paletteColor != null) {
            Log.d(TAG, "找到匹配颜色: ${paletteColor.name} (${paletteColor.colorHex})")
            
            // 选择对应的颜色
            listener?.onColorSelected(paletteColor)
            
            // 模拟填色
            listener?.onRegionFilled(region.id)
            
            // 更新进度
            listener?.onProgressUpdate(currentRegionIndex + 1, coloringData.regions.size)
        } else {
            Log.w(TAG, "未找到区域 ${region.id} 的匹配颜色: ${region.colorHex}")
        }
        
        currentRegionIndex++
        
        // 安排下一次填色
        scheduleNextAutoFill(coloringData)
    }
    
    private fun completeAutoDemo(totalRegions: Int) {
        isAutoDemo = false
        
        Log.d(TAG, "自动演示完成: $totalRegions 个区域")
        
        listener?.onDemoCompleted()
    }
}