package com.example.coloringproject.manager

import android.content.Context
import android.util.Log
import com.example.coloringproject.utils.LightweightResourceValidator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope

/**
 * Library加载策略管理器
 * 实现本地项目优先显示，网络项目异步补充的策略
 */
class LibraryLoadStrategy private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "LibraryLoadStrategy"
        
        @Volatile
        private var INSTANCE: LibraryLoadStrategy? = null
        
        fun getInstance(context: Context): LibraryLoadStrategy {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LibraryLoadStrategy(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val projectDataManager = ProjectDataManager.getInstance(context)
    
    /**
     * 加载结果回调接口
     */
    interface LoadCallback {
        /**
         * 本地项目加载完成
         */
        fun onLocalProjectsLoaded(projects: List<LightweightResourceValidator.LightweightProject>)
        
        /**
         * 网络项目加载完成
         */
        fun onNetworkProjectsLoaded(projects: List<LightweightResourceValidator.LightweightProject>)
        
        /**
         * 加载出错
         */
        fun onLoadError(error: String, isNetworkError: Boolean)
        
        /**
         * 加载状态更新
         */
        fun onLoadingStateChanged(isLoading: Boolean, message: String)
    }
    
    /**
     * 使用分层加载策略加载项目
     * @param categoryId 分类ID，null表示加载所有项目
     * @param callback 加载结果回调
     * @param coroutineScope 协程作用域
     */
    fun loadProjectsWithStrategy(
        categoryId: String?,
        callback: LoadCallback,
        coroutineScope: CoroutineScope
    ) {
        Log.d(TAG, "开始分层加载策略: categoryId=$categoryId")
        
        coroutineScope.launch {
            try {
                // 第一阶段：立即加载本地项目
                callback.onLoadingStateChanged(true, "正在加载本地项目...")
                
                val localProjects = loadLocalProjects(categoryId)
                Log.d(TAG, "本地项目加载完成: ${localProjects.size} 个项目")
                
                // 立即回调本地项目，让UI快速显示
                callback.onLocalProjectsLoaded(localProjects)
                
                if (localProjects.isNotEmpty()) {
                    callback.onLoadingStateChanged(false, "本地项目已显示，正在加载网络项目...")
                } else {
                    callback.onLoadingStateChanged(true, "正在加载网络项目...")
                }
                
                // 第二阶段：异步加载网络项目
                launch {
                    try {
                        val networkProjects = loadNetworkProjects(categoryId)
                        Log.d(TAG, "网络项目加载完成: ${networkProjects.size} 个项目")
                        
                        if (networkProjects.isNotEmpty()) {
                            // 去重：移除与本地项目重复的项目
                            val localIds = localProjects.map { it.id }.toSet()
                            val uniqueNetworkProjects = networkProjects.filter { it.id !in localIds }
                            
                            if (uniqueNetworkProjects.isNotEmpty()) {
                                callback.onNetworkProjectsLoaded(uniqueNetworkProjects)
                                Log.d(TAG, "网络项目去重后: ${uniqueNetworkProjects.size} 个新项目")
                            } else {
                                Log.d(TAG, "网络项目全部重复，无需添加")
                            }
                        }
                        
                        callback.onLoadingStateChanged(false, "加载完成")
                        
                    } catch (e: Exception) {
                        Log.e(TAG, "网络项目加载失败", e)
                        callback.onLoadError("网络项目加载失败: ${e.message}", true)
                        callback.onLoadingStateChanged(false, "网络加载失败")
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "本地项目加载失败", e)
                callback.onLoadError("本地项目加载失败: ${e.message}", false)
                callback.onLoadingStateChanged(false, "加载失败")
            }
        }
    }
    
    /**
     * 加载本地项目
     */
    private suspend fun loadLocalProjects(categoryId: String?): List<LightweightResourceValidator.LightweightProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载本地项目: categoryId=$categoryId")
            
            if (categoryId != null) {
                // 加载指定分类的本地项目
                projectDataManager.loadLocalProjectsByCategory(categoryId)
            } else {
                // 加载所有本地项目
                projectDataManager.loadAllLocalProjects()
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载本地项目异常: categoryId=$categoryId", e)
            emptyList()
        }
    }
    
    /**
     * 加载网络项目
     */
    private suspend fun loadNetworkProjects(categoryId: String?): List<LightweightResourceValidator.LightweightProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始加载网络项目: categoryId=$categoryId")
            
            if (categoryId != null) {
                // 加载指定分类的网络项目
                projectDataManager.loadNetworkProjectsByCategory(categoryId)
            } else {
                // 加载所有网络项目
                projectDataManager.loadAllNetworkProjects()
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载网络项目异常: categoryId=$categoryId", e)
            emptyList()
        }
    }
    
    /**
     * 预加载策略：在后台预加载常用分类的项目
     */
    fun preloadCommonCategories(
        commonCategories: List<String>,
        coroutineScope: CoroutineScope
    ) {
        Log.d(TAG, "开始预加载常用分类: ${commonCategories.joinToString(", ")}")
        
        coroutineScope.launch {
            for (category in commonCategories) {
                try {
                    // 预加载本地项目（快速）
                    val localProjects = loadLocalProjects(category)
                    Log.d(TAG, "预加载分类 $category 本地项目: ${localProjects.size} 个")
                    
                    // 异步预加载网络项目（慢速）
                    launch {
                        try {
                            val networkProjects = loadNetworkProjects(category)
                            Log.d(TAG, "预加载分类 $category 网络项目: ${networkProjects.size} 个")
                        } catch (e: Exception) {
                            Log.w(TAG, "预加载分类 $category 网络项目失败", e)
                        }
                    }
                    
                } catch (e: Exception) {
                    Log.w(TAG, "预加载分类 $category 失败", e)
                }
            }
        }
    }
    
    /**
     * 获取加载统计信息
     */
    fun getLoadStatistics(): String {
        return "LibraryLoadStrategy统计信息"
    }
}