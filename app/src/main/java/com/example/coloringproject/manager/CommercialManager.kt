package com.example.coloringproject.manager

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.coloringproject.data.*
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 商业化管理器
 * 管理用户的商业化状态、等级、订阅和广告解锁
 */
class CommercialManager(private val context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "commercial_prefs", Context.MODE_PRIVATE
    )
    private val gson = Gson()
    
    // 状态流
    private val _userStatus = MutableStateFlow(loadUserStatus())
    val userStatus: StateFlow<UserCommercialStatus> = _userStatus.asStateFlow()
    
    private val _commercialConfig = MutableStateFlow<CommercialConfig?>(null)
    val commercialConfig: StateFlow<CommercialConfig?> = _commercialConfig.asStateFlow()
    
    companion object {
        private const val TAG = "CommercialManager"
        private const val KEY_USER_STATUS = "user_commercial_status"
        private const val KEY_COMMERCIAL_CONFIG = "commercial_config"
        
        // 默认商业化配置
        private val DEFAULT_TIERS = mapOf(
            "free" to CommercialTier(
                name = "免费体验版",
                description = "观看广告解锁，基础填色体验",
                monetization = "ad_supported",
                generator = "simple",
                maxColors = 8,
                minRegionSize = 1000,
                maxRegions = 80,
                targetTimeMinutes = 15,
                qualityFeatures = QualityFeatures(),
                userExperience = UserExperience()
            ),
            "basic" to CommercialTier(
                name = "基础订阅版",
                description = "订阅用户基础体验",
                monetization = "subscription_basic",
                generator = "boundary_aware",
                maxColors = 15,
                minRegionSize = 500,
                maxRegions = 150,
                targetTimeMinutes = 25,
                qualityFeatures = QualityFeatures(
                    edgeDetection = true,
                    boundaryAlignment = true
                ),
                userExperience = UserExperience(
                    autoProgression = true,
                    saveToGallery = true,
                    watermark = false
                )
            ),
            "premium" to CommercialTier(
                name = "高级订阅版",
                description = "高质量填色体验",
                monetization = "subscription_premium",
                generator = "adaptive",
                maxColors = 30,
                minRegionSize = 200,
                maxRegions = 400,
                targetTimeMinutes = 45,
                qualityFeatures = QualityFeatures(
                    edgeDetection = true,
                    boundaryAlignment = true,
                    adaptiveAlgorithm = true
                ),
                userExperience = UserExperience(
                    autoProgression = true,
                    saveToGallery = true,
                    watermark = false,
                    aiSuggestions = true
                )
            )
        )
    }
    
    init {
        // 初始化默认配置
        if (_commercialConfig.value == null) {
            _commercialConfig.value = CommercialConfig(
                commercialTiers = DEFAULT_TIERS,
                adUnlockTiers = mapOf(
                    "watch_ad_basic" to AdUnlockOption(
                        name = "观看广告解锁基础",
                        description = "观看30秒广告解锁基础功能",
                        unlockDurationHours = 2,
                        unlockedTier = "basic",
                        adDurationSeconds = 30
                    )
                )
            )
        }
    }
    
    /**
     * 加载用户商业化状态
     */
    private fun loadUserStatus(): UserCommercialStatus {
        val statusJson = prefs.getString(KEY_USER_STATUS, null)
        return if (statusJson != null) {
            try {
                gson.fromJson(statusJson, UserCommercialStatus::class.java)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load user status", e)
                UserCommercialStatus()
            }
        } else {
            UserCommercialStatus()
        }
    }
    
    /**
     * 保存用户商业化状态
     */
    private fun saveUserStatus(status: UserCommercialStatus) {
        try {
            val statusJson = gson.toJson(status)
            prefs.edit().putString(KEY_USER_STATUS, statusJson).apply()
            _userStatus.value = status
            Log.d(TAG, "User status saved: ${status.getEffectiveTier()}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save user status", e)
        }
    }
    
    /**
     * 获取当前有效等级
     */
    fun getCurrentTier(): String {
        return _userStatus.value.getEffectiveTier()
    }
    
    /**
     * 获取等级配置
     */
    fun getTierConfig(tier: String): CommercialTier? {
        return _commercialConfig.value?.commercialTiers?.get(tier)
    }
    
    /**
     * 获取当前等级配置
     */
    fun getCurrentTierConfig(): CommercialTier? {
        return getTierConfig(getCurrentTier())
    }
    
    /**
     * 检查是否可以访问功能
     */
    fun canAccessFeature(feature: String): Boolean {
        val currentTier = getCurrentTierConfig() ?: return false
        
        return when (feature) {
            "save_to_gallery" -> currentTier.userExperience.saveToGallery
            "ai_suggestions" -> currentTier.userExperience.aiSuggestions
            "auto_progression" -> currentTier.userExperience.autoProgression
            "style_recommendations" -> currentTier.userExperience.styleRecommendations
            "export_high_res" -> currentTier.userExperience.exportHighRes
            "custom_palettes" -> currentTier.userExperience.customPalettes
            else -> true // 默认允许访问
        }
    }
    
    /**
     * 检查是否需要显示水印
     */
    fun shouldShowWatermark(): Boolean {
        return getCurrentTierConfig()?.userExperience?.watermark ?: true
    }
    
    /**
     * 观看广告解锁
     */
    fun watchAdForUnlock(adOptionId: String): Boolean {
        val adOption = _commercialConfig.value?.adUnlockTiers?.get(adOptionId) ?: return false
        val currentStatus = _userStatus.value
        
        if (!currentStatus.canWatchAdForUnlock()) {
            Log.w(TAG, "Cannot watch ad: unlock still active")
            return false
        }
        
        val unlockExpiry = System.currentTimeMillis() + (adOption.unlockDurationHours * 60 * 60 * 1000)
        
        val newStatus = currentStatus.copy(
            adUnlockExpiry = unlockExpiry,
            adUnlockedTier = adOption.unlockedTier,
            totalAdsWatched = currentStatus.totalAdsWatched + 1
        )
        
        saveUserStatus(newStatus)
        
        Log.i(TAG, "Ad watched: unlocked ${adOption.unlockedTier} for ${adOption.unlockDurationHours} hours")
        return true
    }
    
    /**
     * 购买订阅
     */
    fun purchaseSubscription(subscriptionType: SubscriptionType, durationDays: Int): Boolean {
        val currentStatus = _userStatus.value
        val subscriptionExpiry = System.currentTimeMillis() + (durationDays * 24 * 60 * 60 * 1000L)
        
        val newStatus = currentStatus.copy(
            subscriptionType = subscriptionType,
            subscriptionExpiry = subscriptionExpiry
        )
        
        saveUserStatus(newStatus)
        
        Log.i(TAG, "Subscription purchased: $subscriptionType for $durationDays days")
        return true
    }
    
    /**
     * 购买高级内容
     */
    fun purchasePremiumContent(contentId: String): Boolean {
        val currentStatus = _userStatus.value
        val newPurchases = currentStatus.premiumPurchases + contentId
        
        val newStatus = currentStatus.copy(
            premiumPurchases = newPurchases
        )
        
        saveUserStatus(newStatus)
        
        Log.i(TAG, "Premium content purchased: $contentId")
        return true
    }
    
    /**
     * 检查是否拥有高级内容
     */
    fun hasPremiumContent(contentId: String): Boolean {
        return _userStatus.value.premiumPurchases.contains(contentId)
    }
    
    /**
     * 获取可用的广告解锁选项
     */
    fun getAvailableAdUnlocks(): List<Pair<String, AdUnlockOption>> {
        val config = _commercialConfig.value ?: return emptyList()
        val currentStatus = _userStatus.value
        
        return if (currentStatus.canWatchAdForUnlock()) {
            config.adUnlockTiers.toList()
        } else {
            emptyList()
        }
    }
    
    /**
     * 获取用户状态摘要
     */
    fun getUserStatusSummary(): String {
        val status = _userStatus.value
        val effectiveTier = status.getEffectiveTier()
        val tierConfig = getTierConfig(effectiveTier)
        
        return buildString {
            append("当前等级: ${tierConfig?.name ?: effectiveTier}\n")
            
            if (status.hasActiveSubscription()) {
                append("订阅剩余: ${status.getRemainingSubscriptionHours()}小时\n")
            }
            
            if (status.getRemainingAdUnlockHours() > 0) {
                append("广告解锁剩余: ${status.getRemainingAdUnlockHours()}小时\n")
            }
            
            append("观看广告次数: ${status.totalAdsWatched}\n")
            append("高级内容: ${status.premiumPurchases.size}项")
        }
    }
    
    /**
     * 重置用户状态（用于测试）
     */
    fun resetUserStatus() {
        saveUserStatus(UserCommercialStatus())
        Log.i(TAG, "User status reset to default")
    }
    
    /**
     * 模拟升级到指定等级（用于测试）
     */
    fun simulateUpgrade(tier: String, durationHours: Int = 24) {
        when (tier) {
            "basic", "premium", "professional" -> {
                val subscriptionType = when (tier) {
                    "basic" -> SubscriptionType.BASIC
                    "premium" -> SubscriptionType.PREMIUM
                    "professional" -> SubscriptionType.PROFESSIONAL
                    else -> SubscriptionType.NONE
                }
                purchaseSubscription(subscriptionType, durationHours / 24)
            }
            else -> {
                // 通过广告解锁
                val currentStatus = _userStatus.value
                val unlockExpiry = System.currentTimeMillis() + (durationHours * 60 * 60 * 1000L)
                
                val newStatus = currentStatus.copy(
                    adUnlockExpiry = unlockExpiry,
                    adUnlockedTier = tier
                )
                
                saveUserStatus(newStatus)
            }
        }
        
        Log.i(TAG, "Simulated upgrade to $tier for $durationHours hours")
    }
}
