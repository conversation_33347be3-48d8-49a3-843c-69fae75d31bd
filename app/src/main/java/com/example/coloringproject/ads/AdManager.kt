package com.example.coloringproject.ads

import android.app.Activity
import android.content.Context
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.LiveData

/**
 * 广告管理器
 * 统一管理应用内的广告展示和奖励
 */
class AdManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "AdManager"
        
        @Volatile
        private var INSTANCE: AdManager? = null
        
        fun getInstance(context: Context): AdManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AdManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    // 广告状态
    private val _isAdLoaded = MutableLiveData<Boolean>()
    val isAdLoaded: LiveData<Boolean> = _isAdLoaded
    
    private val _isAdShowing = MutableLiveData<Boolean>()
    val isAdShowing: LiveData<Boolean> = _isAdShowing
    
    // 广告配置
    private var isInitialized = false
    private var testMode = true // 开发阶段使用测试广告
    
    // 广告单元ID（测试ID，正式发布时需要替换）
    private val rewardedAdUnitId = "ca-app-pub-3940256099942544/5224354917" // 测试ID
    private val interstitialAdUnitId = "ca-app-pub-3940256099942544/1033173712" // 测试ID
    private val bannerAdUnitId = "ca-app-pub-3940256099942544/6300978111" // 测试ID
    
    /**
     * 初始化广告SDK
     */
    fun initialize() {
        if (isInitialized) return
        
        try {
            // TODO: 集成AdMob SDK
            // MobileAds.initialize(context) { initializationStatus ->
            //     Log.d(TAG, "AdMob initialized: ${initializationStatus.adapterStatusMap}")
            //     isInitialized = true
            //     loadRewardedAd()
            // }
            
            // 模拟初始化成功
            isInitialized = true
            _isAdLoaded.value = true
            Log.d(TAG, "Ad SDK initialized (mock)")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize ad SDK", e)
        }
    }
    
    /**
     * 显示奖励视频广告
     */
    fun showRewardedAd(
        activity: Activity,
        onAdRewarded: (rewardType: String, rewardAmount: Int) -> Unit,
        onAdFailed: (error: String) -> Unit
    ) {
        if (!isInitialized) {
            onAdFailed("广告SDK未初始化")
            return
        }
        
        if (_isAdShowing.value == true) {
            onAdFailed("广告正在显示中")
            return
        }
        
        try {
            _isAdShowing.value = true
            
            // TODO: 显示真实的奖励视频广告
            // rewardedAd?.show(activity) { rewardItem ->
            //     onAdRewarded(rewardItem.type, rewardItem.amount)
            // }
            
            // 模拟广告播放
            simulateAdPlayback(onAdRewarded, onAdFailed)
            
        } catch (e: Exception) {
            _isAdShowing.value = false
            onAdFailed("广告显示失败: ${e.message}")
            Log.e(TAG, "Failed to show rewarded ad", e)
        }
    }
    
    /**
     * 显示插屏广告
     */
    fun showInterstitialAd(
        activity: Activity,
        onAdClosed: () -> Unit,
        onAdFailed: (error: String) -> Unit
    ) {
        if (!isInitialized) {
            onAdFailed("广告SDK未初始化")
            return
        }
        
        try {
            _isAdShowing.value = true
            
            // TODO: 显示真实的插屏广告
            // interstitialAd?.show(activity)
            
            // 模拟插屏广告
            simulateInterstitialAd(onAdClosed, onAdFailed)
            
        } catch (e: Exception) {
            _isAdShowing.value = false
            onAdFailed("插屏广告显示失败: ${e.message}")
            Log.e(TAG, "Failed to show interstitial ad", e)
        }
    }
    
    /**
     * 检查奖励视频广告是否可用
     */
    fun isRewardedAdAvailable(): Boolean {
        return isInitialized && _isAdLoaded.value == true && _isAdShowing.value != true
    }
    
    /**
     * 预加载广告
     */
    fun preloadAds() {
        if (!isInitialized) return
        
        try {
            // TODO: 预加载奖励视频和插屏广告
            // loadRewardedAd()
            // loadInterstitialAd()
            
            Log.d(TAG, "Ads preloaded (mock)")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to preload ads", e)
        }
    }
    
    /**
     * 设置测试模式
     */
    fun setTestMode(enabled: Boolean) {
        testMode = enabled
        Log.d(TAG, "Test mode: $enabled")
    }
    
    /**
     * 获取广告统计信息
     */
    fun getAdStats(): AdStats {
        // TODO: 实现真实的广告统计
        return AdStats(
            totalAdsShown = 0,
            totalRewardsEarned = 0,
            lastAdShownTime = 0L,
            adLoadSuccessRate = 100.0f
        )
    }
    
    // 模拟方法（开发阶段使用）
    private fun simulateAdPlayback(
        onAdRewarded: (rewardType: String, rewardAmount: Int) -> Unit,
        onAdFailed: (error: String) -> Unit
    ) {
        // 模拟3秒广告播放
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            _isAdShowing.value = false
            
            // 90%概率成功，10%概率失败
            if (Math.random() < 0.9) {
                onAdRewarded("premium_unlock", 1)
                Log.d(TAG, "Simulated rewarded ad completed successfully")
            } else {
                onAdFailed("用户取消了广告播放")
                Log.d(TAG, "Simulated rewarded ad failed")
            }
            
            // 重新加载广告
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                _isAdLoaded.value = true
            }, 1000)
            
        }, 3000)
    }
    
    private fun simulateInterstitialAd(
        onAdClosed: () -> Unit,
        onAdFailed: (error: String) -> Unit
    ) {
        // 模拟2秒插屏广告
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            _isAdShowing.value = false
            onAdClosed()
            Log.d(TAG, "Simulated interstitial ad closed")
        }, 2000)
    }
}

/**
 * 广告统计信息
 */
data class AdStats(
    val totalAdsShown: Int,
    val totalRewardsEarned: Int,
    val lastAdShownTime: Long,
    val adLoadSuccessRate: Float
)

/**
 * 广告类型
 */
enum class AdType {
    REWARDED,       // 奖励视频
    INTERSTITIAL,   // 插屏
    BANNER          // 横幅
}

/**
 * 广告事件
 */
sealed class AdEvent {
    object AdLoaded : AdEvent()
    object AdShown : AdEvent()
    object AdClosed : AdEvent()
    data class AdRewarded(val type: String, val amount: Int) : AdEvent()
    data class AdFailed(val error: String) : AdEvent()
}
