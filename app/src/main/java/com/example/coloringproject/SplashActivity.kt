package com.example.coloringproject

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.coloringproject.databinding.ActivitySplashBinding
import com.example.coloringproject.manager.ProjectPreloadManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Splash启动页面
 * 负责应用初始化和项目预加载
 */
class SplashActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "SplashActivity"
        private const val MIN_SPLASH_TIME = 2000L // 最少显示2秒
        private const val ANIMATION_DURATION = 800L // 动画持续时间
    }
    
    private lateinit var binding: ActivitySplashBinding
    private lateinit var preloadManager: ProjectPreloadManager
    private var startTime = 0L
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 隐藏状态栏，全屏显示
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        startTime = System.currentTimeMillis()
        preloadManager = ProjectPreloadManager.getInstance(this)
        
        // 启动初始化流程
        startInitialization()
    }
    
    /**
     * 启动初始化流程
     */
    private fun startInitialization() {
        lifecycleScope.launch {
            try {
                // 启动入场动画
                startEntranceAnimation()
                
                // 等待动画开始
                delay(500)
                
                // 开始预加载流程
                startPreloadingProcess()
                
            } catch (e: Exception) {
                Log.e(TAG, "初始化过程异常", e)
                // 即使出错也要跳转，不能卡在启动页
                navigateToMain()
            }
        }
    }
    
    /**
     * 启动入场动画
     */
    private fun startEntranceAnimation() {
        // Logo淡入动画
        val logoFadeIn = ObjectAnimator.ofFloat(binding.logoImageView, "alpha", 0f, 1f).apply {
            duration = ANIMATION_DURATION
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        // 应用名称淡入动画
        val appNameFadeIn = ObjectAnimator.ofFloat(binding.appNameTextView, "alpha", 0f, 1f).apply {
            duration = ANIMATION_DURATION
            startDelay = 200
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        // 副标题淡入动画
        val subtitleFadeIn = ObjectAnimator.ofFloat(binding.subtitleTextView, "alpha", 0f, 1f).apply {
            duration = ANIMATION_DURATION
            startDelay = 400
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        // 版本信息淡入动画
        val versionFadeIn = ObjectAnimator.ofFloat(binding.versionTextView, "alpha", 0f, 1f).apply {
            duration = ANIMATION_DURATION
            startDelay = 600
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        // 装饰元素动画
        val brushFadeIn = ObjectAnimator.ofFloat(binding.brushImageView, "alpha", 0f, 0.6f).apply {
            duration = ANIMATION_DURATION
            startDelay = 800
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        val paletteFadeIn = ObjectAnimator.ofFloat(binding.paletteImageView, "alpha", 0f, 0.6f).apply {
            duration = ANIMATION_DURATION
            startDelay = 1000
            interpolator = AccelerateDecelerateInterpolator()
        }
        
        // 组合动画
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(
            logoFadeIn, appNameFadeIn, subtitleFadeIn, 
            versionFadeIn, brushFadeIn, paletteFadeIn
        )
        animatorSet.start()
        
        Log.d(TAG, "入场动画已启动")
    }
    
    /**
     * 开始预加载流程
     */
    private suspend fun startPreloadingProcess() {
        // 显示加载区域
        showLoadingArea()
        
        // 阶段1：系统初始化
        updateProgress(10, "正在初始化系统...")
        delay(300)
        
        // 阶段2：准备资源管理器
        updateProgress(30, "正在准备资源管理器...")
        delay(300)
        
        // 阶段3：预加载热门项目
        updateProgress(50, "正在加载热门项目...")
        val preloadSuccess = preloadManager.preloadPopularProjects()
        
        if (preloadSuccess) {
            updateProgress(80, "正在优化缓存...")
            delay(300)
            
            updateProgress(100, "准备完成!")
            Log.d(TAG, "预加载成功完成")
        } else {
            updateProgress(100, "准备完成!")
            Log.w(TAG, "预加载部分失败，但继续启动")
        }
        
        // 确保最少显示时间
        val elapsedTime = System.currentTimeMillis() - startTime
        if (elapsedTime < MIN_SPLASH_TIME) {
            delay(MIN_SPLASH_TIME - elapsedTime)
        }
        
        // 延迟一下让用户看到完成状态
        delay(500)
        
        // 跳转到主页面
        navigateToMain()
    }
    
    /**
     * 显示加载区域
     */
    private fun showLoadingArea() {
        val fadeIn = ObjectAnimator.ofFloat(binding.loadingContainer, "alpha", 0f, 1f).apply {
            duration = 500
            interpolator = AccelerateDecelerateInterpolator()
        }
        fadeIn.start()
    }
    
    /**
     * 更新进度
     */
    private fun updateProgress(progress: Int, message: String) {
        binding.progressBar.progress = progress
        binding.loadingTextView.text = message
        Log.d(TAG, "进度更新: $progress% - $message")
    }
    
    /**
     * 跳转到主页面
     */
    private fun navigateToMain() {
        Log.d(TAG, "跳转到主页面")
        
        val intent = Intent(this, EnhancedMainActivity::class.java)
        startActivity(intent)
        finish()
        
        // 添加淡入淡出过渡动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }
    
    /**
     * 处理返回键 - 在启动页面禁用返回键
     */
    override fun onBackPressed() {
        // 在启动页面不允许返回
        // 可以添加双击退出逻辑，但通常启动页不需要
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "SplashActivity销毁")
    }
}
