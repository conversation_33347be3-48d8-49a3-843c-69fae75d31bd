package com.example.coloringproject.adapter

import android.graphics.BitmapFactory
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.coloringproject.R
import com.example.coloringproject.utils.ProjectSaveManager
import com.example.coloringproject.utils.ProjectProgress
import com.google.android.material.card.MaterialCardView

/**
 * 我的图库适配器
 */
class MyGalleryAdapter(
    private val onProjectClick: (ProjectProgress, android.widget.ImageView) -> Unit,
    private val onProjectLongClick: (ProjectProgress) -> Unit,
    private val onDeleteClick: (ProjectProgress) -> Unit,
    private val onShareClick: (ProjectProgress) -> Unit
) : RecyclerView.Adapter<MyGalleryAdapter.GalleryViewHolder>() {

    private var projects: List<ProjectProgress> = emptyList()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GalleryViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_my_gallery, parent, false)
        return GalleryViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: GalleryViewHolder, position: Int) {
        holder.bind(projects[position])
    }

    override fun onBindViewHolder(holder: GalleryViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty() && payloads.contains("thumbnail")) {
            // 只刷新缩略图
            holder.refreshThumbnail(projects[position])
        } else {
            // 完整刷新
            super.onBindViewHolder(holder, position, payloads)
        }
    }
    
    override fun getItemCount(): Int = projects.size
    
    /**
     * 更新项目列表
     */
    fun updateProjects(newProjects: List<ProjectProgress>) {
        projects = newProjects
        notifyDataSetChanged()
    }

    /**
     * 刷新特定项目
     */
    fun refreshProject(projectId: String) {
        val position = projects.indexOfFirst { project ->
            project.projectName == projectId ||
            project.projectName.replace("-", "_") == projectId ||
            projectId.replace("-", "_") == project.projectName
        }

        if (position != -1) {
            notifyItemChanged(position)
        }
    }

    /**
     * 刷新特定项目的缩略图
     */
    fun refreshProjectThumbnail(projectId: String) {
        val position = projects.indexOfFirst { project ->
            project.projectName == projectId ||
            project.projectName.replace("-", "_") == projectId ||
            projectId.replace("-", "_") == project.projectName
        }

        if (position != -1) {
            notifyItemChanged(position, "thumbnail")
        }
    }

    inner class GalleryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: MaterialCardView = itemView.findViewById(R.id.cardGalleryProject)
        private val imageView: ImageView = itemView.findViewById(R.id.ivGalleryProjectImage)
        private val titleText: TextView = itemView.findViewById(R.id.tvGalleryProjectTitle)
        private val progressBar: ProgressBar = itemView.findViewById(R.id.progressGalleryProject)
        private val progressText: TextView = itemView.findViewById(R.id.tvGalleryProjectProgress)
        private val dateText: TextView = itemView.findViewById(R.id.tvGalleryProjectDate)
        private val statusIcon: ImageView = itemView.findViewById(R.id.ivGalleryProjectStatus)
        private val btnShare: ImageButton = itemView.findViewById(R.id.btnShareGalleryProject)
        private val btnDelete: ImageButton = itemView.findViewById(R.id.btnDeleteGalleryProject)
        private val completedBadge: View = itemView.findViewById(R.id.viewCompletedBadge)
        
        fun bind(project: ProjectProgress) {
            titleText.text = project.projectName
            progressText.text = "${project.progressPercentage}%"
            progressBar.progress = project.progressPercentage
            dateText.text = project.getFormattedDate()
            
            // 设置完成状态
            if (project.isCompleted) {
                statusIcon.setImageResource(R.drawable.ic_check_circle)
                statusIcon.setColorFilter(itemView.context.getColor(R.color.completed_color))
                completedBadge.visibility = View.VISIBLE
                cardView.strokeColor = itemView.context.getColor(R.color.completed_color)
                cardView.strokeWidth = 2
            } else {
                statusIcon.setImageResource(R.drawable.ic_play_circle)
                statusIcon.setColorFilter(itemView.context.getColor(R.color.progress_color))
                completedBadge.visibility = View.GONE
                cardView.strokeColor = itemView.context.getColor(R.color.divider_color)
                cardView.strokeWidth = 1
            }
            
            // 加载项目预览图片
            project.previewImagePath?.let { imagePath ->
                try {
                    val bitmap = BitmapFactory.decodeFile(imagePath)
                    if (bitmap != null) {
                        imageView.setImageBitmap(bitmap)
                    } else {
                        imageView.setImageResource(R.drawable.ic_image_placeholder)
                    }
                } catch (e: Exception) {
                    imageView.setImageResource(R.drawable.ic_image_placeholder)
                }
            } ?: run {
                imageView.setImageResource(R.drawable.ic_image_placeholder)
            }
            
            // 设置共享元素过渡名称
            imageView.transitionName = "project_image_${project.projectName}"

            // 设置点击事件
            cardView.setOnClickListener {
                onProjectClick(project, imageView)
            }
            
            cardView.setOnLongClickListener {
                onProjectLongClick(project)
                true
            }
            
            btnShare.setOnClickListener {
                onShareClick(project)
            }
            
            btnDelete.setOnClickListener {
                onDeleteClick(project)
            }
            
            // 设置进度条颜色
            setProgressBarColor(project.progressPercentage)
        }
        
        private fun setProgressBarColor(progress: Int) {
            val colorRes = when {
                progress == 100 -> R.color.completed_color
                progress >= 50 -> R.color.progress_color
                else -> R.color.text_hint
            }

            progressBar.progressTintList =
                itemView.context.getColorStateList(colorRes)
        }

        /**
         * 只刷新缩略图，不更新其他信息
         */
        fun refreshThumbnail(project: ProjectProgress) {
            // 重新加载项目预览图片
            project.previewImagePath?.let { imagePath ->
                try {
                    val bitmap = BitmapFactory.decodeFile(imagePath)
                    if (bitmap != null) {
                        imageView.setImageBitmap(bitmap)
                    } else {
                        imageView.setImageResource(R.drawable.ic_image_placeholder)
                    }
                } catch (e: Exception) {
                    imageView.setImageResource(R.drawable.ic_image_placeholder)
                }
            } ?: run {
                imageView.setImageResource(R.drawable.ic_image_placeholder)
            }
        }
    }
}
