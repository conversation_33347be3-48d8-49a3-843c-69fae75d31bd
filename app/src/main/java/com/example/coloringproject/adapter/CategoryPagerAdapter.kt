package com.example.coloringproject.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.example.coloringproject.ui.CategoryProjectFragment
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.manager.CategoryMappingManager

/**
 * 分类页面适配器
 * 用于ViewPager2显示不同分类的Fragment
 */
class CategoryPagerAdapter(
    private val parentFragment: Fragment,
    private val onProjectSelected: (HybridResourceManager.HybridProject, android.widget.ImageView) -> Unit
) : FragmentStateAdapter(parentFragment) {

    private var categories = listOf<String>()
    private val fragmentCache = mutableMapOf<Int, CategoryProjectFragment>()

    override fun getItemCount(): Int = categories.size

    override fun createFragment(position: Int): Fragment {
        val category = categories[position]
        val fragment = CategoryProjectFragment.newInstance(category)

        // 设置项目选择回调
        fragment.onProjectSelected = onProjectSelected

        // 缓存Fragment实例
        fragmentCache[position] = fragment

        return fragment
    }

    /**
     * 获取标签标题
     */
    fun getTabTitle(position: Int): String {
        return if (position < categories.size) {
            formatCategoryDisplayName(categories[position])
        } else {
            "未知"
        }
    }

    /**
     * 更新分类数据
     */
    fun updateCategories(newCategories: List<String>) {
        categories = newCategories
        fragmentCache.clear() // 清除缓存，强制重新创建Fragment
        notifyDataSetChanged()
    }

    /**
     * 获取分类数量
     */
    fun getCategoryCount(): Int = categories.size

    /**
     * 获取指定位置的分类名称
     */
    fun getCategoryName(position: Int): String? {
        return if (position < categories.size) {
            categories[position]
        } else {
            null
        }
    }

    /**
     * 获取指定位置的Fragment
     * 使用FragmentManager查找实际的Fragment实例
     */
    fun getFragmentAt(position: Int): CategoryProjectFragment? {
        // 首先尝试从缓存获取
        val cachedFragment = fragmentCache[position]
        if (cachedFragment != null) {
            return cachedFragment
        }

        // 如果缓存中没有，尝试从FragmentManager中查找
        val fragmentManager = parentFragment.childFragmentManager
        val fragmentTag = "f$position" // ViewPager2使用的Fragment标签格式
        val fragment = fragmentManager.findFragmentByTag(fragmentTag) as? CategoryProjectFragment

        // 如果找到了，更新缓存
        if (fragment != null) {
            fragmentCache[position] = fragment
        }

        return fragment
    }

    private fun formatCategoryDisplayName(categoryName: String): String {
        return CategoryMappingManager.getCategoryDisplayName(categoryName)
    }
}
