package com.example.coloringproject.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.coloringproject.R
import com.example.coloringproject.utils.EnhancedAssetManager

/**
 * 项目图库适配器
 */
class ProjectGalleryAdapter(
    private val onProjectClick: (EnhancedAssetManager.ValidatedProject) -> Unit
) : RecyclerView.Adapter<ProjectGalleryAdapter.ProjectViewHolder>() {
    
    private var projects = listOf<EnhancedAssetManager.ValidatedProject>()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProjectViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_project_gallery, parent, false)
        return ProjectViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ProjectViewHolder, position: Int) {
        holder.bind(projects[position])
    }
    
    override fun getItemCount(): Int = projects.size
    
    /**
     * 更新项目列表
     */
    fun updateProjects(newProjects: List<EnhancedAssetManager.ValidatedProject>) {
        projects = newProjects
        notifyDataSetChanged()
    }
    
    inner class ProjectViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: CardView = itemView.findViewById(R.id.cardProject)
        private val imageView: ImageView = itemView.findViewById(R.id.ivProjectPreview)
        private val tvName: TextView = itemView.findViewById(R.id.tvProjectName)
        private val tvDifficulty: TextView = itemView.findViewById(R.id.tvDifficulty)
        private val tvRegions: TextView = itemView.findViewById(R.id.tvRegions)
        private val tvColors: TextView = itemView.findViewById(R.id.tvColors)
        private val tvSize: TextView = itemView.findViewById(R.id.tvSize)
        
        fun bind(project: EnhancedAssetManager.ValidatedProject) {
            // 设置项目名称
            tvName.text = project.name
            
            // 设置难度
            tvDifficulty.text = when (project.difficulty) {
                "easy" -> "简单"
                "medium" -> "中等"
                "hard" -> "困难"
                else -> project.difficulty
            }
            
            // 设置难度颜色
            val difficultyColor = when (project.difficulty) {
                "easy" -> Color.parseColor("#4CAF50")    // 绿色
                "medium" -> Color.parseColor("#FF9800")  // 橙色
                "hard" -> Color.parseColor("#F44336")    // 红色
                else -> Color.parseColor("#757575")      // 灰色
            }
            tvDifficulty.setTextColor(difficultyColor)
            
            // 设置区域数量
            tvRegions.text = "${project.totalRegions}区域"
            
            // 设置颜色数量
            tvColors.text = "${project.totalColors}颜色"
            
            // 设置文件大小
            tvSize.text = "${project.fileSize.totalSizeKB}KB"
            
            // 加载预览图片
            try {
                val context = itemView.context
                val inputStream = context.assets.open(project.outlineFile)
                Glide.with(context)
                    .load(inputStream)
                    .placeholder(R.drawable.ic_image_placeholder)
                    .error(R.drawable.ic_image_error)
                    .into(imageView)
            } catch (e: Exception) {
                imageView.setImageResource(R.drawable.ic_image_error)
            }
            
            // 设置点击事件
            cardView.setOnClickListener {
                onProjectClick(project)
            }
            
            // 设置卡片动画效果
            cardView.setOnTouchListener { _, event ->
                when (event.action) {
                    android.view.MotionEvent.ACTION_DOWN -> {
                        cardView.animate()
                            .scaleX(0.95f)
                            .scaleY(0.95f)
                            .setDuration(100)
                            .start()
                    }
                    android.view.MotionEvent.ACTION_UP,
                    android.view.MotionEvent.ACTION_CANCEL -> {
                        cardView.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(100)
                            .start()
                    }
                }
                false
            }
        }
    }
}
