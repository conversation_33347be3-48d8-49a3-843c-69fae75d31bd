package com.example.coloringproject.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.coloringproject.R
import com.example.coloringproject.utils.EnhancedAssetManager
import com.google.android.material.card.MaterialCardView

/**
 * 增强的项目适配器 - 用于显示项目列表
 */
class EnhancedProjectAdapter(
    private val onProjectClick: (EnhancedAssetManager.ValidatedProject) -> Unit,
    private val onProjectLongClick: (EnhancedAssetManager.ValidatedProject) -> Unit
) : RecyclerView.Adapter<EnhancedProjectAdapter.ProjectViewHolder>() {
    
    private var projects: List<EnhancedAssetManager.ValidatedProject> = emptyList()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProjectViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_enhanced_project, parent, false)
        return ProjectViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ProjectViewHolder, position: Int) {
        holder.bind(projects[position])
    }
    
    override fun getItemCount(): Int = projects.size
    
    /**
     * 更新项目列表
     */
    fun updateProjects(newProjects: List<EnhancedAssetManager.ValidatedProject>) {
        projects = newProjects
        notifyDataSetChanged()
    }
    
    inner class ProjectViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: MaterialCardView = itemView.findViewById(R.id.cardProject)
        private val imageView: ImageView = itemView.findViewById(R.id.ivProjectImage)
        private val titleText: TextView = itemView.findViewById(R.id.tvProjectTitle)
        private val difficultyText: TextView = itemView.findViewById(R.id.tvProjectDifficulty)
        private val regionsText: TextView = itemView.findViewById(R.id.tvProjectRegions)
        private val timeText: TextView = itemView.findViewById(R.id.tvProjectTime)
        
        fun bind(project: EnhancedAssetManager.ValidatedProject) {
            titleText.text = project.name
            difficultyText.text = getDifficultyText(project.difficulty)
            regionsText.text = "${project.totalRegions} 区域"
            timeText.text = "${project.estimatedTime} 分钟"

            // 设置难度标签颜色
            setDifficultyTextColor(project.difficulty)
            
            // 使用Glide加载项目预览图片
            try {
                val context = itemView.context
                val inputStream = context.assets.open(project.outlineFile)
                com.bumptech.glide.Glide.with(context)
                    .load(inputStream)
                    .placeholder(R.drawable.ic_image_placeholder)
                    .error(R.drawable.ic_image_error)
                    .centerCrop()
                    .into(imageView)
            } catch (e: Exception) {
                imageView.setImageResource(R.drawable.ic_image_error)
            }
            
            // 设置点击事件
            cardView.setOnClickListener {
                onProjectClick(project)
            }
            
            cardView.setOnLongClickListener {
                onProjectLongClick(project)
                true
            }
            
            // 添加卡片动画效果
            cardView.setOnTouchListener { _, event ->
                when (event.action) {
                    android.view.MotionEvent.ACTION_DOWN -> {
                        cardView.animate()
                            .scaleX(0.95f)
                            .scaleY(0.95f)
                            .setDuration(100)
                            .start()
                    }
                    android.view.MotionEvent.ACTION_UP,
                    android.view.MotionEvent.ACTION_CANCEL -> {
                        cardView.animate()
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(100)
                            .start()
                    }
                }
                false
            }
        }
        
        private fun getDifficultyText(difficulty: String): String {
            return when (difficulty) {
                "easy" -> "简单"
                "medium" -> "中等"
                "hard" -> "困难"
                else -> "未知"
            }
        }
        
        private fun setDifficultyTextColor(difficulty: String) {
            val colorRes = when (difficulty) {
                "easy" -> R.color.completed_color
                "medium" -> R.color.progress_color
                "hard" -> R.color.error_color
                else -> R.color.text_hint
            }

            difficultyText.setTextColor(
                itemView.context.getColor(colorRes)
            )
        }
    }
}
