package com.example.coloringproject.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.coloringproject.R
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.LoadResult
import com.example.coloringproject.utils.OptimizedResourceLoader
import com.example.coloringproject.utils.ProjectSaveManager
import com.google.android.material.card.MaterialCardView
import com.google.android.material.chip.Chip
import kotlinx.coroutines.launch
import java.io.File

/**
 * 混合项目适配器
 * 支持显示本地和远程项目，以及下载功能
 */
class HybridProjectAdapter(
    private val onProjectClick: (HybridResourceManager.HybridProject) -> Unit,
    private val onDownloadClick: (HybridResourceManager.HybridProject) -> Unit,
    private val onDeleteClick: (HybridResourceManager.HybridProject) -> Unit
) : ListAdapter<HybridResourceManager.HybridProject, HybridProjectAdapter.ProjectViewHolder>(ProjectDiffCallback()) {

    // 下载进度映射
    private val downloadProgress = mutableMapOf<String, Float>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProjectViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_hybrid_project, parent, false)
        return ProjectViewHolder(view)
    }

    override fun onBindViewHolder(holder: ProjectViewHolder, position: Int) {
        val project = getItem(position)
        holder.bind(project)
    }

    override fun onBindViewHolder(holder: ProjectViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty()) {
            val project = getItem(position)
            // 处理特定的更新类型
            for (payload in payloads) {
                when (payload) {
                    "thumbnail" -> {
                        // 只刷新缩略图
                        android.util.Log.d("HybridProjectAdapter", "刷新缩略图 payload: ${project.id}")
                        holder.refreshThumbnail(project)
                    }
                    "progress" -> {
                        // 只刷新进度
                        android.util.Log.d("HybridProjectAdapter", "刷新进度 payload: ${project.id}")
                        holder.refreshProgress(project)
                    }
                }
            }
        } else {
            // 没有payload，执行完整绑定
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    /**
     * 更新下载进度
     */
    fun updateDownloadProgress(projectId: String, progress: Float) {
        downloadProgress[projectId] = progress
        // 找到对应的项目并更新
        val position = currentList.indexOfFirst { it.id == projectId }
        if (position != -1) {
            notifyItemChanged(position)
        }
    }

    /**
     * 移除下载进度
     */
    fun removeDownloadProgress(projectId: String) {
        downloadProgress.remove(projectId)
        val position = currentList.indexOfFirst { it.id == projectId }
        if (position != -1) {
            notifyItemChanged(position)
        }
    }

    inner class ProjectViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: MaterialCardView = itemView.findViewById(R.id.cardProject)
        private val imagePreview: ImageView = itemView.findViewById(R.id.imagePreview)
        private val textTitle: TextView = itemView.findViewById(R.id.textTitle)
        private val textDescription: TextView = itemView.findViewById(R.id.textDescription)
        private val chipDifficulty: Chip = itemView.findViewById(R.id.chipDifficulty)
        private val chipSource: Chip = itemView.findViewById(R.id.chipSource)
        private val textStats: TextView = itemView.findViewById(R.id.textStats)
        private val textFileSize: TextView = itemView.findViewById(R.id.textFileSize)
        private val buttonAction: ImageButton = itemView.findViewById(R.id.buttonAction)
        private val progressDownload: ProgressBar = itemView.findViewById(R.id.progressDownload)
        private val textProgress: TextView = itemView.findViewById(R.id.textProgress)

        fun bind(project: HybridResourceManager.HybridProject) {
            // 基本信息
            textTitle.text = project.displayName
            textDescription.text = project.description
            
            // 难度标签
            chipDifficulty.text = project.difficulty
            chipDifficulty.setChipBackgroundColorResource(getDifficultyColor(project.difficulty))
            
            // 来源标签
            chipSource.text = getSourceText(project.resourceSource)
            chipSource.setChipBackgroundColorResource(getSourceColor(project.resourceSource))
            
            // 统计信息（包含进度信息）
//            updateStatsWithProgress(project)
            
            // 文件大小
            textFileSize.text = formatFileSize(project.fileSize)
            
            // 预览图片
            loadPreviewImage(project)
            
            // 下载进度
            val currentProgress = downloadProgress[project.id] ?: 0f
            updateProgressUI(project, currentProgress)
            
            // 操作按钮
            setupActionButton(project)
            
            // 点击事件 - 优化版本
            cardView.setOnClickListener {
                if (project.isDownloaded || project.isBuiltIn) {
                    // 直接启动项目，不预加载数据
                    onProjectClick(project)
                } else {
                    // 未下载的项目，显示下载选项
                    onDownloadClick(project)
                }
            }
        }

        private fun getDifficultyColor(difficulty: String): Int {
            return when (difficulty.lowercase()) {
                "easy", "简单" -> R.color.difficulty_easy
                "medium", "中等" -> R.color.difficulty_medium
                "hard", "困难" -> R.color.difficulty_hard
                else -> R.color.difficulty_unknown
            }
        }

        private fun getSourceText(source: HybridResourceManager.Companion.ResourceSource): String {
            return when (source) {
                HybridResourceManager.Companion.ResourceSource.BUILT_IN -> "内置"
                HybridResourceManager.Companion.ResourceSource.DOWNLOADED -> "已下载"
                HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED -> "远程下载"
                HybridResourceManager.Companion.ResourceSource.STREAMING -> "云端"
            }
        }

        private fun getSourceColor(source: HybridResourceManager.Companion.ResourceSource): Int {
            return when (source) {
                HybridResourceManager.Companion.ResourceSource.BUILT_IN -> R.color.source_builtin
                HybridResourceManager.Companion.ResourceSource.DOWNLOADED -> R.color.source_downloaded
                HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED -> R.color.source_downloaded
                HybridResourceManager.Companion.ResourceSource.STREAMING -> R.color.source_remote
            }
        }

        private fun formatFileSize(bytes: Long): String {
            return when {
                bytes < 1024 -> "${bytes}B"
                bytes < 1024 * 1024 -> "${bytes / 1024}KB"
                else -> String.format("%.1fMB", bytes / (1024f * 1024f))
            }
        }

        /**
         * 只刷新缩略图（用于payload更新）
         */
        fun refreshThumbnail(project: HybridResourceManager.HybridProject) {
            android.util.Log.d("HybridProjectAdapter", "refreshThumbnail: ${project.id}")
            loadPreviewImageForceRefresh(project)
        }

        /**
         * 强制刷新缩略图，跳过Glide缓存
         */
        private fun loadPreviewImageForceRefresh(project: HybridResourceManager.HybridProject) {
            try {
                val projectSaveManager = ProjectSaveManager(itemView.context)
                val projectName = project.id
                val savedPreviewPath = projectSaveManager.getPreviewImagePath(projectName)

                if (savedPreviewPath != null && java.io.File(savedPreviewPath).exists()) {
                    // 强制刷新保存的进度图片
                    val previewFile = java.io.File(savedPreviewPath)
                    val lastModified = previewFile.lastModified()

                    android.util.Log.d("HybridProjectAdapter", "强制刷新进度缩略图: $projectName")

                    com.bumptech.glide.Glide.with(itemView.context)
                        .load(previewFile)
                        .placeholder(R.drawable.ic_image_placeholder)
                        .error(R.drawable.ic_image_error)
                        .centerCrop()
                        .skipMemoryCache(true) // 强制跳过内存缓存
                        .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.NONE) // 强制跳过磁盘缓存
                        .signature(com.bumptech.glide.signature.ObjectKey("refresh_${System.currentTimeMillis()}")) // 使用时间戳强制刷新
                        .into(imagePreview)
                } else {
                    // 如果没有进度图片，加载原图
                    android.util.Log.d("HybridProjectAdapter", "没有进度图片，加载原图: $projectName")
                    loadPreviewImage(project)
                }
            } catch (e: Exception) {
                android.util.Log.e("HybridProjectAdapter", "强制刷新缩略图失败: ${project.id}", e)
                // 降级到普通加载
                loadPreviewImage(project)
            }
        }

        /**
         * 只刷新进度（用于payload更新）
         */
        fun refreshProgress(project: HybridResourceManager.HybridProject) {
            android.util.Log.d("HybridProjectAdapter", "refreshProgress: ${project.id}")
            updateProgressUI(project, project.downloadProgress)
        }

        private fun loadPreviewImage(project: HybridResourceManager.HybridProject) {
            // 1. 首先检查是否有保存的进度图片
            val projectSaveManager = ProjectSaveManager(itemView.context)
            val projectName = project.id // 使用项目ID作为保存名称
            val savedPreviewPath = projectSaveManager.getPreviewImagePath(projectName)

            if (savedPreviewPath != null && File(savedPreviewPath).exists()) {
                // 显示保存的进度图片（包含填色+线稿）
                val previewFile = File(savedPreviewPath)
                val lastModified = previewFile.lastModified()

                com.bumptech.glide.Glide.with(itemView.context)
                    .load(previewFile)
                    .placeholder(R.drawable.ic_image_placeholder)
                    .error(R.drawable.ic_image_error)
                    .centerCrop()
                    .skipMemoryCache(true) // 跳过内存缓存
                    .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.NONE) // 跳过磁盘缓存
                    .signature(com.bumptech.glide.signature.ObjectKey(lastModified)) // 使用文件修改时间作为签名
                    .into(imagePreview)

                android.util.Log.d("HybridProjectAdapter", "显示进度缩略图: $projectName (修改时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(lastModified))})")
                return
            }
              fun updateStatsWithProgress(project: HybridResourceManager.HybridProject) {
                try {
                    // 检查是否有保存的进度
                    val projectSaveManager = ProjectSaveManager(itemView.context)
                    val progressResult = projectSaveManager.loadProgress(project.id)

                    if (progressResult is LoadResult.Success) {
                        val progressInfo = progressResult.data
                        if (progressInfo.progressPercentage > 0) {
                            // 有进度信息，显示进度
                            val baseStats = "${project.totalRegions}区域 · ${project.totalColors}颜色"
//                        textStats.text = "$baseStats · ${progressInfo.progressPercentage}%完成"

                            android.util.Log.d("HybridProjectAdapter", "项目 ${project.id} 进度: ${progressInfo.progressPercentage}%")
                        } else {
                            // 没有进度信息，显示原始统计
//                        textStats.text = "${project.totalRegions}区域 · ${project.totalColors}颜色 · ${project.estimatedTime}分钟"
                        }
                    } else {
                        // 没有进度信息，显示原始统计
//                    textStats.text = "${project.totalRegions}区域 · ${project.totalColors}颜色 · ${project.estimatedTime}分钟"
                    }
                } catch (e: Exception) {
                    android.util.Log.w("HybridProjectAdapter", "获取项目进度失败: ${project.id}", e)
                    // 出错时显示原始统计信息
//                textStats.text = "${project.totalRegions}区域 · ${project.totalColors}颜色 · ${project.estimatedTime}分钟"
                }
            }


            // 2. 如果没有进度图片，使用原有的加载逻辑
            val optimizedLoader = OptimizedResourceLoader(itemView.context)

            // 设置加载中状态
            imagePreview.setImageResource(R.drawable.ic_image_placeholder)

            // 异步加载缩略图
            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                optimizedLoader.loadThumbnailFirst(
                    project = project,
                    onThumbnailReady = { result ->
                        // 使用Glide加载缩略图
                        com.bumptech.glide.Glide.with(itemView.context)
                            .load(result.thumbnailPath)
                            .placeholder(R.drawable.ic_image_placeholder)
                            .error(R.drawable.ic_image_error)
                            .centerCrop()
                            .into(imagePreview)
                    },
                    onError = { error ->
                        android.util.Log.e("HybridProjectAdapter", "Failed to load thumbnail: ${error.message}")
                        imagePreview.setImageResource(R.drawable.ic_image_error)
                    }
                )
            }
        }

        private fun updateProgressUI(project: HybridResourceManager.HybridProject, progress: Float) {
            val isDownloading = downloadProgress.containsKey(project.id)
            
            if (isDownloading) {
                progressDownload.visibility = View.VISIBLE
                textProgress.visibility = View.VISIBLE
                progressDownload.progress = (progress * 100).toInt()
                textProgress.text = "${(progress * 100).toInt()}%"
            } else {
                progressDownload.visibility = View.GONE
                textProgress.visibility = View.GONE
            }
        }

        private fun setupActionButton(project: HybridResourceManager.HybridProject) {
            val isDownloading = downloadProgress.containsKey(project.id)
            
            when {
                isDownloading -> {
                    // 正在下载
                    buttonAction.setImageResource(R.drawable.ic_cancel)
                    buttonAction.setOnClickListener {
                        // TODO: 实现取消下载
                    }
                }
                project.isBuiltIn -> {
                    // 内置项目
                    buttonAction.setImageResource(R.drawable.ic_play_arrow)
                    buttonAction.setOnClickListener {
                        onProjectClick(project)
                    }
                }
                project.isDownloaded -> {
                    // 已下载项目
                    buttonAction.setImageResource(R.drawable.ic_delete)
                    buttonAction.setOnClickListener {
                        onDeleteClick(project)
                    }
                }
                else -> {
                    // 远程项目
                    buttonAction.setImageResource(R.drawable.ic_download)
                    buttonAction.setOnClickListener {
                        onDownloadClick(project)
                    }
                }
            }
        }
    }


    class ProjectDiffCallback : DiffUtil.ItemCallback<HybridResourceManager.HybridProject>() {
        override fun areItemsTheSame(
            oldItem: HybridResourceManager.HybridProject,
            newItem: HybridResourceManager.HybridProject
        ): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: HybridResourceManager.HybridProject,
            newItem: HybridResourceManager.HybridProject
        ): Boolean {
            return oldItem == newItem
        }

        /**
         * 更新统计信息，包含进度信息
         */
    }

    // ========== 刷新方法 ==========

    /**
     * 刷新特定项目的显示
     * 使用统一的项目匹配逻辑
     */
    fun refreshProject(projectId: String) {
        val position = currentList.indexOfFirst { project ->
            val unifiedId = com.example.coloringproject.utils.ProjectNameUtils.getUnifiedProjectId(project)
            com.example.coloringproject.utils.ProjectNameUtils.isProjectMatch(unifiedId, projectId)
        }
        if (position != -1) {
            android.util.Log.d("HybridProjectAdapter", "刷新项目显示: $projectId, 位置: $position")
            notifyItemChanged(position)
        } else {
            android.util.Log.w("HybridProjectAdapter", "未找到匹配的项目进行刷新: $projectId")
        }
    }

    /**
     * 刷新特定项目的缩略图
     */
    fun refreshProjectThumbnail(projectId: String) {
        val position = currentList.indexOfFirst { project ->
            val unifiedId = com.example.coloringproject.utils.ProjectNameUtils.getUnifiedProjectId(project)
            com.example.coloringproject.utils.ProjectNameUtils.isProjectMatch(unifiedId, projectId)
        }
        if (position != -1) {
            android.util.Log.d("HybridProjectAdapter", "刷新项目缩略图: $projectId, 位置: $position")
            // 使用payload来指示只刷新缩略图
            notifyItemChanged(position, "thumbnail")
        } else {
            android.util.Log.w("HybridProjectAdapter", "未找到匹配的项目进行缩略图刷新: $projectId")
        }
    }

    /**
     * 刷新所有项目
     */
    fun refreshAllProjects() {
        android.util.Log.d("HybridProjectAdapter", "刷新所有项目显示")
        notifyDataSetChanged()
    }
}
