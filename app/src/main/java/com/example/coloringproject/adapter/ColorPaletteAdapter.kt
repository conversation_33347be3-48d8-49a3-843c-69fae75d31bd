package com.example.coloringproject.adapter

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.example.coloringproject.R
import com.example.coloringproject.data.ColorPalette

/**
 * 调色板适配器
 */
class ColorPaletteAdapter(
    private var colors: List<ColorPalette> = emptyList(),
    private val onColorSelected: (ColorPalette) -> Unit
) : RecyclerView.Adapter<ColorPaletteAdapter.ColorViewHolder>() {

    private var selectedPosition = -1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ColorViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_color_palette, parent, false)
        return ColorViewHolder(view)
    }

    override fun onBindViewHolder(holder: ColorViewHolder, position: Int) {
        holder.bind(colors[position], position == selectedPosition)
    }

    override fun getItemCount(): Int = colors.size

    /**
     * 更新颜色列表
     */
    fun updateColors(newColors: List<ColorPalette>) {
        colors = newColors
        selectedPosition = -1
        notifyDataSetChanged()
    }

    /**
     * 设置选中的颜色
     */
    fun setSelectedColor(colorHex: String) {
        val newPosition = colors.indexOfFirst { it.colorHex.equals(colorHex, ignoreCase = true) }
        if (newPosition != selectedPosition) {
            val oldPosition = selectedPosition
            selectedPosition = newPosition
            
            if (oldPosition != -1) {
                notifyItemChanged(oldPosition)
            }
            if (newPosition != -1) {
                notifyItemChanged(newPosition)
            }
        }
    }

    /**
     * 获取当前选中的颜色
     */
    fun getSelectedColor(): ColorPalette? {
        return if (selectedPosition != -1 && selectedPosition < colors.size) {
            colors[selectedPosition]
        } else null
    }

    inner class ColorViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val colorView: View = itemView.findViewById(R.id.colorView)
        private val colorName: TextView = itemView.findViewById(R.id.colorName)
        private val usageCount: TextView = itemView.findViewById(R.id.usageCount)

        fun bind(colorPalette: ColorPalette, isSelected: Boolean) {
            // 设置颜色
            val drawable = GradientDrawable().apply {
                shape = GradientDrawable.OVAL
                setColor(Color.parseColor(colorPalette.colorHex))

                // 设置边框
                if (isSelected) {
                    setStroke(8, ContextCompat.getColor(itemView.context, R.color.selected_color_border))
                } else {
                    setStroke(4, ContextCompat.getColor(itemView.context, R.color.color_border))
                }
            }
            colorView.background = drawable

            // 设置文本
            colorName.text = colorPalette.name

            // 显示进度信息而不是使用次数
            if (colorPalette.totalCount > 0) {
                usageCount.text = colorPalette.progressText

                // 根据完成状态设置文本颜色
                usageCount.setTextColor(
                    if (colorPalette.isCompleted) {
                        ContextCompat.getColor(itemView.context, R.color.completed_color)
                    } else {
                        ContextCompat.getColor(itemView.context, R.color.progress_color)
                    }
                )
            } else {
                usageCount.text = "${colorPalette.usageCount}"
                usageCount.setTextColor(ContextCompat.getColor(itemView.context, R.color.default_text_color))
            }

            // 设置选中状态的透明度和完成状态的透明度
            itemView.alpha = when {
                colorPalette.isCompleted -> 0.5f  // 已完成的颜色半透明
                isSelected -> 1.0f               // 选中的颜色完全不透明
                else -> 0.7f                     // 其他颜色稍微透明
            }

            // 点击事件
            itemView.setOnClickListener {
                val oldPosition = selectedPosition
                selectedPosition = adapterPosition

                // 更新UI
                if (oldPosition != -1) {
                    notifyItemChanged(oldPosition)
                }
                notifyItemChanged(selectedPosition)

                // 回调
                onColorSelected(colorPalette)
            }
        }
    }
}
