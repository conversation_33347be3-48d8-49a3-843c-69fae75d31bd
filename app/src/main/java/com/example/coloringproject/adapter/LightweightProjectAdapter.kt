package com.example.coloringproject.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.coloringproject.R
import com.example.coloringproject.utils.LightweightResourceValidator
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.LoadResult
import com.example.coloringproject.utils.ProjectNameUtils
import com.example.coloringproject.utils.ProjectSaveManager
import com.google.android.material.chip.Chip
import android.util.Log
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 轻量级项目适配器
 * 专门用于Library页面的快速显示，只加载缩略图，不加载完整数据
 */
class LightweightProjectAdapter(
    private val onProjectClick: (LightweightResourceValidator.LightweightProject, android.widget.ImageView) -> Unit
) : RecyclerView.Adapter<LightweightProjectAdapter.ProjectViewHolder>() {

    companion object {
        private const val TAG = "LightweightProjectAdapter"
    }

    private var projects = listOf<LightweightResourceValidator.LightweightProject>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProjectViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_hybrid_project, parent, false)
        return ProjectViewHolder(view)
    }

    override fun onBindViewHolder(holder: ProjectViewHolder, position: Int) {
        holder.bind(projects[position])
    }

    override fun onBindViewHolder(holder: ProjectViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty()) {
            val project = projects[position]
            // 处理特定的更新类型
            for (payload in payloads) {
                when (payload) {
                    "thumbnail" -> {
                        // 只刷新缩略图
                        Log.d(TAG, "刷新缩略图 payload: ${project.id}")
                        holder.refreshThumbnail(project)
                    }
                    "progress" -> {
                        // 只刷新进度
                        Log.d(TAG, "刷新进度 payload: ${project.id}")
                        holder.refreshProgress(project)
                    }
                }
            }
        } else {
            // 没有payload，执行完整绑定
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    override fun getItemCount(): Int = projects.size

    /**
     * 更新项目列表 - 使用DiffUtil避免不必要的刷新
     */
    fun updateProjects(newProjects: List<LightweightResourceValidator.LightweightProject>) {
        val oldProjects = projects
        projects = newProjects

        // 如果项目列表没有变化，不进行刷新
        if (oldProjects.size == newProjects.size &&
            oldProjects.zip(newProjects).all { (old, new) ->
                old.id == new.id && old.displayName == new.displayName
            }) {
            Log.d(TAG, "项目列表无变化，跳过刷新")
            return
        }

        Log.d(TAG, "项目列表有变化，执行智能刷新: ${oldProjects.size} -> ${newProjects.size}")

        // 使用DiffUtil进行智能更新
        try {
            val diffCallback = ProjectDiffCallback(oldProjects, newProjects)
            val diffResult = androidx.recyclerview.widget.DiffUtil.calculateDiff(diffCallback)
            diffResult.dispatchUpdatesTo(this)
        } catch (e: Exception) {
            Log.w(TAG, "DiffUtil更新失败，降级到全量刷新", e)
            notifyDataSetChanged()
        }
    }

    /**
     * 在顶部插入新项目（用于网络项目异步加载完成后插入）
     */
    fun insertProjectsAtTop(newProjects: List<LightweightResourceValidator.LightweightProject>) {
        if (newProjects.isEmpty()) {
            Log.d(TAG, "没有新项目需要插入")
            return
        }

        Log.d(TAG, "在顶部插入 ${newProjects.size} 个新项目")

        // 去重：确保不会重复添加相同ID的项目
        val existingIds = projects.map { it.id }.toSet()
        val uniqueNewProjects = newProjects.filter { it.id !in existingIds }

        if (uniqueNewProjects.isEmpty()) {
            Log.d(TAG, "所有新项目都已存在，跳过插入")
            return
        }

        // 将新项目插入到列表顶部
        val updatedProjects = uniqueNewProjects + projects
        projects = updatedProjects

        // 通知适配器有新项目插入到顶部
        notifyItemRangeInserted(0, uniqueNewProjects.size)

        Log.d(TAG, "成功在顶部插入 ${uniqueNewProjects.size} 个新项目，总项目数: ${projects.size}")
    }

    /**
     * 按分类筛选项目
     */
    fun filterByCategory(category: String, allProjects: List<LightweightResourceValidator.LightweightProject>) {
        projects = if (category == "all") {
            allProjects
        } else {
            allProjects.filter { it.category == category }
        }
        notifyDataSetChanged()
    }

    inner class ProjectViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: CardView = itemView.findViewById(R.id.cardProject)
        private val imagePreview: ImageView = itemView.findViewById(R.id.imagePreview)
        private val textTitle: TextView = itemView.findViewById(R.id.textTitle)
        private val textDescription: TextView = itemView.findViewById(R.id.textDescription)
        private val chipDifficulty: Chip = itemView.findViewById(R.id.chipDifficulty)
        private val chipSource: Chip = itemView.findViewById(R.id.chipSource)
        private val textStats: TextView = itemView.findViewById(R.id.textStats)
        private val textFileSize: TextView = itemView.findViewById(R.id.textFileSize)

        fun bind(project: LightweightResourceValidator.LightweightProject) {
            // 基本信息
            textTitle.text = project.displayName
            textDescription.text = project.description ?: "轻量级项目预览"

            // 难度标签
            chipDifficulty.text = getDifficultyText(project.difficulty)
            chipDifficulty.setChipBackgroundColorResource(getDifficultyColor(project.difficulty))

            // 来源标签
            chipSource.text = getSourceText(project.resourceSource)
            chipSource.setChipBackgroundColorResource(getSourceColor(project.resourceSource))

            // 统计信息（包含进度信息）
            updateStatsWithProgress(project)

            // 文件大小
            textFileSize.text = formatFileSize(project.estimatedFileSize)

            // 加载缩略图（优化版本）
            loadThumbnailOptimized(project)

            // 设置项目状态
            updateProjectStatus(project)

            // 设置共享元素过渡名称
            imagePreview.transitionName = "project_image_${project.id}"

            // 点击事件
            cardView.setOnClickListener {
                Log.d(TAG, "=== 项目点击事件 ===")
                Log.d(TAG, "项目ID: ${project.id}")
                Log.d(TAG, "项目名称: ${project.displayName}")
                Log.d(TAG, "项目来源: ${project.resourceSource}")
                Log.d(TAG, "项目有效性: ${project.isValid}")
                Log.d(TAG, "验证错误: ${project.validationErrors}")
                Log.d(TAG, "==================")

                if (project.isValid) {
                    Log.i(TAG, "启动有效项目: ${project.id}")
                    onProjectClick(project, imagePreview)
                } else {
                    Log.w(TAG, "项目无效，无法启动: ${project.id}")
                    Log.w(TAG, "验证错误详情: ${project.validationErrors.joinToString(", ")}")

                    // 显示错误提示给用户
                    val context = itemView.context
                    android.widget.Toast.makeText(
                        context,
                        "项目无效: ${project.id}\n${project.validationErrors.firstOrNull() ?: "未知错误"}",
                        android.widget.Toast.LENGTH_LONG
                    ).show()
                }
            }
        }

        private fun getDifficultyText(difficulty: String): String {
            return when (difficulty.lowercase()) {
                "easy" -> "简单"
                "medium" -> "中等"
                "hard" -> "困难"
                else -> "未知"
            }
        }

        private fun getDifficultyColor(difficulty: String): Int {
            return when (difficulty.lowercase()) {
                "easy" -> R.color.difficulty_easy
                "medium" -> R.color.difficulty_medium
                "hard" -> R.color.difficulty_hard
                else -> R.color.difficulty_unknown
            }
        }

        private fun getSourceText(source: HybridResourceManager.Companion.ResourceSource): String {
            return when (source) {
                HybridResourceManager.Companion.ResourceSource.BUILT_IN -> "内置"
                HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED -> "已下载"
                HybridResourceManager.Companion.ResourceSource.DOWNLOADED -> "缓存"
                HybridResourceManager.Companion.ResourceSource.STREAMING -> "在线"
                else -> "未知"
            }
        }

        private fun getSourceColor(source: HybridResourceManager.Companion.ResourceSource): Int {
            return when (source) {
                HybridResourceManager.Companion.ResourceSource.BUILT_IN -> R.color.difficulty_easy
                HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED -> R.color.difficulty_medium
                HybridResourceManager.Companion.ResourceSource.DOWNLOADED -> R.color.difficulty_hard
                HybridResourceManager.Companion.ResourceSource.STREAMING -> R.color.difficulty_medium
                else -> R.color.difficulty_unknown
            }
        }

        private fun formatFileSize(sizeBytes: Long): String {
            return when {
                sizeBytes < 1024 -> "${sizeBytes}B"
                sizeBytes < 1024 * 1024 -> "${sizeBytes / 1024}KB"
                else -> "${sizeBytes / (1024 * 1024)}MB"
            }
        }

        /**
         * 优化的缩略图加载
         * 优先显示保存的进度图片，如果没有则显示原图
         */
        private fun loadThumbnailOptimized(project: LightweightResourceValidator.LightweightProject) {
            try {
                // 1. 首先检查是否有保存的进度图片
                val projectSaveManager = ProjectSaveManager(itemView.context)
                val projectName = getProjectNameForSave(project)
                val savedPreviewPath = projectSaveManager.getPreviewImagePath(projectName)

                if (savedPreviewPath != null && File(savedPreviewPath).exists()) {
                    // 显示保存的进度图片（包含填色+线稿）
                    val previewFile = File(savedPreviewPath)
                    val lastModified = previewFile.lastModified()

                    Glide.with(itemView.context)
                        .load(previewFile)
                        .placeholder(R.drawable.ic_image_placeholder)
                        .error(R.drawable.ic_image_error)
                        .centerCrop()
                        .skipMemoryCache(true) // 跳过内存缓存
                        .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.NONE) // 跳过磁盘缓存
                        .signature(com.bumptech.glide.signature.ObjectKey(lastModified)) // 使用文件修改时间作为签名
                        .into(imagePreview)

                    Log.d(TAG, "显示进度缩略图: $projectName (修改时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date(lastModified))})")
                    return
                }

                // 2. 如果没有保存的进度图片，显示原图但检查是否有进度数据
                checkProgressWithoutImage(project, projectSaveManager, projectName)

                when (project.resourceSource) {
                    HybridResourceManager.Companion.ResourceSource.BUILT_IN -> {
                        // 从Assets加载缩略图
                        val assetPath = "file:///android_asset/${project.outlinePath}"
                        Glide.with(itemView.context)
                            .load(assetPath)
                            .placeholder(R.drawable.ic_image_placeholder)
                            .error(R.drawable.ic_image_error)
                            .centerCrop()
                            .into(imagePreview)
                        Log.d(TAG, "加载Assets缩略图: ${project.outlinePath}")
                    }

                    HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED,
                    HybridResourceManager.Companion.ResourceSource.DOWNLOADED -> {
                        // 从文件系统加载缩略图
                        val file = java.io.File(project.outlinePath)
                        if (file.exists()) {
                            Glide.with(itemView.context)
                                .load(file)
                                .placeholder(R.drawable.ic_image_placeholder)
                                .error(R.drawable.ic_image_error)
                                .centerCrop()
                                .into(imagePreview)
                            Log.d(TAG, "加载文件缩略图: ${project.outlinePath}")
                        } else {
                            Log.w(TAG, "文件不存在: ${project.outlinePath}")
                            imagePreview.setImageResource(R.drawable.ic_image_error)
                        }
                    }

                    HybridResourceManager.Companion.ResourceSource.STREAMING -> {
                        // 从服务器加载缩略图
                        val thumbnailUrl = project.thumbnailPath ?: project.outlinePath
                        if (thumbnailUrl.isNotEmpty()) {
                            Glide.with(itemView.context)
                                .load(thumbnailUrl)
                                .placeholder(R.drawable.ic_image_placeholder)
                                .error(R.drawable.ic_image_error)
                                .centerCrop()
                                .into(imagePreview)
                            Log.d(TAG, "加载远程缩略图: $thumbnailUrl")
                        } else {
                            Log.w(TAG, "远程缩略图URL为空")
                            imagePreview.setImageResource(R.drawable.ic_image_placeholder)
                        }
                    }

                    else -> {
                        Log.w(TAG, "未知资源来源: ${project.resourceSource}")
                        imagePreview.setImageResource(R.drawable.ic_image_placeholder)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载缩略图失败: ${project.id}", e)
                imagePreview.setImageResource(R.drawable.ic_image_error)
            }
        }

        /**
         * 检查没有进度图片但可能有进度数据的情况
         */
        private fun checkProgressWithoutImage(
            project: LightweightResourceValidator.LightweightProject,
            projectSaveManager: ProjectSaveManager,
            projectName: String
        ) {
            try {
            } catch (e: Exception) {
                Log.w(TAG, "检查项目进度失败: $projectName", e)
            }
        }



        /**
         * 获取用于保存的项目名称
         * 使用统一的命名规则
         */
        private fun getProjectNameForSave(project: LightweightResourceValidator.LightweightProject): String {
            return ProjectNameUtils.getUnifiedProjectId(project)
        }

        /**
         * 更新项目状态显示
         */
        private fun updateProjectStatus(project: LightweightResourceValidator.LightweightProject) {
            if (project.isValid) {
                // 项目有效
                cardView.alpha = 1.0f
                cardView.isEnabled = true
            } else {
                // 项目无效
                cardView.alpha = 0.6f
                cardView.isEnabled = false

                // 可以在这里添加错误指示器
                Log.w(TAG, "项目无效: ${project.id}, 错误: ${project.validationErrors}")
            }
        }

        /**
         * 更新统计信息，包含进度信息
         */
        private fun updateStatsWithProgress(project: LightweightResourceValidator.LightweightProject) {
            try {
                // 检查是否有保存的进度
                val projectSaveManager = ProjectSaveManager(itemView.context)
                val projectName = getProjectNameForSave(project)
                val progressResult = projectSaveManager.loadProgress(projectName)

                if (progressResult is LoadResult.Success) {
                    val progressInfo = progressResult.data
                    if (progressInfo.progressPercentage > 0) {
                        // 有进度信息，显示进度
                        val baseStats = "${project.category} · ${project.difficulty}"
                        textStats.text = "$baseStats · ${progressInfo.progressPercentage}%完成"

                        Log.d(TAG, "项目 $projectName 进度: ${progressInfo.progressPercentage}%")
                    } else {
                        // 没有进度信息，显示原始统计
                        textStats.text = "${project.category} · ${project.difficulty}"
                    }
                } else {
                    // 没有进度信息，显示原始统计
                    textStats.text = "${project.category} · ${project.difficulty}"
                }
            } catch (e: Exception) {
                Log.w(TAG, "获取项目进度失败: ${project.id}", e)
                // 出错时显示原始统计信息
                textStats.text = "${project.category} · ${project.difficulty}"
            }
        }

        /**
         * 只刷新缩略图（用于payload更新）
         */
        fun refreshThumbnail(project: LightweightResourceValidator.LightweightProject) {
            Log.d(TAG, "refreshThumbnail: ${project.id}")
            loadThumbnailForceRefresh(project)
        }

        /**
         * 强制刷新缩略图，跳过Glide缓存
         */
        private fun loadThumbnailForceRefresh(project: LightweightResourceValidator.LightweightProject) {
            try {
                val projectSaveManager = ProjectSaveManager(itemView.context)
                val projectName = getProjectNameForSave(project)
                val savedPreviewPath = projectSaveManager.getPreviewImagePath(projectName)

                if (savedPreviewPath != null && File(savedPreviewPath).exists()) {
                    // 强制刷新保存的进度图片
                    val previewFile = File(savedPreviewPath)
                    val lastModified = previewFile.lastModified()

                    Log.d(TAG, "强制刷新进度缩略图: $projectName")

                    Glide.with(itemView.context)
                        .load(previewFile)
                        .placeholder(R.drawable.ic_image_placeholder)
                        .error(R.drawable.ic_image_error)
                        .centerCrop()
                        .skipMemoryCache(true) // 强制跳过内存缓存
                        .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.NONE) // 强制跳过磁盘缓存
                        .signature(com.bumptech.glide.signature.ObjectKey("refresh_${System.currentTimeMillis()}")) // 使用时间戳强制刷新
                        .into(imagePreview)
                } else {
                    // 如果没有进度图片，加载原图
                    Log.d(TAG, "没有进度图片，加载原图: $projectName")
                    loadThumbnailOptimized(project)
                }
            } catch (e: Exception) {
                Log.e(TAG, "强制刷新缩略图失败: ${project.id}", e)
                // 降级到普通加载
                loadThumbnailOptimized(project)
            }
        }

        /**
         * 只刷新进度（用于payload更新）
         */
        fun refreshProgress(project: LightweightResourceValidator.LightweightProject) {
            Log.d(TAG, "refreshProgress: ${project.id}")
            updateStatsWithProgress(project)
        }
    }

    // ========== 刷新方法 ==========

    /**
     * 刷新特定项目的显示
     * 使用统一的项目匹配逻辑
     */
    fun refreshProject(projectId: String) {
        Log.d(TAG, "🔍 开始查找项目进行刷新: $projectId")
        Log.d(TAG, "📋 当前项目列表大小: ${projects.size}")

        val position = projects.indexOfFirst { project ->
            val unifiedId = ProjectNameUtils.getUnifiedProjectId(project)
            val isMatch = ProjectNameUtils.isProjectMatch(unifiedId, projectId)
            Log.d(TAG, "🔍 检查项目: ${project.id} -> $unifiedId, 匹配: $isMatch")
            isMatch
        }

        if (position != -1) {
            Log.d(TAG, "✅ 找到匹配项目，刷新显示: $projectId, 位置: $position")
            notifyItemChanged(position, "progress")
        } else {
            Log.w(TAG, "❌ 未找到匹配的项目进行刷新: $projectId")
            // 输出调试信息
            Log.d(TAG, "📋 当前所有项目列表:")
            projects.forEachIndexed { index, project ->
                val unifiedId = ProjectNameUtils.getUnifiedProjectId(project)
                Log.d(TAG, "  项目 $index: id=${project.id}, displayName=${project.displayName}, unified=$unifiedId")
            }
        }
    }

    /**
     * 刷新特定项目的缩略图
     */
    fun refreshProjectThumbnail(projectId: String) {
        Log.d(TAG, "🖼️ 开始查找项目进行缩略图刷新: $projectId")

        val position = projects.indexOfFirst { project ->
            val unifiedId = ProjectNameUtils.getUnifiedProjectId(project)
            val isMatch = ProjectNameUtils.isProjectMatch(unifiedId, projectId)
            Log.d(TAG, "🖼️ 检查项目缩略图: ${project.id} -> $unifiedId, 匹配: $isMatch")
            isMatch
        }

        if (position != -1) {
            Log.d(TAG, "✅ 找到匹配项目，刷新缩略图: $projectId, 位置: $position")
            // 使用payload来指示只刷新缩略图
            notifyItemChanged(position, "thumbnail")
        } else {
            Log.w(TAG, "❌ 未找到匹配的项目进行缩略图刷新: $projectId")
        }
    }

    /**
     * 刷新所有项目
     */
    fun refreshAllProjects() {
        Log.d(TAG, "刷新所有项目显示")
        notifyDataSetChanged()
    }
}

/**
 * DiffUtil回调，用于智能更新项目列表
 */
class ProjectDiffCallback(
    private val oldList: List<LightweightResourceValidator.LightweightProject>,
    private val newList: List<LightweightResourceValidator.LightweightProject>
) : androidx.recyclerview.widget.DiffUtil.Callback() {

    override fun getOldListSize(): Int = oldList.size

    override fun getNewListSize(): Int = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition].id == newList[newItemPosition].id
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldList[oldItemPosition]
        val newItem = newList[newItemPosition]

        // 简化比较逻辑，只比较基本属性
        // 进度和预览图的更新通过单项目刷新机制处理
        return oldItem.id == newItem.id &&
                oldItem.displayName == newItem.displayName &&
                oldItem.category == newItem.category &&
                oldItem.isValid == newItem.isValid
    }
}
