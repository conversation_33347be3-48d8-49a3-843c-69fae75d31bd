package com.example.coloringproject.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.coloringproject.R
import com.example.coloringproject.viewmodel.DailyChallengeViewModel
import com.google.android.material.card.MaterialCardView
import java.text.SimpleDateFormat
import java.util.*

/**
 * 每日挑战适配器
 */
class DailyChallengeAdapter(
    private val onChallengeClick: (DailyChallengeViewModel.DailyChallenge) -> Unit
) : RecyclerView.Adapter<DailyChallengeAdapter.ChallengeViewHolder>() {
    
    private var challenges: List<DailyChallengeViewModel.DailyChallenge> = emptyList()
    private val dateFormat = SimpleDateFormat("MM/dd", Locale.getDefault())
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChallengeViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_daily_challenge, parent, false)
        return ChallengeViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ChallengeViewHolder, position: Int) {
        holder.bind(challenges[position])
    }
    
    override fun getItemCount(): Int = challenges.size
    
    /**
     * 更新挑战列表
     */
    fun updateChallenges(newChallenges: List<DailyChallengeViewModel.DailyChallenge>) {
        challenges = newChallenges
        notifyDataSetChanged()
    }
    
    inner class ChallengeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cardView: MaterialCardView = itemView.findViewById(R.id.cardChallenge)
        private val dateText: TextView = itemView.findViewById(R.id.tvChallengeDate)
        private val titleText: TextView = itemView.findViewById(R.id.tvChallengeTitle)
        private val descriptionText: TextView = itemView.findViewById(R.id.tvChallengeDescription)
        private val progressBar: ProgressBar = itemView.findViewById(R.id.progressChallenge)
        private val progressText: TextView = itemView.findViewById(R.id.tvChallengeProgress)
        private val statusIcon: ImageView = itemView.findViewById(R.id.ivChallengeStatus)
        private val rewardText: TextView = itemView.findViewById(R.id.tvChallengeReward)
        
        fun bind(challenge: DailyChallengeViewModel.DailyChallenge) {
            // 设置日期
            try {
                val date = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).parse(challenge.date)
                dateText.text = date?.let { dateFormat.format(it) } ?: challenge.date
            } catch (e: Exception) {
                dateText.text = challenge.date
            }
            
            titleText.text = challenge.title
            descriptionText.text = challenge.description
            rewardText.text = "+${challenge.rewardPoints} 积分"
            
            // 设置进度
            val progressPercentage = (challenge.progress * 100).toInt()
            progressBar.progress = progressPercentage
            progressText.text = "$progressPercentage%"
            
            // 设置状态图标和卡片样式
            if (challenge.isCompleted) {
                statusIcon.setImageResource(R.drawable.ic_check_circle)
                statusIcon.setColorFilter(itemView.context.getColor(R.color.completed_color))
                cardView.alpha = 0.8f
                cardView.strokeColor = itemView.context.getColor(R.color.completed_color)
                cardView.strokeWidth = 2
            } else {
                statusIcon.setImageResource(R.drawable.ic_play_circle)
                statusIcon.setColorFilter(itemView.context.getColor(R.color.progress_color))
                cardView.alpha = 1f
                cardView.strokeColor = itemView.context.getColor(R.color.divider_color)
                cardView.strokeWidth = 1
            }
            
            // 设置点击事件
            cardView.setOnClickListener {
                onChallengeClick(challenge)
            }
            
            // 设置挑战类型背景色
            setChallengeTypeBackground(challenge.type)
        }
        
        private fun setChallengeTypeBackground(type: DailyChallengeViewModel.ChallengeType) {
            val colorRes = when (type) {
                DailyChallengeViewModel.ChallengeType.SPEED_CHALLENGE -> R.color.progress_color
                DailyChallengeViewModel.ChallengeType.PERFECT_MATCH -> R.color.completed_color
                DailyChallengeViewModel.ChallengeType.COLOR_MASTER -> R.color.toolbar_background
                DailyChallengeViewModel.ChallengeType.STREAK_BUILDER -> android.R.color.holo_orange_light
            }
            
            // 设置左边框颜色来表示挑战类型
            cardView.strokeColor = itemView.context.getColor(colorRes)
        }
    }
}
