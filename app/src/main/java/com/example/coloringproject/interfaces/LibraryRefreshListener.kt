package com.example.coloringproject.interfaces

/**
 * Library刷新监听器接口
 * 用于在项目进度更新后通知Library页面刷新
 */
interface LibraryRefreshListener {
    
    /**
     * 项目进度更新后的回调
     * @param projectId 项目ID
     * @param hasProgress 是否有进度数据
     * @param progressPercentage 进度百分比
     */
    fun onProjectProgressUpdated(projectId: String, hasProgress: Boolean, progressPercentage: Int = 0)
    
    /**
     * 预览图片更新后的回调
     * @param projectId 项目ID
     * @param previewImagePath 预览图片路径
     */
    fun onProjectPreviewUpdated(projectId: String, previewImagePath: String?)
    
    /**
     * 项目完成后的回调
     * @param projectId 项目ID
     */
    fun onProjectCompleted(projectId: String)
    
    /**
     * 刷新整个Library
     */
    fun refreshLibrary()
}
