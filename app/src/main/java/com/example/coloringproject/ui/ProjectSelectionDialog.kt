package com.example.coloringproject.ui

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.ImageView
import android.widget.TextView
import com.example.coloringproject.R
import com.example.coloringproject.utils.EnhancedAssetManager

/**
 * 项目选择对话框
 * 显示所有验证过的填色项目，只允许选择有效的项目
 */
class ProjectSelectionDialog(
    private val context: Context,
    private val projects: List<EnhancedAssetManager.ValidatedProject>,
    private val onProjectSelected: (EnhancedAssetManager.ValidatedProject) -> Unit
) {

    fun show() {
        val adapter = ProjectAdapter(context, projects)
        
        AlertDialog.Builder(context)
            .setTitle("选择填色项目")
            .setAdapter(adapter) { _, position ->
                val selectedProject = projects[position]
                if (selectedProject.isValid) {
                    onProjectSelected(selectedProject)
                } else {
                    showInvalidProjectDialog(selectedProject)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showInvalidProjectDialog(project: EnhancedAssetManager.ValidatedProject) {
        val errorMessage = buildString {
            appendLine("项目 '${project.name}' 无法使用")
            appendLine()
            appendLine("错误原因:")
            project.validationErrors.forEach { error ->
                appendLine("• $error")
            }
            appendLine()
            appendLine("请检查assets文件夹中是否有匹配的JSON和PNG文件。")
        }

        AlertDialog.Builder(context)
            .setTitle("❌ 项目无效")
            .setMessage(errorMessage)
            .setPositiveButton("确定", null)
            .show()
    }

    /**
     * 项目列表适配器
     */
    private class ProjectAdapter(
        private val context: Context,
        private val projects: List<EnhancedAssetManager.ValidatedProject>
    ) : BaseAdapter() {

        override fun getCount(): Int = projects.size

        override fun getItem(position: Int): EnhancedAssetManager.ValidatedProject = projects[position]

        override fun getItemId(position: Int): Long = position.toLong()

        override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
            val view = convertView ?: LayoutInflater.from(context).inflate(
                android.R.layout.simple_list_item_2, parent, false
            )

            val project = projects[position]
            
            val titleView = view.findViewById<TextView>(android.R.id.text1)
            val subtitleView = view.findViewById<TextView>(android.R.id.text2)

            // 设置项目标题
            val statusIcon = if (project.isValid) "✅" else "❌"
            titleView.text = "$statusIcon ${project.name}"

            // 设置项目详情
            val details = if (project.isValid) {
                buildString {
                    append("${project.totalRegions}个区域 • ")
                    append("${project.totalColors}种颜色 • ")
                    append("${project.fileSize.totalSizeKB}KB")
                    if (project.difficulty != "unknown") {
                        append(" • ${project.difficulty}")
                    }
                    if (project.estimatedTime > 0) {
                        append(" • 约${project.estimatedTime}分钟")
                    }
                }
            } else {
                "无效项目: ${project.validationErrors.firstOrNull() ?: "未知错误"}"
            }
            
            subtitleView.text = details

            // 设置样式
            if (project.isValid) {
                titleView.setTextColor(context.getColor(android.R.color.primary_text_light))
                subtitleView.setTextColor(context.getColor(android.R.color.secondary_text_light))
                view.alpha = 1.0f
            } else {
                titleView.setTextColor(context.getColor(android.R.color.tertiary_text_light))
                subtitleView.setTextColor(context.getColor(android.R.color.tertiary_text_light))
                view.alpha = 0.6f
            }

            return view
        }

        override fun isEnabled(position: Int): Boolean {
            // 只有有效项目才能被选择
            return projects[position].isValid
        }
    }

    companion object {
        /**
         * 快速显示项目选择对话框
         */
        fun showProjectSelection(
            context: Context,
            projects: List<EnhancedAssetManager.ValidatedProject>,
            onProjectSelected: (EnhancedAssetManager.ValidatedProject) -> Unit
        ) {
            ProjectSelectionDialog(context, projects, onProjectSelected).show()
        }

        /**
         * 显示项目验证报告
         */
        fun showValidationReport(
            context: Context,
            enhancedAssetManager: EnhancedAssetManager,
            projects: List<EnhancedAssetManager.ValidatedProject>
        ) {
            val report = enhancedAssetManager.getValidationReport(projects)
            
            AlertDialog.Builder(context)
                .setTitle("📊 项目验证报告")
                .setMessage(report)
                .setPositiveButton("确定", null)
                .setNeutralButton("选择项目") { _, _ ->
                    // 可以在这里再次显示项目选择对话框
                }
                .show()
        }

        /**
         * 显示项目详细信息
         */
        fun showProjectDetails(
            context: Context,
            project: EnhancedAssetManager.ValidatedProject
        ) {
            val details = buildString {
                appendLine("项目名称: ${project.name}")
                appendLine("项目ID: ${project.id}")
                appendLine()
                
                if (project.isValid) {
                    appendLine("✅ 项目状态: 有效")
                    appendLine()
                    appendLine("📊 项目信息:")
                    appendLine("• 区域数量: ${project.totalRegions}")
                    appendLine("• 颜色数量: ${project.totalColors}")
                    appendLine("• 难度等级: ${project.difficulty}")
                    if (project.estimatedTime > 0) {
                        appendLine("• 预计时间: ${project.estimatedTime}分钟")
                    }
                    appendLine()
                    appendLine("📁 文件信息:")
                    appendLine("• JSON文件: ${project.jsonFile} (${project.fileSize.jsonSizeKB}KB)")
                    appendLine("• 线稿文件: ${project.outlineFile} (${project.fileSize.outlineSizeKB}KB)")
                    if (project.regionFile != null) {
                        appendLine("• 区域文件: ${project.regionFile} (${project.fileSize.regionSizeKB}KB)")
                    }
                    appendLine("• 总大小: ${project.fileSize.totalSizeKB}KB")
                } else {
                    appendLine("❌ 项目状态: 无效")
                    appendLine()
                    appendLine("🚫 错误信息:")
                    project.validationErrors.forEach { error ->
                        appendLine("• $error")
                    }
                    appendLine()
                    appendLine("💡 解决建议:")
                    appendLine("1. 检查assets文件夹中是否有对应的JSON文件")
                    appendLine("2. 检查是否有匹配的PNG线稿文件")
                    appendLine("3. 确认JSON文件格式正确且包含必要数据")
                    appendLine("4. 确认PNG文件可以正常加载")
                }
            }

            AlertDialog.Builder(context)
                .setTitle("📋 项目详情")
                .setMessage(details)
                .setPositiveButton("确定", null)
                .show()
        }
    }
}
