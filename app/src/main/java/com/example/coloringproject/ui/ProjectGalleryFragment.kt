package com.example.coloringproject.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.example.coloringproject.databinding.FragmentProjectGalleryBinding
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.coloringproject.R
import com.example.coloringproject.adapter.ProjectGalleryAdapter
import com.example.coloringproject.utils.EnhancedAssetManager
import com.example.coloringproject.viewmodel.ProjectGalleryViewModel
import kotlinx.coroutines.launch

/**
 * 项目图库Fragment
 * 显示所有可用的填色项目
 */
class ProjectGalleryFragment : Fragment() {
    
    private val viewModel: ProjectGalleryViewModel by viewModels()
    private var _binding: FragmentProjectGalleryBinding? = null
    private val binding get() = _binding!!

    private lateinit var enhancedAssetManager: EnhancedAssetManager
    private lateinit var adapter: ProjectGalleryAdapter
    
    // 项目选择回调
    var onProjectSelected: ((EnhancedAssetManager.ValidatedProject) -> Unit)? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProjectGalleryBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        enhancedAssetManager = EnhancedAssetManager(requireContext())
        
        setupRecyclerView(view)
        setupObservers()
        loadProjects()
    }
    
    private fun setupRecyclerView(view: View) {
        binding.recyclerViewProjects = view.findViewById(R.id.recyclerViewProjects)
        
        // 设置网格布局，每行2个项目
        val layoutManager = GridLayoutManager(requireContext(), 2)
        binding.recyclerViewProjects.layoutManager = layoutManager
        
        // 创建适配器
        adapter = ProjectGalleryAdapter { project ->
            onProjectSelected?.invoke(project)
        }
        binding.recyclerViewProjects.adapter = adapter
    }
    
    private fun setupObservers() {
        viewModel.projects.observe(viewLifecycleOwner) { projects ->
            adapter.updateProjects(projects)
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // TODO: 显示/隐藏加载指示器
        }
        
        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                Toast.makeText(requireContext(), "加载失败: $it", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun loadProjects() {
        lifecycleScope.launch {
            try {
                val result = enhancedAssetManager.getValidatedProjects()
                result.fold(
                    onSuccess = { projects ->
                        viewModel.setProjects(projects)
                    },
                    onFailure = { error ->
                        viewModel.setError(error.message ?: "未知错误")
                    }
                )
            } catch (e: Exception) {
                viewModel.setError(e.message ?: "未知错误")
            }
        }
    }
    
    companion object {
        fun newInstance(): ProjectGalleryFragment {
            return ProjectGalleryFragment()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
