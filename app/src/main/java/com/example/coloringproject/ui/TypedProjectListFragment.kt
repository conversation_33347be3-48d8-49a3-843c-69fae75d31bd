package com.example.coloringproject.ui

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.example.coloringproject.config.ProjectListConfig
import com.example.coloringproject.utils.LightweightResourceValidator

/**
 * 通用的类型化项目列表Fragment
 * 通过传入不同的ProjectListConfig来支持不同类型的项目列表
 */
class TypedProjectListFragment : BaseProjectListFragment() {
    
    companion object {
        private const val TAG = "TypedProjectListFragment"
        private const val ARG_PROJECT_TYPE = "project_type"
        
        /**
         * 创建新项目列表Fragment
         */
        fun newInstanceForNewProjects(): TypedProjectListFragment {
            return newInstance(ProjectListConfig.ProjectType.NEW)
        }
        
        /**
         * 创建动物项目列表Fragment
         */
        fun newInstanceForAnimalProjects(): TypedProjectListFragment {
            return newInstance(ProjectListConfig.ProjectType.ANIMAL)
        }
        
        /**
         * 创建建筑项目列表Fragment
         */
        fun newInstanceForBuildingProjects(): TypedProjectListFragment {
            return newInstance(ProjectListConfig.ProjectType.BUILDING)
        }
        
        /**
         * 创建花园项目列表Fragment
         */
        fun newInstanceForGardensProjects(): TypedProjectListFragment {
            return newInstance(ProjectListConfig.ProjectType.GARDENS)
        }
        
        /**
         * 创建指定类型的Fragment实例
         */
        fun newInstance(projectType: ProjectListConfig.ProjectType): TypedProjectListFragment {
            val fragment = TypedProjectListFragment()
            val args = Bundle()
            args.putString(ARG_PROJECT_TYPE, projectType.id)
            fragment.arguments = args
            return fragment
        }

        /**
         * 根据分类名称创建Fragment实例（用于动态分类）
         */
        fun newInstanceForCategory(categoryName: String): TypedProjectListFragment {
            val fragment = TypedProjectListFragment()
            val args = Bundle()
            args.putString(ARG_PROJECT_TYPE, categoryName)
            fragment.arguments = args
            return fragment
        }
    }
    
    // 配置缓存
    private var cachedConfig: ProjectListConfig? = null
    
    /**
     * 获取项目类型
     */
    private fun getProjectType(): ProjectListConfig.ProjectType {
        val typeId = arguments?.getString(ARG_PROJECT_TYPE) ?: ProjectListConfig.ProjectType.NEW.id
        return ProjectListConfig.ProjectType.values().find { it.id == typeId } 
            ?: ProjectListConfig.ProjectType.NEW
    }
    
    /**
     * 获取配置
     */
    override fun getConfig(): ProjectListConfig {
        // 使用缓存避免重复创建
        if (cachedConfig == null) {
            val typeId = arguments?.getString(ARG_PROJECT_TYPE) ?: ProjectListConfig.ProjectType.NEW.id

            cachedConfig = when (typeId) {
                ProjectListConfig.ProjectType.NEW.id -> ProjectListConfig.createNewProjectsConfig()
                ProjectListConfig.ProjectType.ANIMAL.id -> ProjectListConfig.createAnimalProjectsConfig()
                ProjectListConfig.ProjectType.BUILDING.id -> ProjectListConfig.createBuildingProjectsConfig()
                ProjectListConfig.ProjectType.GARDENS.id -> ProjectListConfig.createGardensProjectsConfig()
                ProjectListConfig.ProjectType.ALL.id -> ProjectListConfig.createNewProjectsConfig()
                else -> {
                    // 动态分类：根据分类名称创建配置
                    Log.d(TAG, "创建动态分类配置: $typeId")
                    ProjectListConfig.createDynamicCategoryConfig(typeId)
                }
            }
        }
        return cachedConfig!!
    }
    
    /**
     * 创建自定义视图（使用默认布局）
     */
    override fun onCreateCustomView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // 使用默认布局
        return null
    }
    
    /**
     * 设置自定义UI
     */
    override fun setupCustomUI() {
        val config = getConfig()
        Log.d(TAG, "设置${config.title}自定义UI")
        
        // 根据不同类型进行UI定制
        when (getProjectType()) {
            ProjectListConfig.ProjectType.NEW -> {
                // 新项目的UI定制
                setupNewProjectsUI()
            }
            ProjectListConfig.ProjectType.ANIMAL -> {
                // 动物项目的UI定制
                setupAnimalProjectsUI()
            }
            ProjectListConfig.ProjectType.BUILDING -> {
                // 建筑项目的UI定制
                setupBuildingProjectsUI()
            }
            ProjectListConfig.ProjectType.CASTLE -> {
                // 城堡项目的UI定制
                setupCastleProjectsUI()
            }
            ProjectListConfig.ProjectType.GARDENS -> {
                // 花园项目的UI定制
                setupGardensProjectsUI()
            }
            ProjectListConfig.ProjectType.TREEHOUSE -> {
                // 树屋项目的UI定制
                setupTreehouseProjectsUI()
            }
            ProjectListConfig.ProjectType.ALL -> {
                // 全部项目的UI定制
                setupAllProjectsUI()
            }
        }
    }
    
    /**
     * 设置新项目UI
     */
    private fun setupNewProjectsUI() {
        // 保持原有的分类功能
        // 分类标签会在基类中处理
    }
    
    /**
     * 设置动物项目UI
     */
    private fun setupAnimalProjectsUI() {
        // 隐藏分类标签（因为配置中禁用了分类）
        try {
            binding.categoryTabsContainer.visibility = View.GONE
        } catch (e: Exception) {
            Log.w(TAG, "无法隐藏分类标签", e)
        }
    }
    
    /**
     * 设置建筑项目UI
     */
    private fun setupBuildingProjectsUI() {
        // 隐藏分类标签
        try {
            binding.categoryTabsContainer.visibility = View.GONE
        } catch (e: Exception) {
            Log.w(TAG, "无法隐藏分类标签", e)
        }
        
        // 可以设置不同的网格布局
        // binding.recyclerViewNewProjects.layoutManager = GridLayoutManager(requireContext(), 3)
    }
    
    /**
     * 设置城堡项目UI
     */
    private fun setupCastleProjectsUI() {
        // 隐藏分类标签
        try {
            binding.categoryTabsContainer.visibility = View.GONE
        } catch (e: Exception) {
            Log.w(TAG, "无法隐藏分类标签", e)
        }

        // 可以设置不同的颜色主题
        // binding.swipeRefreshNewProjects.setColorSchemeResources(R.color.castle_primary, R.color.castle_secondary)
    }

    /**
     * 设置花园项目UI
     */
    private fun setupGardensProjectsUI() {
        // 隐藏分类标签
        try {
            binding.categoryTabsContainer.visibility = View.GONE
        } catch (e: Exception) {
            Log.w(TAG, "无法隐藏分类标签", e)
        }

        // 可以设置不同的颜色主题
        // binding.swipeRefreshNewProjects.setColorSchemeResources(R.color.garden_primary, R.color.garden_secondary)
    }

    /**
     * 设置树屋项目UI
     */
    private fun setupTreehouseProjectsUI() {
        // 隐藏分类标签
        try {
            binding.categoryTabsContainer.visibility = View.GONE
        } catch (e: Exception) {
            Log.w(TAG, "无法隐藏分类标签", e)
        }

        // 可以设置不同的颜色主题
        // binding.swipeRefreshNewProjects.setColorSchemeResources(R.color.treehouse_primary, R.color.treehouse_secondary)
    }
    
    /**
     * 设置全部项目UI
     */
    private fun setupAllProjectsUI() {
        // 全部项目的特殊UI设置
    }
    
    /**
     * 项目加载完成回调
     */
    override fun onProjectsLoaded(projects: List<LightweightResourceValidator.LightweightProject>) {
        val config = getConfig()
        Log.d(TAG, "${config.title}加载完成: ${projects.size}个项目")
        
        // 可以根据类型进行不同的处理
        when (getProjectType()) {
            ProjectListConfig.ProjectType.ANIMAL -> {
                Log.d(TAG, "动物项目详情:")
                projects.take(3).forEach { project ->
                    Log.d(TAG, "  - ${project.id}: ${project.displayName}")
                }
            }
            ProjectListConfig.ProjectType.BUILDING -> {
                Log.d(TAG, "建筑项目详情:")
                projects.take(3).forEach { project ->
                    Log.d(TAG, "  - ${project.id}: ${project.displayName}")
                }
            }
            ProjectListConfig.ProjectType.GARDENS -> {
                Log.d(TAG, "花园项目详情:")
                projects.take(3).forEach { project ->
                    Log.d(TAG, "  - ${project.id}: ${project.displayName}")
                }
            }
            else -> {
                // 其他类型的处理
            }
        }
    }
    
    /**
     * 加载错误回调
     */
    override fun onLoadError(error: String) {
        val config = getConfig()
        Log.e(TAG, "${config.title}加载失败: $error")

        // 可以根据类型进行不同的错误处理
    }

}
