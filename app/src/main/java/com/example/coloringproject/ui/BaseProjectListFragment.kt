package com.example.coloringproject.ui

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.example.coloringproject.EnhancedMainActivity
import com.example.coloringproject.R
import com.example.coloringproject.adapter.LightweightProjectAdapter
import com.example.coloringproject.config.ProjectListConfig
import com.example.coloringproject.databinding.FragmentNewProjectsBinding
import com.example.coloringproject.interfaces.LibraryRefreshListener
import com.example.coloringproject.manager.ProjectDataManager
import com.example.coloringproject.manager.ProjectPreloadManager
import com.example.coloringproject.utils.CategoryManager
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.LibraryEventManager
import com.example.coloringproject.utils.LightweightResourceValidator
import kotlinx.coroutines.launch
import java.io.File

/**
 * 基础项目列表Fragment
 * 提供通用的项目列表功能，支持不同类型的项目列表
 */
abstract class BaseProjectListFragment : Fragment(), LibraryRefreshListener {
    
    companion object {
        private const val TAG = "BaseProjectListFragment"
        private const val ARG_CONFIG = "config"
    }
    
    // 抽象属性和方法，由子类实现
    abstract fun getConfig(): ProjectListConfig
    abstract fun onCreateCustomView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View?
    
    // 可选的自定义方法，子类可以重写
    open fun onProjectsLoaded(projects: List<LightweightResourceValidator.LightweightProject>) {}
    open fun onLoadError(error: String) {}
    open fun setupCustomUI() {}
    
    // 项目选择回调
    var onProjectSelected: ((HybridResourceManager.HybridProject, android.widget.ImageView) -> Unit)? = null
    
    // UI组件
    private var _binding: FragmentNewProjectsBinding? = null
    protected val binding get() = _binding!!
    
    // 数据组件
    private lateinit var projectDataManager: ProjectDataManager
    private lateinit var lightweightAdapter: LightweightProjectAdapter
    private lateinit var preloadManager: ProjectPreloadManager
    
    // 状态管理
    private var allLightweightProjects = listOf<LightweightResourceValidator.LightweightProject>()
    private var categoryManager: CategoryManager? = null
    private var pendingCategoryManager: CategoryManager? = null
    
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // 优先使用子类的自定义布局
        val customView = onCreateCustomView(inflater, container, savedInstanceState)
        if (customView != null) {
            return customView
        }
        
        // 使用默认布局
        _binding = FragmentNewProjectsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 注册Library刷新监听器
        LibraryEventManager.registerListener(this)
        
        // 应用延迟设置的CategoryManager
        applyPendingCategoryManager()
        
        // 初始化组件
        initializeComponents()
        
        // 设置UI
        setupUI()
        
        // 设置自定义UI
        setupCustomUI()
        
        // 加载数据
        loadProjects()
    }
    
    override fun onResume() {
        super.onResume()
        
        Log.d("CALLBACK_FIX", "=== BaseProjectListFragment onResume ===")
        Log.d("CALLBACK_FIX", "Fragment类型: ${this::class.simpleName}")
        Log.d("CALLBACK_FIX", "Activity类型: ${activity?.javaClass?.simpleName}")
        
        // 检查回调是否有效，如果无效则请求重新设置
        if (onProjectSelected == null) {
            Log.w("CALLBACK_FIX", "⚠️ onResume时发现回调为null，请求重新设置")
            // 通知Activity重新设置回调
            val mainActivity = activity as? EnhancedMainActivity
            if (mainActivity != null) {
                Log.d("CALLBACK_FIX", "调用Activity的ensureNewProjectsFragmentCallback方法")
                mainActivity.ensureNewProjectsFragmentCallback()
                
                // 再次检查回调是否已设置
                if (onProjectSelected != null) {
                    Log.d("CALLBACK_FIX", "✅ 回调重新设置成功")
                } else {
                    Log.e("CALLBACK_FIX", "❌ 回调重新设置失败")
                }
            } else {
                Log.e("CALLBACK_FIX", "❌ Activity不是EnhancedMainActivity类型")
            }
        } else {
            Log.d("CALLBACK_FIX", "✅ onResume时回调有效")
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        
        // 取消注册Library刷新监听器
        LibraryEventManager.unregisterListener(this)
        
        // 清理缓存
        clearFragmentCache()
        
        _binding = null
    }
    
    /**
     * 初始化组件
     */
    private fun initializeComponents() {
        projectDataManager = ProjectDataManager.getInstance(requireContext())
        preloadManager = ProjectPreloadManager.getInstance(requireContext())
    }
    
    /**
     * 设置UI
     */
    private fun setupUI() {
        val config = getConfig()
        
        // 设置标题
        // binding.tvTitle?.text = config.title
        
        // 设置RecyclerView
        setupRecyclerView(config)
        
        // 设置下拉刷新
        if (config.enableSwipeRefresh) {
            setupSwipeRefresh()
        }
        
        // 设置搜索
        if (config.enableSearch) {
            setupSearch()
        }
        
        // 设置随机项目按钮
        if (config.enableRandomProject) {
            setupFab()
        }
    }
    
    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView(config: ProjectListConfig) {
        val spanCount = if (resources.configuration.screenWidthDp >= 600) config.spanCount + 1 else config.spanCount
        val layoutManager = GridLayoutManager(requireContext(), spanCount)
        binding.recyclerViewNewProjects.layoutManager = layoutManager
        
        // 创建适配器
        lightweightAdapter = LightweightProjectAdapter { project, imageView ->
            convertAndStartProject(project, imageView)
        }
        binding.recyclerViewNewProjects.adapter = lightweightAdapter
    }
    
    /**
     * 设置下拉刷新
     */
    private fun setupSwipeRefresh() {
        binding.swipeRefreshNewProjects.setOnRefreshListener {
            refreshProjects()
        }
        binding.swipeRefreshNewProjects.setColorSchemeResources(
            R.color.progress_color,
            R.color.toolbar_background,
            R.color.completed_color
        )
    }
    
    /**
     * 设置搜索
     */
    private fun setupSearch() {
        binding.btnFilterNewProjects.setOnClickListener {
            // 搜索功能实现
        }
    }
    
    /**
     * 设置随机项目按钮
     */
    private fun setupFab() {
        binding.fabRandomProject.setOnClickListener {
            selectRandomProject()
        }
    }
    
    /**
     * 加载项目数据
     */
    private fun loadProjects() {
        lifecycleScope.launch {
            try {
                binding.swipeRefreshNewProjects.isRefreshing = true
                
                val config = getConfig()
                val result = projectDataManager.loadProjects(config)
                
                when (result) {
                    is ProjectDataManager.LoadResult.Success -> {
                        allLightweightProjects = result.projects
                        lightweightAdapter.updateProjects(result.projects)
                        updateProjectCount(result.projects.size)
                        onProjectsLoaded(result.projects)
                        Log.d(TAG, "项目加载成功: ${result.projects.size}个")
                    }
                    is ProjectDataManager.LoadResult.Error -> {
                        val errorMsg = "加载失败: ${result.message}"
                        Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_SHORT).show()
                        onLoadError(result.message)
                        Log.e(TAG, errorMsg, result.exception)
                    }
                    is ProjectDataManager.LoadResult.Loading -> {
                        // 处理加载状态
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "加载项目异常", e)
                Toast.makeText(requireContext(), "加载异常: ${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                binding.swipeRefreshNewProjects.isRefreshing = false
            }
        }
    }
    
    /**
     * 刷新项目数据
     */
    private fun refreshProjects() {
        // 清理缓存后重新加载
        projectDataManager.clearCache(getConfig().type)
        loadProjects()
    }
    
    /**
     * 更新项目数量显示
     */
    private fun updateProjectCount(count: Int) {
        binding.tvNewProjectCount.text = "共 $count 个项目"
    }
    
    /**
     * 选择随机项目
     */
    private fun selectRandomProject() {
        if (allLightweightProjects.isNotEmpty()) {
            val randomProject = allLightweightProjects.random()
            // 创建一个临时的ImageView用于过渡动画
            val tempImageView = android.widget.ImageView(requireContext())
            tempImageView.transitionName = "project_image_${randomProject.id}"
            
            // 转换并启动项目
            convertAndStartProject(randomProject, tempImageView)
        }
    }
    
    /**
     * 转换轻量级项目为混合项目并启动
     */
    private fun convertAndStartProject(lightweightProject: LightweightResourceValidator.LightweightProject, imageView: android.widget.ImageView) {
        Log.d(TAG, "=== 转换并启动项目 ===")
        Log.d(TAG, "轻量级项目ID: ${lightweightProject.id}")
        Log.d(TAG, "轻量级项目名称: ${lightweightProject.displayName}")
        Log.d(TAG, "轻量级项目来源: ${lightweightProject.resourceSource}")
        Log.d(TAG, "轻量级项目有效性: ${lightweightProject.isValid}")

        // 检查回调是否有效
        Log.d("CALLBACK_FIX", "检查onProjectSelected回调: ${if (onProjectSelected != null) "有效" else "NULL"}")

        if (onProjectSelected == null) {
            Log.e("CALLBACK_FIX", "❌ onProjectSelected回调为null，无法启动项目")
            Toast.makeText(requireContext(), "项目启动失败：回调丢失，请重新进入页面", Toast.LENGTH_LONG).show()
            return
        }

        // 对于STREAMING类型的项目，需要先下载
        if (lightweightProject.resourceSource == HybridResourceManager.Companion.ResourceSource.STREAMING) {
            Log.d(TAG, "检测到STREAMING项目，开始下载...")
            downloadAndStartStreamingProject(lightweightProject, imageView)
            return
        }

        // 将轻量级项目转换为混合项目格式
        val hybridProject = HybridResourceManager.HybridProject(
            id = lightweightProject.id,
            name = lightweightProject.id,
            displayName = lightweightProject.displayName,
            description = lightweightProject.description ?: "",
            category = lightweightProject.category ?: "other",
            difficulty = lightweightProject.difficulty,
            totalRegions = 0, // 这些信息将在绘图页面加载时获取
            totalColors = 0,
            estimatedTime = 0,
            thumbnailUrl = "",
            previewUrl = "",
            resourceType = HybridResourceManager.Companion.ResourceType.LOCAL_ASSET,
            resourceSource = lightweightProject.resourceSource,
            version = "1.0",
            fileSize = lightweightProject.estimatedFileSize,
            isDownloaded = false,
            isBuiltIn = lightweightProject.resourceSource == HybridResourceManager.Companion.ResourceSource.BUILT_IN,
            downloadProgress = 0f,
            tags = emptyList(),
            releaseDate = null,
            popularity = 0,
            rating = 0.0f
        )

        Log.d(TAG, "混合项目转换完成:")
        Log.d(TAG, "  ID: ${hybridProject.id}")
        Log.d(TAG, "  名称: ${hybridProject.displayName}")
        Log.d(TAG, "  来源: ${hybridProject.resourceSource}")
        Log.d(TAG, "  是否内置: ${hybridProject.isBuiltIn}")
        Log.d(TAG, "========================")

        // 检查是否有预加载数据
        val preloadedData = preloadManager.getPreloadedProject(lightweightProject.id)

        if (preloadedData != null) {
            Log.d(TAG, "发现预加载数据，快速启动项目: ${lightweightProject.id}")
            // 标记有预加载数据
            hybridProject.hasPreloadedData = true
        } else {
            Log.d(TAG, "无预加载数据，使用常规启动: ${lightweightProject.id}")
        }

        // 启动项目
        Log.d("CALLBACK_FIX", "调用onProjectSelected回调...")
        try {
            onProjectSelected!!.invoke(hybridProject, imageView)
            Log.d("CALLBACK_FIX", "✅ 回调调用成功")
        } catch (e: Exception) {
            Log.e("CALLBACK_FIX", "❌ 回调调用失败", e)
            Toast.makeText(requireContext(), "项目启动失败：${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 下载并启动STREAMING项目
     */
    private fun downloadAndStartStreamingProject(lightweightProject: LightweightResourceValidator.LightweightProject, imageView: android.widget.ImageView) {
        Log.d(TAG, "开始下载STREAMING项目: ${lightweightProject.id}")

        // 显示下载进度对话框
        val progressDialog = android.app.ProgressDialog(requireContext()).apply {
            setTitle("下载项目")
            setMessage("正在下载 ${lightweightProject.displayName}...")
            setProgressStyle(android.app.ProgressDialog.STYLE_HORIZONTAL)
            max = 100
            setCancelable(false)
            show()
        }

        // 使用ResourceDownloadManager下载项目
        val downloadManager = com.example.coloringproject.network.ResourceDownloadManager(requireContext())

        lifecycleScope.launch {
            try {
                val result = downloadManager.downloadProject(lightweightProject.id) { progress ->
                    // 更新进度
                    requireActivity().runOnUiThread {
                        progressDialog.progress = (progress * 100).toInt()
                    }
                }

                progressDialog.dismiss()

                if (result.isSuccess) {
                    Log.d(TAG, "项目下载成功: ${lightweightProject.id}")
                    Toast.makeText(requireContext(), "下载完成，正在启动项目...", Toast.LENGTH_SHORT).show()

                    // 创建下载后的项目对象
                    val downloadedProject = lightweightProject.copy(
                        resourceSource = HybridResourceManager.Companion.ResourceSource.DOWNLOADED
                    )

                    // 启动下载后的项目
                    convertAndStartDownloadedProject(downloadedProject, imageView)
                } else {
                    Log.e(TAG, "项目下载失败: ${lightweightProject.id}")
                    val error = result.exceptionOrNull()?.message ?: "未知错误"
                    Toast.makeText(requireContext(), "下载失败: $error", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                progressDialog.dismiss()
                Log.e(TAG, "下载项目异常: ${lightweightProject.id}", e)
                Toast.makeText(requireContext(), "下载异常: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * 启动已下载的项目
     */
    private fun convertAndStartDownloadedProject(lightweightProject: LightweightResourceValidator.LightweightProject, imageView: android.widget.ImageView) {
        Log.d(TAG, "启动已下载项目: ${lightweightProject.id}")

        // 构建下载文件的路径
        val downloadDir = File(requireContext().filesDir, "downloaded_projects/${lightweightProject.id}")
        val jsonFile = File(downloadDir, "${lightweightProject.id}.json")
        val outlineFile = File(downloadDir, "${lightweightProject.id}.png")

        Log.d(TAG, "检查下载文件:")
        Log.d(TAG, "  JSON文件: ${jsonFile.absolutePath}, 存在: ${jsonFile.exists()}, 大小: ${if (jsonFile.exists()) jsonFile.length() else 0}")
        Log.d(TAG, "  PNG文件: ${outlineFile.absolutePath}, 存在: ${outlineFile.exists()}, 大小: ${if (outlineFile.exists()) outlineFile.length() else 0}")

        // 验证文件是否存在
        if (!jsonFile.exists() || !outlineFile.exists()) {
            Log.e(TAG, "下载的文件不完整，无法启动项目")
            Toast.makeText(requireContext(), "项目文件不完整，请重新下载", Toast.LENGTH_LONG).show()
            return
        }

        // 创建更新后的轻量级项目，设置正确的路径和资源来源
        val updatedLightweightProject = lightweightProject.copy(
            resourceSource = HybridResourceManager.Companion.ResourceSource.DOWNLOADED,
            jsonPath = jsonFile.absolutePath,
            outlinePath = outlineFile.absolutePath
        )

        Log.d(TAG, "更新后的项目路径:")
        Log.d(TAG, "  JSON路径: ${updatedLightweightProject.jsonPath}")
        Log.d(TAG, "  PNG路径: ${updatedLightweightProject.outlinePath}")

        // 将轻量级项目转换为混合项目格式
        val hybridProject = HybridResourceManager.HybridProject(
            id = lightweightProject.id,
            name = lightweightProject.id,
            displayName = lightweightProject.displayName,
            description = lightweightProject.description ?: "",
            category = lightweightProject.category ?: "other",
            difficulty = lightweightProject.difficulty,
            totalRegions = 0, // 这些信息将在绘图页面加载时获取
            totalColors = 0,
            estimatedTime = 0,
            thumbnailUrl = "",
            previewUrl = "",
            resourceType = HybridResourceManager.Companion.ResourceType.DOWNLOADED,
            resourceSource = HybridResourceManager.Companion.ResourceSource.DOWNLOADED,
            version = "1.0",
            fileSize = lightweightProject.estimatedFileSize,
            isDownloaded = true,
            isBuiltIn = false
        )

        Log.d("CALLBACK_FIX", "调用onProjectSelected回调")
        onProjectSelected?.invoke(hybridProject, imageView)
        Log.d("CALLBACK_FIX", "✅ 成功启动下载项目: ${hybridProject.id}")
    }

    /**
     * 设置品类管理器（延迟设置）
     */
    fun setCategoryManager(manager: CategoryManager) {
        // 总是先保存到pending，在onViewCreated中统一处理
        pendingCategoryManager = manager

        // 如果Fragment已经创建完成，立即应用
        if (isAdded && view != null) {
            applyPendingCategoryManager()
        }
    }

    /**
     * 应用延迟设置的CategoryManager
     */
    private fun applyPendingCategoryManager() {
        pendingCategoryManager?.let { manager ->
            categoryManager = manager
            pendingCategoryManager = null

            // 如果配置启用了分类功能，初始化分类
            if (getConfig().enableCategories) {
                initializeCategories()
            }
        }
    }

    /**
     * 初始化品类
     */
    private fun initializeCategories() {
        lifecycleScope.launch {
            try {
                val manager = categoryManager ?: return@launch
                val result = manager.initialize()
                if (result.isSuccess) {
                    val availableCategories = result.getOrNull() ?: emptyList()
                    setupCategoryTabs(availableCategories)
                    loadDefaultCategory()
                } else {
                    Log.e(TAG, "分类初始化失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "分类初始化异常", e)
            }
        }
    }

    /**
     * 设置分类标签（子类可以重写）
     */
    protected open fun setupCategoryTabs(categories: List<CategoryManager.Category>) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 加载默认分类（子类可以重写）
     */
    protected open fun loadDefaultCategory() {
        // 默认实现为空，子类可以重写
    }

    /**
     * 清理Fragment缓存（轻量级清理，不影响项目列表显示）
     */
    fun clearFragmentCache() {
        try {
            Log.d("MEMORY_CLEANUP", "清理Fragment缓存...")

            // 只清理必要的状态，保留项目数据
            // 不清理 allLightweightProjects，避免项目列表消失

            Log.d("MEMORY_CLEANUP", "Fragment缓存清理完成（轻量级）")
        } catch (e: Exception) {
            Log.e("MEMORY_CLEANUP", "清理Fragment缓存失败", e)
        }
    }

    /**
     * 项目进度更新回调
     */
    override fun onProjectProgressUpdated(projectId: String, hasProgress: Boolean, progressPercentage: Int) {
        Log.d(TAG, "收到项目进度更新通知: $projectId, 进度: ${progressPercentage}%")

        // 在主线程中更新UI
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    // 刷新适配器中对应项目的显示
                    refreshProjectInAdapter(projectId)
                } catch (e: Exception) {
                    Log.e(TAG, "更新项目进度显示失败: $projectId", e)
                }
            }
        }
    }

    /**
     * 预览图片更新回调
     */
    override fun onProjectPreviewUpdated(projectId: String, previewImagePath: String?) {
        Log.d(TAG, "收到预览图片更新通知: $projectId, 路径: $previewImagePath")

        // 在主线程中更新UI
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    // 刷新适配器中对应项目的缩略图
                    refreshProjectThumbnail(projectId)
                } catch (e: Exception) {
                    Log.e(TAG, "更新项目缩略图失败: $projectId", e)
                }
            }
        }
    }

    /**
     * 项目完成回调
     */
    override fun onProjectCompleted(projectId: String) {
        Log.d(TAG, "收到项目完成通知: $projectId")

        // 项目完成时刷新整个项目显示
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    refreshProjectInAdapter(projectId)
                } catch (e: Exception) {
                    Log.e(TAG, "更新项目完成状态失败: $projectId", e)
                }
            }
        }
    }

    /**
     * 刷新Library
     */
    override fun refreshLibrary() {
        Log.d(TAG, "收到刷新整个Library的通知")

        // 刷新整个Library
        if (isAdded && view != null) {
            refreshProjects()
        }
    }

    /**
     * 刷新适配器中的特定项目
     */
    private fun refreshProjectInAdapter(projectId: String) {
        if (::lightweightAdapter.isInitialized) {
            lightweightAdapter.refreshProject(projectId)
        }
    }

    /**
     * 刷新适配器中的项目缩略图
     */
    private fun refreshProjectThumbnail(projectId: String) {
        if (::lightweightAdapter.isInitialized) {
            lightweightAdapter.refreshProjectThumbnail(projectId)
        }
    }
}
