package com.example.coloringproject.ui

import android.os.Bundle
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.example.coloringproject.BuildConfig
import com.example.coloringproject.EnhancedMainActivity
import com.example.coloringproject.R
import com.example.coloringproject.adapter.HybridProjectAdapter
import com.example.coloringproject.adapter.LightweightProjectAdapter
import com.example.coloringproject.databinding.FragmentNewProjectsBinding
import com.example.coloringproject.interfaces.LibraryRefreshListener
import com.example.coloringproject.manager.DynamicCategoryManager
import com.example.coloringproject.manager.ProjectDataManager
import com.example.coloringproject.utils.CategoryManager
import com.example.coloringproject.utils.EnhancedAssetManager
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.LibraryEventManager
import com.example.coloringproject.utils.LightweightResourceValidator
import com.example.coloringproject.network.ResourceDownloadManager
import com.example.coloringproject.utils.LoadResult
import com.example.coloringproject.utils.ProjectSaveManager
import com.example.coloringproject.manager.ProjectPreloadManager
import com.example.coloringproject.viewmodel.OptimizedResourceViewModel
import com.example.coloringproject.viewmodel.ResourceViewModel
import kotlinx.coroutines.launch
import java.io.File

/**
 * New Projects Fragment - 展示各种填色项目
 * 提供沉浸式的项目浏览体验
 */
class NewProjectsFragment : Fragment(), LibraryRefreshListener {

    companion object {
        private const val TAG = "NewProjectsFragment"

        fun newInstance(): NewProjectsFragment {
            return NewProjectsFragment()
        }
    }

    var onProjectSelected: ((HybridResourceManager.HybridProject, android.widget.ImageView) -> Unit)? = null

    private val viewModel: ResourceViewModel by viewModels()
    private val optimizedViewModel: OptimizedResourceViewModel by viewModels()
    private var _binding: FragmentNewProjectsBinding? = null
    private val binding get() = _binding!!

    private lateinit var adapter: HybridProjectAdapter
    private lateinit var lightweightAdapter: LightweightProjectAdapter
    private lateinit var lightweightValidator: LightweightResourceValidator
    private lateinit var enhancedAssetManager: EnhancedAssetManager
    private var categoryManager: CategoryManager? = null
    private lateinit var preloadManager: ProjectPreloadManager

    // 轻量级项目数据
    private var allLightweightProjects = listOf<LightweightResourceValidator.LightweightProject>()
    private var useLightweightMode = true // 默认使用轻量级模式

    private var selectedTab: TextView? = null
    private var currentCategory = "New"
    private var availableCategories = listOf<CategoryManager.Category>()
    private var pendingCategoryManager: CategoryManager? = null
    private val categoryLoadingStates = mutableMapOf<String, Boolean>()

    // 动态分类管理
    private lateinit var dynamicCategoryManager: DynamicCategoryManager
    private lateinit var projectDataManager: ProjectDataManager
    private var discoveredCategories = listOf<DynamicCategoryManager.CategoryInfo>()
    private var currentSelectedCategory: String = "all" // 默认显示全部

    // 加载状态管理 - 避免重复刷新
    private var isDataLoaded = false
    private var isInitialLoadComplete = false
    private val categoryTabs = mutableMapOf<String, TextView>()



    /**
     * 设置品类管理器（延迟设置）
     */
    fun setCategoryManager(manager: CategoryManager) {
        // 总是先保存到pending，在onViewCreated中统一处理
        pendingCategoryManager = manager

        // 如果Fragment已经创建完成，立即应用
        if (isAdded && view != null) {
            applyPendingCategoryManager()
        }
    }

    /**
     * 应用延迟设置的CategoryManager
     */
    private fun applyPendingCategoryManager() {
        pendingCategoryManager?.let { manager ->
            categoryManager = manager
            initializeCategories()
            pendingCategoryManager = null
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentNewProjectsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 强制注册Library刷新监听器
        Log.d("NewProjectsFragment", "🔧 onViewCreated: 强制注册Library监听器")
        LibraryEventManager.unregisterListener(this) // 先取消注册，避免重复
        LibraryEventManager.registerListener(this)
        Log.d("NewProjectsFragment", "✅ onViewCreated: Library监听器注册完成")

        // 应用延迟设置的CategoryManager
        applyPendingCategoryManager()

        enhancedAssetManager = EnhancedAssetManager(requireContext())
        lightweightValidator = LightweightResourceValidator(requireContext())
        preloadManager = ProjectPreloadManager.getInstance(requireContext())

        // 初始化动态分类管理器
        dynamicCategoryManager = DynamicCategoryManager.getInstance(requireContext())
        projectDataManager = ProjectDataManager.getInstance(requireContext())

        setupRecyclerView()
        setupSwipeRefresh()
        setupSmartPreloading()
        setupSearch()
        setupObservers()
        setupFab()

        // 设置动态分类标签
        setupDynamicCategoryTabs()

        // 混合加载模式：先加载本地资源，再获取服务端资源
        loadHybridProjects()

        // Debug模式下测试进度缩略图功能
        if (BuildConfig.DEBUG) {
            testProgressThumbnails()
        }
    }

    override fun onStart() {
        super.onStart()
        Log.d("NewProjectsFragment", "🔧 onStart: 确保监听器已注册")

        // 在onStart时确保监听器已注册
        if (LibraryEventManager.getListenerCount() == 0) {
            Log.d("NewProjectsFragment", "⚠️ onStart时发现没有监听器，重新注册")
            LibraryEventManager.registerListener(this)
        }
    }

    override fun onResume() {
        super.onResume()

        Log.d("CALLBACK_FIX", "=== onResume被调用 ===")
        Log.d("CALLBACK_FIX", "Activity类型: ${activity?.javaClass?.simpleName}")
        Log.d("CALLBACK_FIX", "数据加载状态: isDataLoaded=$isDataLoaded, isInitialLoadComplete=$isInitialLoadComplete")

        // 强制重新注册Library监听器，确保Fragment缓存时监听器有效
        Log.d("CALLBACK_FIX", "🔧 onResume时强制重新注册Library监听器")
        Log.d("CALLBACK_FIX", "📋 注册前监听器数量: ${LibraryEventManager.getListenerCount()}")
        LibraryEventManager.unregisterListener(this)
        LibraryEventManager.registerListener(this)

        // 验证监听器注册状态
        val listenerCount = LibraryEventManager.getListenerCount()
        Log.d("CALLBACK_FIX", "✅ onResume后监听器数量: $listenerCount")
        Log.d("CALLBACK_FIX", "📋 当前监听器列表: ${LibraryEventManager.getListenerInfo()}")

        // 检查回调是否有效，如果无效则请求重新设置
        if (onProjectSelected == null) {
            Log.w("CALLBACK_FIX", "⚠️ onResume时发现回调为null，请求重新设置")
            // 通知Activity重新设置回调
            val mainActivity = activity as? EnhancedMainActivity
            if (mainActivity != null) {
                Log.d("CALLBACK_FIX", "调用Activity的ensureNewProjectsFragmentCallback方法")
                mainActivity.ensureNewProjectsFragmentCallback()

                // 再次检查回调是否已设置
                if (onProjectSelected != null) {
                    Log.d("CALLBACK_FIX", "✅ 回调重新设置成功")
                } else {
                    Log.e("CALLBACK_FIX", "❌ 回调重新设置失败")
                }
            } else {
                Log.e("CALLBACK_FIX", "❌ Activity不是EnhancedMainActivity类型")
            }
        } else {
            Log.d("CALLBACK_FIX", "✅ onResume时回调有效")
        }

        // 避免重复加载数据
        if (!isDataLoaded) {
            Log.d("CALLBACK_FIX", "onResume时数据未加载，触发加载")
            loadHybridProjects()
        } else {
            Log.d("CALLBACK_FIX", "onResume时数据已加载，跳过重复加载")
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        Log.d("NewProjectsFragment", "🔍 Fragment可见性变化: $isVisibleToUser")
        Log.d("NewProjectsFragment", "📋 Fragment状态: isAdded=$isAdded, view=${view != null}")

        if (isVisibleToUser && isAdded && view != null) {
            // Fragment变为可见时，确保监听器已注册
            Log.d("NewProjectsFragment", "🔧 Fragment变为可见，强制重新注册监听器")
            Log.d("NewProjectsFragment", "📋 注册前监听器数量: ${LibraryEventManager.getListenerCount()}")
            LibraryEventManager.unregisterListener(this)
            LibraryEventManager.registerListener(this)
            Log.d("NewProjectsFragment", "✅ Fragment可见时监听器数量: ${LibraryEventManager.getListenerCount()}")
        } else if (!isVisibleToUser) {
            // Fragment变为不可见时，保留监听器注册状态
            Log.d("NewProjectsFragment", "👁️ Fragment变为不可见，保留监听器")
            Log.d("NewProjectsFragment", "📋 当前监听器数量: ${LibraryEventManager.getListenerCount()}")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        // 不要在onDestroyView时取消注册监听器！
        // 因为Fragment可能只是被隐藏，而不是真正销毁
        // 监听器应该在onDestroy时才取消注册
        Log.d("NewProjectsFragment", "🔧 onDestroyView: 保留监听器注册状态")

        // 清理缓存
        clearFragmentCache()

        _binding = null
    }

    /**
     * 清理Fragment缓存（轻量级清理，不影响项目列表显示）
     */
    fun clearFragmentCache() {
        try {
            Log.d("MEMORY_CLEANUP", "清理Fragment缓存...")

            // 只清理分类加载状态，保留项目数据和分类数据
            categoryLoadingStates.clear()

            // 不清理 allLightweightProjects 和 availableCategories，避免项目列表消失
            // 不清理适配器数据，保持UI显示

            Log.d("MEMORY_CLEANUP", "Fragment缓存清理完成（轻量级）")
        } catch (e: Exception) {
            Log.e("MEMORY_CLEANUP", "清理Fragment缓存失败", e)
        }
    }

    // ========== LibraryRefreshListener 实现 ==========

    override fun onProjectProgressUpdated(projectId: String, hasProgress: Boolean, progressPercentage: Int) {
        Log.d("NewProjectsFragment", "🔔 收到项目进度更新通知: $projectId, 进度: ${progressPercentage}%")
        Log.d("NewProjectsFragment", "📋 Fragment状态: isAdded=$isAdded, view=${view != null}")
        Log.d("NewProjectsFragment", "📋 适配器状态: lightweightAdapter=${::lightweightAdapter.isInitialized}, adapter=${::adapter.isInitialized}")

        // 在主线程中更新UI
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    // 刷新适配器中对应项目的显示
                    Log.d("NewProjectsFragment", "🔄 开始刷新项目进度显示: $projectId")
                    refreshProjectInAdapter(projectId)
                    Log.d("NewProjectsFragment", "✅ 项目进度显示刷新完成: $projectId")
                } catch (e: Exception) {
                    Log.e("NewProjectsFragment", "❌ 更新项目进度显示失败: $projectId", e)
                }
            }
        } else {
            Log.w("NewProjectsFragment", "⚠️ Fragment状态不允许更新UI: isAdded=$isAdded, view=${view != null}")
        }
    }

    override fun onProjectPreviewUpdated(projectId: String, previewImagePath: String?) {
        Log.d("NewProjectsFragment", "🖼️ 收到预览图片更新通知: $projectId, 路径: $previewImagePath")
        Log.d("NewProjectsFragment", "📋 Fragment状态: isAdded=$isAdded, view=${view != null}")

        // 在主线程中更新UI
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    // 刷新适配器中对应项目的缩略图
                    Log.d("NewProjectsFragment", "🔄 开始刷新项目缩略图: $projectId")
                    refreshProjectThumbnail(projectId)
                    Log.d("NewProjectsFragment", "✅ 项目缩略图刷新完成: $projectId")
                } catch (e: Exception) {
                    Log.e("NewProjectsFragment", "❌ 更新项目缩略图失败: $projectId", e)
                }
            }
        } else {
            Log.w("NewProjectsFragment", "⚠️ Fragment状态不允许更新UI: isAdded=$isAdded, view=${view != null}")
        }
    }

    override fun onProjectCompleted(projectId: String) {
        Log.d("NewProjectsFragment", "收到项目完成通知: $projectId")

        // 项目完成时刷新整个项目显示
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    refreshProjectInAdapter(projectId)
                } catch (e: Exception) {
                    Log.e("NewProjectsFragment", "更新项目完成状态失败: $projectId", e)
                }
            }
        }
    }

    override fun refreshLibrary() {
        Log.d("NewProjectsFragment", "收到刷新整个Library的通知")

        // 刷新整个Library
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    // 重新加载项目列表
                    loadHybridProjects()
                } catch (e: Exception) {
                    Log.e("NewProjectsFragment", "刷新Library失败", e)
                }
            }
        }
    }

    // ========== 私有刷新方法 ==========

    /**
     * 刷新适配器中特定项目的显示
     */
    private fun refreshProjectInAdapter(projectId: String) {
        // 对于LightweightProjectAdapter
        if (::lightweightAdapter.isInitialized) {
            lightweightAdapter.refreshProject(projectId)
        }

        // 对于HybridProjectAdapter
        if (::adapter.isInitialized) {
            adapter.refreshProject(projectId)
        }
    }

    /**
     * 刷新适配器中特定项目的缩略图
     */
    private fun refreshProjectThumbnail(projectId: String) {
        // 对于LightweightProjectAdapter
        if (::lightweightAdapter.isInitialized) {
            lightweightAdapter.refreshProjectThumbnail(projectId)
        }

        // 对于HybridProjectAdapter
        if (::adapter.isInitialized) {
            adapter.refreshProjectThumbnail(projectId)
        }
    }


    private fun setupRecyclerView() {
        // 使用网格布局，每行2个项目，在平板上可以显示更多
        val spanCount = if (resources.configuration.screenWidthDp >= 600) 3 else 2
        val layoutManager = GridLayoutManager(requireContext(), spanCount)
        binding.recyclerViewNewProjects.layoutManager = layoutManager

        if (useLightweightMode) {
            // 创建轻量级项目适配器
            lightweightAdapter = LightweightProjectAdapter { project, imageView ->
                // 直接启动项目，传递项目信息和ImageView给绘图页面
                convertAndStartProject(project, imageView)
            }
            binding.recyclerViewNewProjects.adapter = lightweightAdapter
        } else {
            // 创建混合项目适配器
            adapter = HybridProjectAdapter(
                onProjectClick = { project ->
                    // 使用优化的数据加载策略
                    loadProjectDataOnDemand(project)
                },
                onDownloadClick = { project ->
                    // 实现真正的下载功能
                    startProjectDownload(project)
                },
                onDeleteClick = { project ->
                    // 实现删除功能
                    deleteProject(project)
                }
            )
            binding.recyclerViewNewProjects.adapter = adapter
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshNewProjects.setOnRefreshListener {
            loadProjects()
        }
        binding.swipeRefreshNewProjects.setColorSchemeResources(
            R.color.progress_color,
            R.color.toolbar_background,
            R.color.completed_color
        )
    }
    
    private fun setupSearch() {
        binding.btnFilterNewProjects.setOnClickListener {
        }
    }
    
    // 旧的硬编码分类标签方法已被动态分类系统替代
    // 分类标签现在在 setupCategoryTabsNew() 中动态创建
    
    private fun setupObservers() {
        if (!useLightweightMode) {
            // 使用优化的ViewModel观察项目列表
            optimizedViewModel.projects.observe(viewLifecycleOwner) { projects ->
                adapter.submitList(projects)
                updateProjectCount(projects.size)
            }

            // 保持原有的观察器作为备用
            viewModel.projects.observe(viewLifecycleOwner) { projects ->
                if (optimizedViewModel.projects.value.isNullOrEmpty()) {
                    adapter.submitList(projects)
                    updateProjectCount(projects.size)
                }
            }
        }

        // 观察加载进度
        optimizedViewModel.loadingProgress.observe(viewLifecycleOwner) { progress ->
            binding.swipeRefreshNewProjects.isRefreshing = progress.current < progress.total
            // TODO: 可以添加进度显示
        }

        // 观察错误信息
        optimizedViewModel.errorMessage.observe(viewLifecycleOwner) { error ->
            error?.let {
                optimizedViewModel.clearError()
            }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            if (optimizedViewModel.loadingProgress.value == null) {
                binding.swipeRefreshNewProjects.isRefreshing = isLoading
            }
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) { error ->
            if (optimizedViewModel.errorMessage.value == null) {
                error?.let {
                    Toast.makeText(requireContext(), "加载失败: $it", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun setupFab() {
        binding.fabRandomProject.setOnClickListener {
            selectRandomProject()
        }
    }
    
    private fun loadProjects() {
        // 优先使用优化的加载策略
        optimizedViewModel.loadProjectsWithThumbnails(includeRemote = true, forceRefresh = false)

        // 备用加载策略
        viewModel.loadProjects(includeRemote = true, forceRefresh = false)
    }

    /**
     * 按需加载项目数据 - 优化版本
     * 只传递项目索引信息，由绘图页面自行加载
     */
    private fun loadProjectDataOnDemand(project: HybridResourceManager.HybridProject) {
        // 直接传递项目信息，不进行预加载
        // 绘图页面将根据项目信息自行加载资源
        // 创建一个临时的ImageView用于过渡动画
        val tempImageView = android.widget.ImageView(requireContext())
        tempImageView.transitionName = "project_image_${project.id}"
        onProjectSelected?.invoke(project, tempImageView)
    }

    /**
     * 混合加载项目：先加载本地资源，再获取服务端资源
     */
    private fun loadHybridProjects() {
        // 避免重复加载
        if (isDataLoaded) {
            Log.d("NewProjectsFragment", "数据已加载，跳过重复加载")
            return
        }

        Log.d("NewProjectsFragment", "开始混合加载项目")
        isDataLoaded = true

        lifecycleScope.launch {
            try {
                // 第一步：加载本地轻量级项目（立即显示）
                val localResult = lightweightValidator.getLightweightProjects(
                    includeBuiltIn = true,
                    includeDownloaded = true,
                    includeCached = false
                )

                localResult.fold(
                    onSuccess = { localProjects ->
                        allLightweightProjects = localProjects.toMutableList()
                        lightweightAdapter.updateProjects(allLightweightProjects)
                        updateCategoriesFromLightweightProjects(allLightweightProjects)

                        Log.d(TAG, "本地项目加载完成: ${localProjects.size} 个项目")

                        // 第二步：异步获取服务端资源
                        loadRemoteProjects()
                    },
                    onFailure = { error ->
                        Log.e(TAG, "加载本地项目失败: ${error.message}")
                        Toast.makeText(requireContext(), "加载本地项目失败: ${error.message}", Toast.LENGTH_SHORT).show()

                        // 即使本地加载失败，也尝试加载远程资源
                        loadRemoteProjects()
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "混合加载项目时出错", e)
                Toast.makeText(requireContext(), "加载项目失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 加载远程项目（仅缩略图）
     */
    private fun loadRemoteProjects() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "开始获取服务端资源...")

                // 使用ResourceDownloadManager获取项目列表
                val downloadManager = ResourceDownloadManager(requireContext())
                val remoteResult = downloadManager.getProjectsList()

                remoteResult.fold(
                    onSuccess = { response ->
                        val remoteProjects = response.data?.projects?.map { projectDto ->
                            // 构建完整的缩略图URL
                            val baseServerUrl = com.example.coloringproject.network.NetworkConfig.getBaseServerUrl(requireContext())
                            val fullThumbnailUrl = if (projectDto.thumbnailUrl?.startsWith("http") == true) {
                                projectDto.thumbnailUrl
                            } else {
                                val thumbnailPath = projectDto.thumbnailUrl ?: ""
                                if (thumbnailPath.startsWith("/api/")) {
                                    "$baseServerUrl$thumbnailPath"
                                } else {
                                    "$baseServerUrl/api/files$thumbnailPath"
                                }
                            }

                            Log.d(TAG, "远程项目缩略图URL: ${projectDto.id} -> $fullThumbnailUrl")

                            // 转换为轻量级项目
                            LightweightResourceValidator.LightweightProject(
                                id = projectDto.id,
                                displayName = projectDto.displayName,
                                description = projectDto.description,
                                category = projectDto.category,
                                difficulty = projectDto.difficulty,
                                resourceSource = HybridResourceManager.Companion.ResourceSource.STREAMING,
                                jsonPath = projectDto.files?.jsonUrl ?: "",
                                outlinePath = fullThumbnailUrl,
                                isValid = true,
                                validationErrors = emptyList(),
                                estimatedFileSize = (projectDto.files?.jsonSize ?: 0L) + (projectDto.files?.outlineSize ?: 0L),
                                thumbnailPath = fullThumbnailUrl
                            )
                        } ?: emptyList()

                        // 合并本地和远程项目
                        val allProjects = (allLightweightProjects + remoteProjects).distinctBy { it.id }
                        allLightweightProjects = allProjects
                        lightweightAdapter.updateProjects(allProjects)
                        updateCategoriesFromLightweightProjects(allProjects)

                        Log.d(TAG, "服务端资源获取完成: ${remoteProjects.size} 个远程项目，总计 ${allProjects.size} 个项目")
                    },
                    onFailure = { error ->
                        Log.e(TAG, "获取服务端资源失败: ${error.message}")
                        // 不显示错误提示，因为本地资源已经可用
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "获取服务端资源时出错", e)
                // 不显示错误提示，因为本地资源已经可用
            } finally {
                // 标记初始加载完成
                isInitialLoadComplete = true
                Log.d("NewProjectsFragment", "初始加载完成")
            }
        }
    }

    /**
     * 加载轻量级项目列表（保留原方法以备用）
     */
    private fun loadLightweightProjects() {
        lifecycleScope.launch {
            try {
                val result = lightweightValidator.getLightweightProjects(
                    includeBuiltIn = true,
                    includeDownloaded = true,
                    includeCached = false
                )

                result.fold(
                    onSuccess = { projects ->
                        allLightweightProjects = projects
                        lightweightAdapter.updateProjects(projects)
                        updateCategoriesFromLightweightProjects(projects)
                    },
                    onFailure = { error ->
                        Toast.makeText(requireContext(), "加载项目失败: ${error.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "加载项目失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 转换轻量级项目为混合项目并启动
     */
    private fun convertAndStartProject(lightweightProject: LightweightResourceValidator.LightweightProject, imageView: android.widget.ImageView) {
        Log.d("NewProjectsFragment", "=== 转换并启动项目 ===")
        Log.d("NewProjectsFragment", "轻量级项目ID: ${lightweightProject.id}")
        Log.d("NewProjectsFragment", "轻量级项目名称: ${lightweightProject.displayName}")
        Log.d("NewProjectsFragment", "轻量级项目来源: ${lightweightProject.resourceSource}")
        Log.d("NewProjectsFragment", "轻量级项目有效性: ${lightweightProject.isValid}")

        // 检查回调是否有效
        Log.d("CALLBACK_FIX", "检查onProjectSelected回调: ${if (onProjectSelected != null) "有效" else "NULL"}")

        if (onProjectSelected == null) {
            Log.e("CALLBACK_FIX", "❌ onProjectSelected回调为null，无法启动项目")
            Toast.makeText(requireContext(), "项目启动失败：回调丢失，请重新进入页面", Toast.LENGTH_LONG).show()
            return
        }

        // 将轻量级项目转换为混合项目格式
        val hybridProject = HybridResourceManager.HybridProject(
            id = lightweightProject.id,
            name = lightweightProject.id,
            displayName = lightweightProject.displayName,
            description = lightweightProject.description ?: "",
            category = lightweightProject.category ?: "other",
            difficulty = lightweightProject.difficulty,
            resourceSource = lightweightProject.resourceSource,
            resourceType = HybridResourceManager.Companion.ResourceType.LOCAL_ASSET,
            totalRegions = 0, // 这些信息将在绘图页面加载时获取
            totalColors = 0,
            estimatedTime = 0,
            fileSize = lightweightProject.estimatedFileSize,
            isBuiltIn = lightweightProject.resourceSource == HybridResourceManager.Companion.ResourceSource.BUILT_IN,
            isDownloaded = lightweightProject.resourceSource == HybridResourceManager.Companion.ResourceSource.REMOTE_DOWNLOADED,
            version = "1.0.0",
            thumbnailUrl = null,
            previewUrl = null,
            tags = emptyList(),
            releaseDate = "",
            popularity = 0,
            rating = 0.0f,
            // downloadCount = 0L  // 这个字段在HybridProject中不存在
        )

        Log.d("NewProjectsFragment", "混合项目转换完成:")
        Log.d("NewProjectsFragment", "  ID: ${hybridProject.id}")
        Log.d("NewProjectsFragment", "  名称: ${hybridProject.displayName}")
        Log.d("NewProjectsFragment", "  来源: ${hybridProject.resourceSource}")
        Log.d("NewProjectsFragment", "  是否内置: ${hybridProject.isBuiltIn}")
        Log.d("NewProjectsFragment", "========================")

        // 检查是否有预加载数据
        val preloadedData = preloadManager.getPreloadedProject(lightweightProject.id)

        if (preloadedData != null) {
            Log.d("NewProjectsFragment", "发现预加载数据，快速启动项目: ${lightweightProject.id}")
            // 标记有预加载数据
            hybridProject.hasPreloadedData = true
        } else {
            Log.d("NewProjectsFragment", "无预加载数据，使用常规启动: ${lightweightProject.id}")
        }

        // 启动项目
        Log.d("CALLBACK_FIX", "调用onProjectSelected回调...")
        try {
            onProjectSelected!!.invoke(hybridProject, imageView)
            Log.d("CALLBACK_FIX", "✅ 回调调用成功")
        } catch (e: Exception) {
            Log.e("CALLBACK_FIX", "❌ 回调调用失败", e)
            Toast.makeText(requireContext(), "项目启动失败：${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 测试进度缩略图功能
     */
    private fun testProgressThumbnails() {
        lifecycleScope.launch {
            try {
                Log.d("NewProjectsFragment", "开始测试进度缩略图功能...")

                // 简单的进度缩略图检查
                val projectSaveManager = ProjectSaveManager(requireContext())
                val testProjects = listOf("animal-1", "animal-2", "animal-3", "animal-9")
                var projectsWithThumbnails = 0

                testProjects.forEach { projectId ->
                    val previewPath = projectSaveManager.getPreviewImagePath(projectId)
                    val hasPreview = previewPath != null && File(previewPath).exists()
                    val progressResult = projectSaveManager.loadProgress(projectId)
                    val hasProgress = progressResult is LoadResult.Success &&
                                     progressResult.data.progressPercentage > 0

                    if (hasPreview && hasProgress) {
                        projectsWithThumbnails++
                        val progressInfo = (progressResult as LoadResult.Success).data
                        Log.d("NewProjectsFragment", "项目 $projectId 有进度缩略图: ${progressInfo.progressPercentage}%")
                    }
                }

                Log.d("NewProjectsFragment", "进度缩略图测试完成，发现 $projectsWithThumbnails 个项目有进度缩略图")

            } catch (e: Exception) {
                Log.e("NewProjectsFragment", "测试进度缩略图功能失败", e)
            }
        }
    }

    /**
     * 从轻量级项目更新分类信息
     */
    private fun updateCategoriesFromLightweightProjects(projects: List<LightweightResourceValidator.LightweightProject>) {
        val categories = projects.mapNotNull { it.category }.distinct()
        // 这里可以更新分类标签显示
        // 暂时保持现有的分类逻辑
    }
    
    private fun selectTab(tab: TextView, category: String) {
        // 重置所有标签状态
        resetAllTabs()

        // 设置选中状态
        selectedTab = tab
        currentCategory = category
        tab.isSelected = true
        tab.setTextColor(resources.getColor(R.color.category_text_selected, null))
        tab.setBackgroundResource(R.drawable.category_chip_background)

        // 筛选项目
        filterByCategory(category)
    }

    private fun resetAllTabs() {
        // 重置动态创建的所有分类标签
        for (i in 0 until binding.categoryTabsContainer.childCount) {
            val tab = binding.categoryTabsContainer.getChildAt(i) as? TextView
            tab?.let {
                it.isSelected = false
                it.setTextColor(resources.getColor(R.color.category_text_unselected, null))
                it.setBackgroundResource(R.drawable.category_chip_background)
            }
        }
    }

    private fun filterByCategory(categoryId: String) {
        android.util.Log.d("NewProjectsFragment", "Filtering by category: $categoryId")

        // 加载对应分类的项目数据
        loadCategoryProjects(categoryId)
    }
    
    private fun updateProjectCount(count: Int) {
        binding.tvNewProjectCount.text = "共 $count 个项目"
    }
    
    private fun showProjectDetails(project: HybridResourceManager.HybridProject) {
        // TODO: 显示项目详情对话框
    }
    
    private fun selectRandomProject() {
        val projects = viewModel.projects.value
        if (!projects.isNullOrEmpty()) {
            val randomProject = projects.random()
            // 创建一个临时的ImageView用于过渡动画
            val tempImageView = android.widget.ImageView(requireContext())
            tempImageView.transitionName = "project_image_${randomProject.id}"
            onProjectSelected?.invoke(randomProject, tempImageView)
        }
    }

    /**
     * 初始化品类
     */
    private fun initializeCategories() {
        lifecycleScope.launch {
            try {
                val manager = categoryManager ?: return@launch
                val result = manager.initialize()
                if (result.isSuccess) {
                    availableCategories = result.getOrNull() ?: emptyList()
                    setupCategoryTabsNew()
                    loadDefaultCategory()
                } else {
                }
            } catch (e: Exception) {
            }
        }
    }

    /**
     * 设置品类标签 - 显示API获取的分类
     */
    private fun setupCategoryTabsNew() {
        android.util.Log.d("NewProjectsFragment", "Setting up ${availableCategories.size} categories")

        // 清空现有的分类标签
        binding.categoryTabsContainer.removeAllViews()

        // 动态创建分类标签
        availableCategories.forEachIndexed { index, category ->
            val tabView = createCategoryTab(category)
            binding.categoryTabsContainer.addView(tabView)

            val loadingStatus = if (category.isPopular) "正在加载..." else "待加载"
            android.util.Log.d("NewProjectsFragment", "分类 ${index + 1}: ${category.name} (${category.projectCount} 个项目) - $loadingStatus")
        }

        // 默认选中第一个分类
        if (availableCategories.isNotEmpty()) {
            val firstCategory = availableCategories.first()
            val firstTab = binding.categoryTabsContainer.getChildAt(0) as? TextView
            firstTab?.let {
                selectTab(it, firstCategory.id)
            }
        }

        // 自动开始加载前两个热门分类
        val popularCategories = availableCategories.filter { it.isPopular }.take(2)
        popularCategories.forEach { category ->
            android.util.Log.d("NewProjectsFragment", "开始预加载热门分类: ${category.name}")
            loadCategoryProjects(category.id)
        }
    }

    /**
     * 创建分类标签
     */
    private fun createCategoryTab(category: CategoryManager.Category): TextView {
        val tabView = TextView(requireContext())

        // 设置基本属性
        tabView.text = category.name
        tabView.textSize = 14f
        tabView.setPadding(32, 16, 32, 16)

        // 设置样式
        tabView.setTextColor(resources.getColor(R.color.category_text_unselected, null))
        tabView.setBackgroundResource(R.drawable.category_chip_background)

        // 设置布局参数
        val layoutParams = ViewGroup.MarginLayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        layoutParams.marginEnd = 16
        tabView.layoutParams = layoutParams

        // 设置点击事件
        tabView.setOnClickListener {
            selectTab(tabView, category.id)
        }

        return tabView
    }

    /**
     * 加载默认品类
     */
    private fun loadDefaultCategory() {
        if (availableCategories.isNotEmpty()) {
            val defaultCategory = availableCategories.find { it.isPopular } ?: availableCategories.first()
            loadCategoryProjects(defaultCategory.id)
        }
    }



    /**
     * 更新分类加载状态
     */
    private fun updateCategoryLoadingStatus(categoryId: String, isLoading: Boolean) {
        val category = availableCategories.find { it.id == categoryId }
        if (category != null) {
            val status = if (isLoading) "正在加载..." else "加载完成"
            android.util.Log.d("NewProjectsFragment", "分类 ${category.name}: $status")
        }
    }

    /**
     * 更新项目列表
     */
    private fun updateProjectsList(projects: List<HybridResourceManager.HybridProject>) {
        // 这里应该更新RecyclerView的数据
        // adapter.updateProjects(projects) // 实际实现时取消注释
        android.util.Log.d("NewProjectsFragment", "Updated UI with ${projects.size} projects")
    }

    /**
     * 开始项目下载
     */
    private fun startProjectDownload(project: HybridResourceManager.HybridProject) {
        lifecycleScope.launch {
            try {
                Toast.makeText(requireContext(), "开始下载: ${project.displayName}", Toast.LENGTH_SHORT).show()

                // 获取下载管理器
                val manager = categoryManager?.getDownloadManager()

                if (manager != null) {
                    // 执行真正的下载
                    val result = manager.downloadProject(project.id) { progress ->
                        // 更新下载进度
                        activity?.runOnUiThread {
                            val progressPercent = (progress * 100).toInt()
                            android.util.Log.d("NewProjectsFragment", "下载进度: ${project.displayName} - $progressPercent%")
                        }
                    }

                    if (result.isSuccess) {
                        android.util.Log.d("NewProjectsFragment", "Download completed: ${project.displayName}")

                        // 刷新项目列表以显示下载状态变化
                        loadCategoryProjects(currentCategory)
                    } else {
                        val error = result.exceptionOrNull()?.message ?: "未知错误"
                        android.util.Log.e("NewProjectsFragment", "Download failed: ${project.displayName}, error: $error")
                    }
                } else {
                    android.util.Log.e("NewProjectsFragment", "DownloadManager not available")
                }

            } catch (e: Exception) {
                android.util.Log.e("NewProjectsFragment", "Download error: ${project.displayName}", e)
            }
        }
    }

    /**
     * 删除项目
     */
    private fun deleteProject(project: HybridResourceManager.HybridProject) {
        // 显示确认对话框
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("删除项目")
            .setMessage("确定要删除 ${project.displayName} 吗？")
            .setPositiveButton("删除") { _, _ ->
                lifecycleScope.launch {
                    try {
                        // 这里应该实现真正的删除逻辑
                        // 暂时只显示Toast
                        android.util.Log.d("NewProjectsFragment", "Deleted project: ${project.displayName}")

                        // 刷新项目列表
                        loadCategoryProjects(currentCategory)
                    } catch (e: Exception) {
                        android.util.Log.e("NewProjectsFragment", "Delete error: ${project.displayName}", e)
                    }
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 设置智能预加载
     * 监听RecyclerView滚动，预加载可见项目
     */
    private fun setupSmartPreloading() {
        binding.recyclerViewNewProjects.addOnScrollListener(object : androidx.recyclerview.widget.RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: androidx.recyclerview.widget.RecyclerView, newState: Int) {
                if (newState == androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE) {
                    // 滚动停止时，预加载可见项目
                    val visibleProjectIds = getVisibleProjectIds()
                    if (visibleProjectIds.isNotEmpty()) {
                        Log.d(TAG, "滚动停止，智能预加载可见项目: $visibleProjectIds")
                        preloadManager.smartPreload(visibleProjectIds)
                    }
                }
            }
        })
    }

    /**
     * 获取当前可见的项目ID列表
     */
    private fun getVisibleProjectIds(): List<String> {
        val layoutManager = binding.recyclerViewNewProjects.layoutManager as? GridLayoutManager ?: return emptyList()
        val firstVisible = layoutManager.findFirstVisibleItemPosition()
        val lastVisible = layoutManager.findLastVisibleItemPosition()

        if (firstVisible < 0 || lastVisible < 0) return emptyList()

        val visibleIds = mutableListOf<String>()

        // 获取可见项目的ID
        for (i in firstVisible..lastVisible) {
            if (i >= 0 && i < viewModel.projects.value?.size ?: 0) {
                val project = viewModel.projects.value?.get(i)
                if (project != null) {
                    visibleIds.add(project.id)
                }
            }
        }

        return visibleIds
    }

    /**
     * 设置动态分类标签
     */
    private fun setupDynamicCategoryTabs() {
        lifecycleScope.launch {
            try {
                Log.d("NewProjectsFragment", "开始设置动态分类标签...")

                // 发现所有分类
                val result = dynamicCategoryManager.discoverAndCreateCategories()
                if (result.isSuccess) {
                    val categories = result.getOrNull() ?: emptyList()
                    discoveredCategories = categories

                    Log.d("NewProjectsFragment", "发现 ${categories.size} 个分类")

                    // 在主线程中创建标签
                    withContext(Dispatchers.Main) {
                        createCategoryTabs(categories)
                    }
                } else {
                    Log.e("NewProjectsFragment", "发现分类失败", result.exceptionOrNull())
                }

            } catch (e: Exception) {
                Log.e("NewProjectsFragment", "设置动态分类标签失败", e)
            }
        }
    }

    /**
     * 创建分类标签
     */
    private fun createCategoryTabs(categories: List<DynamicCategoryManager.CategoryInfo>) {
        val container = binding.categoryTabsContainer
        container.removeAllViews()
        categoryTabs.clear()

        // 添加"全部"标签
        val allTab = createCategoryTab("全部", "all", true)
        container.addView(allTab)
        categoryTabs["all"] = allTab

        // 添加各个分类标签
        categories.forEach { category ->
            val tab = createCategoryTab(category.displayName, category.id, false)
            container.addView(tab)
            categoryTabs[category.id] = tab
        }

        Log.d("NewProjectsFragment", "创建了 ${categoryTabs.size} 个分类标签")
    }

    /**
     * 创建单个分类标签
     */
    private fun createCategoryTab(title: String, categoryId: String, isSelected: Boolean): TextView {
        val tab = TextView(requireContext()).apply {
            text = title
            setPadding(32, 16, 32, 16)
            setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
            gravity = Gravity.CENTER

            // 设置样式
            if (isSelected) {
                setBackgroundResource(R.drawable.category_tab_selected)
                setTextColor(ContextCompat.getColor(requireContext(), R.color.white))
            } else {
                setBackgroundResource(R.drawable.category_tab_normal)
                setTextColor(ContextCompat.getColor(requireContext(), R.color.text_primary))
            }

            // 设置点击事件
            setOnClickListener {
                onCategoryTabClicked(categoryId)
            }

            // 设置边距
            val layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                marginEnd = 16
            }
            this.layoutParams = layoutParams
        }

        return tab
    }

    /**
     * 分类标签点击事件
     */
    private fun onCategoryTabClicked(categoryId: String) {
        Log.d("NewProjectsFragment", "点击分类标签: $categoryId")

        // 更新选中状态
        updateTabSelection(categoryId)

        // 加载对应分类的数据
        loadCategoryProjects(categoryId)
    }

    /**
     * 更新标签选中状态
     */
    private fun updateTabSelection(selectedCategoryId: String) {
        currentSelectedCategory = selectedCategoryId

        categoryTabs.forEach { (categoryId, tab) ->
            if (categoryId == selectedCategoryId) {
                tab.setBackgroundResource(R.drawable.category_tab_selected)
                tab.setTextColor(ContextCompat.getColor(requireContext(), R.color.white))
            } else {
                tab.setBackgroundResource(R.drawable.category_tab_normal)
                tab.setTextColor(ContextCompat.getColor(requireContext(), R.color.text_primary))
            }
        }
    }

    /**
     * 加载分类项目数据
     */
    private fun loadCategoryProjects(categoryId: String) {
        lifecycleScope.launch {
            try {
                binding.swipeRefreshNewProjects.isRefreshing = true

                if (categoryId == "all") {
                    // 加载所有项目
                    loadHybridProjects()
                } else {
                    // 加载特定分类的项目
                    val config = dynamicCategoryManager.getConfigForCategory(categoryId)
                    val result = projectDataManager.loadProjects(config)

                    when (result) {
                        is ProjectDataManager.LoadResult.Success -> {
                            allLightweightProjects = result.projects
                            lightweightAdapter.updateProjects(result.projects)
                            updateProjectCount(result.projects.size)
                            Log.d("NewProjectsFragment", "分类 $categoryId 加载成功: ${result.projects.size} 个项目")
                        }
                        is ProjectDataManager.LoadResult.Error -> {
                            Log.e("NewProjectsFragment", "分类 $categoryId 加载失败: ${result.message}")
                            Toast.makeText(requireContext(), "加载失败: ${result.message}", Toast.LENGTH_SHORT).show()
                        }
                        else -> {
                            // 处理其他状态
                        }
                    }
                }

            } catch (e: Exception) {
                Log.e("NewProjectsFragment", "加载分类项目失败: $categoryId", e)
                Toast.makeText(requireContext(), "加载异常: ${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                binding.swipeRefreshNewProjects.isRefreshing = false
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        LibraryEventManager.unregisterListener(this)
    }
}
