package com.example.coloringproject.ui

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.example.coloringproject.adapter.LightweightProjectAdapter
import com.example.coloringproject.databinding.FragmentCategoryProjectBinding
import com.example.coloringproject.interfaces.LibraryRefreshListener
import com.example.coloringproject.manager.ProjectDataManager
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.LibraryEventManager
import com.example.coloringproject.utils.LightweightResourceValidator
import kotlinx.coroutines.launch

/**
 * 单个分类的项目Fragment
 * 显示特定分类下的所有项目
 */
class CategoryProjectFragment : Fragment(), LibraryRefreshListener {
    
    companion object {
        private const val TAG = "CategoryProjectFragment"
        private const val ARG_CATEGORY = "category"
        
        fun newInstance(category: String): CategoryProjectFragment {
            return CategoryProjectFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_CATEGORY, category)
                }
            }
        }
    }
    
    private var _binding: FragmentCategoryProjectBinding? = null
    private val binding get() = _binding ?: throw IllegalStateException("Fragment binding is null")
    
    private lateinit var projectDataManager: ProjectDataManager
    private lateinit var lightweightAdapter: LightweightProjectAdapter
    
    // 项目选择回调
    var onProjectSelected: ((HybridResourceManager.HybridProject, android.widget.ImageView) -> Unit)? = null
    
    // 分类名称
    private val category: String by lazy {
        arguments?.getString(ARG_CATEGORY) ?: "unknown"
    }
    
    // 项目数据
    private var projects: List<LightweightResourceValidator.LightweightProject> = emptyList()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCategoryProjectBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Log.d(TAG, "onViewCreated for category: $category")

        // 注册Library刷新监听器
        Log.d(TAG, "🔧 注册Library监听器: $category")
        LibraryEventManager.unregisterListener(this) // 先取消注册，避免重复
        LibraryEventManager.registerListener(this)
        Log.d(TAG, "✅ Library监听器注册完成: $category, 监听器数量: ${LibraryEventManager.getListenerCount()}")

        // 初始化组件
        initializeComponents()

        // 设置UI
        setupUI()

        // 加载项目数据
        loadProjects()
    }
    
    private fun initializeComponents() {
        projectDataManager = ProjectDataManager.getInstance(requireContext())
    }
    
    private fun setupUI() {
        // 设置RecyclerView
        setupRecyclerView()
        
        // 设置下拉刷新
        setupSwipeRefresh()
        
        // 设置随机项目按钮
        setupFab()
    }
    
    private fun setupRecyclerView() {
        val spanCount = if (resources.configuration.screenWidthDp >= 600) 3 else 2
        val layoutManager = GridLayoutManager(requireContext(), spanCount)
        binding.recyclerViewProjects.layoutManager = layoutManager
        
        // 创建适配器
        lightweightAdapter = LightweightProjectAdapter { project, imageView ->
            convertAndStartProject(project, imageView)
        }
        binding.recyclerViewProjects.adapter = lightweightAdapter
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshProjects.setOnRefreshListener {
            loadProjects(forceRefresh = true)
        }
        binding.swipeRefreshProjects.setColorSchemeResources(
            com.example.coloringproject.R.color.progress_color,
            com.example.coloringproject.R.color.toolbar_background,
            com.example.coloringproject.R.color.completed_color
        )
    }
    
    private fun setupFab() {
        binding.fabRandomProject.setOnClickListener {
            selectRandomProject()
        }
    }
    
    private fun loadProjects(forceRefresh: Boolean = false) {
        // 检查Fragment是否仍然活跃
        if (!isAdded || isDetached || _binding == null) {
            Log.w(TAG, "Fragment不活跃，跳过加载: $category")
            return
        }

        Log.d(TAG, "开始使用分层加载策略: $category")

        // 使用新的分层加载策略
        val loadStrategy = com.example.coloringproject.manager.LibraryLoadStrategy.getInstance(requireContext())
        
        loadStrategy.loadProjectsWithStrategy(
            categoryId = category,
            callback = object : com.example.coloringproject.manager.LibraryLoadStrategy.LoadCallback {
                override fun onLocalProjectsLoaded(localProjects: List<LightweightResourceValidator.LightweightProject>) {
                    // 本地项目加载完成，立即显示
                    if (!isAdded || _binding == null) {
                        Log.w(TAG, "本地项目加载完成时Fragment已不活跃: $category")
                        return
                    }
                    
                    Log.d(TAG, "本地项目加载完成: $category, ${localProjects.size} 个项目")
                    
                    projects = localProjects
                    lightweightAdapter.updateProjects(projects)
                    updateProjectCount(projects.size)
                    
                    // 显示本地项目加载完成的提示
                    if (localProjects.isNotEmpty()) {
                        Log.d(TAG, "✅ 本地项目已显示: $category")
                    } else {
                        Log.d(TAG, "⚠️ 没有本地项目: $category")
                    }
                }
                
                override fun onNetworkProjectsLoaded(networkProjects: List<LightweightResourceValidator.LightweightProject>) {
                    // 网络项目加载完成，插入到顶部
                    if (!isAdded || _binding == null) {
                        Log.w(TAG, "网络项目加载完成时Fragment已不活跃: $category")
                        return
                    }
                    
                    Log.d(TAG, "网络项目加载完成: $category, ${networkProjects.size} 个项目")
                    
                    if (networkProjects.isNotEmpty()) {
                        // 将网络项目添加到项目列表顶部
                        projects = networkProjects + projects
                        
                        // 使用适配器的插入方法，实现平滑动画
                        lightweightAdapter.insertProjectsAtTop(networkProjects)
                        updateProjectCount(projects.size)
                        
                        Log.d(TAG, "✅ 网络项目已插入顶部: $category, 新增 ${networkProjects.size} 个项目")
                        
                        // 显示网络项目加载完成的提示
                        if (isAdded) {
                            Toast.makeText(requireContext(), 
                                "新增 ${networkProjects.size} 个在线项目", 
                                Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        Log.d(TAG, "没有新的网络项目: $category")
                    }
                }
                
                override fun onLoadError(error: String, isNetworkError: Boolean) {
                    if (!isAdded) return
                    
                    Log.e(TAG, "加载错误: $category, 网络错误: $isNetworkError, 错误: $error")
                    
                    val errorType = if (isNetworkError) "网络项目" else "本地项目"
                    if (isNetworkError) {
                        // 网络错误不影响用户体验，只记录日志
                        Log.w(TAG, "网络项目加载失败，但本地项目正常显示: $category")
                    } else {
                        // 本地项目加载失败是严重问题
                        Toast.makeText(requireContext(), 
                            "${errorType}加载失败: $error", 
                            Toast.LENGTH_SHORT).show()
                    }
                }
                
                override fun onLoadingStateChanged(isLoading: Boolean, message: String) {
                    if (!isAdded || _binding == null) return
                    
                    Log.d(TAG, "加载状态变化: $category, 加载中: $isLoading, 消息: $message")
                    
                    // 更新加载状态UI
                    _binding?.progressBar?.visibility = if (isLoading && !forceRefresh) View.VISIBLE else View.GONE
                    _binding?.swipeRefreshProjects?.isRefreshing = forceRefresh && isLoading
                }
            },
            coroutineScope = lifecycleScope
        )
    }
    
    private fun convertAndStartProject(
        project: LightweightResourceValidator.LightweightProject,
        imageView: android.widget.ImageView
    ) {
        // 转换为HybridProject并启动
        // 保持原始项目ID格式，确保保存/加载进度时路径一致
        val hybridProject = HybridResourceManager.HybridProject(
            id = project.id, // 使用原始ID，不包含路径
            name = project.id,
            displayName = project.displayName,
            description = project.description ?: "",
            category = project.category ?: "other",
            difficulty = project.difficulty,
            totalRegions = 0,
            totalColors = 0,
            estimatedTime = 0,
            thumbnailUrl = null,
            previewUrl = null,
            resourceType = HybridResourceManager.Companion.ResourceType.LOCAL_ASSET,
            resourceSource = project.resourceSource,
            version = "1.0",
            fileSize = project.estimatedFileSize,
            isDownloaded = true,
            isBuiltIn = project.resourceSource == HybridResourceManager.Companion.ResourceSource.BUILT_IN,
            downloadProgress = 100f,
            tags = emptyList(),
            releaseDate = null,
            popularity = 0,
            rating = 0f
        )

        onProjectSelected?.invoke(hybridProject, imageView)
    }
    
    private fun selectRandomProject() {
        if (projects.isNotEmpty()) {
            val randomProject = projects.random()
            val tempImageView = android.widget.ImageView(requireContext())
            convertAndStartProject(randomProject, tempImageView)
        } else {
            Toast.makeText(requireContext(), "没有可用的项目", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun updateProjectCount(count: Int) {
        _binding?.tvProjectCount?.text = "共 $count 个项目"
    }

    /**
     * 刷新项目数据（用于缩略图更新）
     */
    fun refreshProjects() {
        // 检查Fragment是否仍然活跃
        if (!isAdded || isDetached || _binding == null) {
            Log.w(TAG, "Fragment不活跃，跳过刷新: $category")
            return
        }

        Log.d(TAG, "刷新分类 $category 的项目数据")
        loadProjects(forceRefresh = true)
    }

    override fun onResume() {
        super.onResume()

        // 强制重新注册Library监听器，确保Fragment缓存时监听器有效
        Log.d(TAG, "🔧 onResume时重新注册Library监听器: $category")
        Log.d(TAG, "📋 注册前监听器数量: ${LibraryEventManager.getListenerCount()}")
        LibraryEventManager.unregisterListener(this)
        LibraryEventManager.registerListener(this)
        Log.d(TAG, "✅ onResume后监听器数量: ${LibraryEventManager.getListenerCount()}")
    }

    override fun onDestroyView() {
        super.onDestroyView()

        // 不要在onDestroyView时取消注册监听器
        // Fragment可能只是被隐藏，而不是真正销毁
        Log.d(TAG, "🔧 onDestroyView: 保留监听器注册状态: $category")

        _binding = null
    }

    override fun onDestroy() {
        super.onDestroy()

        // 只在Fragment真正销毁时才取消注册监听器
        Log.d(TAG, "🔧 onDestroy: 取消注册Library监听器: $category")
        LibraryEventManager.unregisterListener(this)
    }

    // ========== LibraryRefreshListener 实现 ==========

    override fun onProjectProgressUpdated(projectId: String, hasProgress: Boolean, progressPercentage: Int) {
        Log.d(TAG, "🔔 收到项目进度更新通知: $projectId, 进度: ${progressPercentage}%, 分类: $category")

        // 在主线程中更新UI
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    Log.d(TAG, "🔄 开始刷新项目进度显示: $projectId")
                    lightweightAdapter.refreshProject(projectId)
                    Log.d(TAG, "✅ 项目进度显示刷新完成: $projectId")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 更新项目进度显示失败: $projectId", e)
                }
            }
        } else {
            Log.w(TAG, "⚠️ Fragment状态不允许更新UI: isAdded=$isAdded, view=${view != null}")
        }
    }

    override fun onProjectPreviewUpdated(projectId: String, previewImagePath: String?) {
        Log.d(TAG, "🖼️ 收到项目预览图更新通知: $projectId, 路径: $previewImagePath, 分类: $category")

        // 在主线程中更新UI
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    Log.d(TAG, "🔄 开始刷新项目缩略图: $projectId")
                    lightweightAdapter.refreshProjectThumbnail(projectId)
                    Log.d(TAG, "✅ 项目缩略图刷新完成: $projectId")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 更新项目缩略图失败: $projectId", e)
                }
            }
        } else {
            Log.w(TAG, "⚠️ Fragment状态不允许更新UI: isAdded=$isAdded, view=${view != null}")
        }
    }

    override fun onProjectCompleted(projectId: String) {
        Log.d(TAG, "🎉 收到项目完成通知: $projectId, 分类: $category")

        // 项目完成时刷新进度和预览图
        if (isAdded && view != null) {
            lifecycleScope.launch {
                try {
                    lightweightAdapter.refreshProject(projectId)
                    lightweightAdapter.refreshProjectThumbnail(projectId)
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 更新项目完成状态失败: $projectId", e)
                }
            }
        }
    }

    override fun refreshLibrary() {
        Log.d(TAG, "🔄 刷新Library: $category")
        loadProjects(forceRefresh = true)
    }
}
