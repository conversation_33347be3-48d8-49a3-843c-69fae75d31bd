package com.example.coloringproject.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.example.coloringproject.databinding.FragmentDailyChallengeBinding
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.coloringproject.R
import com.example.coloringproject.adapter.DailyChallengeAdapter
import com.example.coloringproject.utils.EnhancedAssetManager
import com.example.coloringproject.viewmodel.DailyChallengeViewModel
import com.example.coloringproject.network.ResourceDownloadManager
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * Daily Challenge Fragment - 每日推荐的填色项目
 * 提供每日挑战和特殊奖励系统
 */
class DailyChallengeFragment : Fragment() {
    
    private val viewModel: DailyChallengeViewModel by viewModels()
    private var _binding: FragmentDailyChallengeBinding? = null
    private val binding get() = _binding!!

    private lateinit var enhancedAssetManager: EnhancedAssetManager
    private lateinit var challengeHistoryAdapter: DailyChallengeAdapter
    private var downloadManager: ResourceDownloadManager? = null
    private var pendingDownloadManager: ResourceDownloadManager? = null

    // 今日挑战卡片

    // 挑战历史

    // 统计信息

    // 项目选择回调
    var onProjectSelected: ((EnhancedAssetManager.ValidatedProject) -> Unit)? = null

    /**
     * 设置下载管理器（延迟设置，等Fragment attach后再应用）
     */
    fun setDownloadManager(manager: ResourceDownloadManager) {
        // 总是先保存到pending，在onViewCreated中统一处理
        pendingDownloadManager = manager

        // 如果Fragment已经创建完成，立即应用
        if (isAdded && view != null) {
            applyPendingDownloadManager()
        }
    }

    /**
     * 应用延迟设置的DownloadManager
     */
    private fun applyPendingDownloadManager() {
        pendingDownloadManager?.let { manager ->
            downloadManager = manager
            viewModel.setDownloadManager(manager)
            pendingDownloadManager = null
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDailyChallengeBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 应用延迟设置的DownloadManager
        applyPendingDownloadManager()

        enhancedAssetManager = EnhancedAssetManager(requireContext())
        setupRecyclerView()
        setupObservers()
        setupTodayChallenge()

        loadDailyChallenges()
    }
    
    
    
    private fun setupRecyclerView() {
        challengeHistoryAdapter = DailyChallengeAdapter { challenge ->
            // 处理历史挑战点击
            Toast.makeText(requireContext(), "查看挑战: ${challenge.title}", Toast.LENGTH_SHORT).show()
        }
        
        binding.recyclerViewChallengeHistory.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = challengeHistoryAdapter
        }
    }
    
    private fun setupObservers() {
        viewModel.todayChallenge.observe(viewLifecycleOwner) { challenge ->
            challenge?.let {
                updateTodayChallengeUI(it)
            }
        }
        
        viewModel.challengeHistory.observe(viewLifecycleOwner) { history ->
            challengeHistoryAdapter.updateChallenges(history)
        }
        
        viewModel.userStats.observe(viewLifecycleOwner) { stats ->
            updateStatsUI(stats)
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // TODO: 显示/隐藏加载指示器
        }
        
        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                Toast.makeText(requireContext(), "加载失败: $it", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun setupTodayChallenge() {
        binding.btnStartTodayChallenge.setOnClickListener {
            val todayChallenge = viewModel.todayChallenge.value
            todayChallenge?.let { challenge ->
                // 启动今日挑战
                startChallenge(challenge)
            }
        }
    }
    
    private fun loadDailyChallenges() {
        lifecycleScope.launch {
            try {
                // 加载所有项目
                val result = enhancedAssetManager.getValidatedProjects()
                result.fold(
                    onSuccess = { projects ->
                        viewModel.initializeDailyChallenges(projects)
                    },
                    onFailure = { error ->
                        viewModel.setError(error.message ?: "未知错误")
                    }
                )
            } catch (e: Exception) {
                viewModel.setError(e.message ?: "未知错误")
            }
        }
    }
    
    private fun updateTodayChallengeUI(challenge: DailyChallengeViewModel.DailyChallenge) {
        binding.tvTodayChallengeTitle.text = challenge.title
        binding.tvTodayChallengeDescription.text = challenge.description
        
        // 更新进度
        val progressPercentage = (challenge.progress * 100).toInt()
        binding.progressTodayChallenge.progress = progressPercentage
        binding.tvTodayChallengeProgress.text = "$progressPercentage%"
        
        // 更新按钮状态
        when {
            challenge.isCompleted -> {
                binding.btnStartTodayChallenge.text = "已完成"
                binding.btnStartTodayChallenge.isEnabled = false
            }
            challenge.progress > 0 -> {
                binding.btnStartTodayChallenge.text = "继续挑战"
                binding.btnStartTodayChallenge.isEnabled = true
            }
            else -> {
                binding.btnStartTodayChallenge.text = "开始挑战"
                binding.btnStartTodayChallenge.isEnabled = true
            }
        }
        
        // TODO: 加载挑战图片
        // Glide.with(this).load(challenge.imageUrl).into(todayChallengeImage)
    }
    
    private fun updateStatsUI(stats: DailyChallengeViewModel.UserChallengeStats) {
        binding.tvStreakCount.text = "${stats.currentStreak}"
        binding.tvCompletedChallenges.text = "${stats.completedChallenges}"
        binding.tvTotalPoints.text = "${stats.totalPoints}"
    }
    
    private fun startChallenge(challenge: DailyChallengeViewModel.DailyChallenge) {
        challenge.project?.let { project ->
            onProjectSelected?.invoke(project)
            Toast.makeText(requireContext(), "开始今日挑战: ${challenge.title}", Toast.LENGTH_SHORT).show()
        }
    }
    
    companion object {
        fun newInstance(): DailyChallengeFragment {
            return DailyChallengeFragment()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
