package com.example.coloringproject.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.coloringproject.utils.ProjectSaveManager
import com.example.coloringproject.utils.ProjectProgress
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log

/**
 * MyGalleryViewModel - 管理我的图库页面的数据
 */
class MyGalleryViewModel : ViewModel() {
    
    private val _projects = MutableLiveData<List<ProjectProgress>>()
    val projects: LiveData<List<ProjectProgress>> = _projects
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    private val _selectedFilter = MutableLiveData<GalleryFilter>()
    val selectedFilter: LiveData<GalleryFilter> = _selectedFilter
    
    private var allProjects: List<ProjectProgress> = emptyList()
    private var saveManager: ProjectSaveManager? = null

    init {
        _selectedFilter.value = GalleryFilter.ALL
    }

    /**
     * 设置保存管理器
     */
    fun setSaveManager(manager: ProjectSaveManager) {
        saveManager = manager
    }
    
    /**
     * 设置项目列表
     */
    fun setProjects(projects: List<ProjectProgress>) {
        allProjects = projects
        applyFilter(_selectedFilter.value ?: GalleryFilter.ALL)
    }

    // 防抖机制：避免频繁刷新
    private var lastRefreshTime = 0L
    private val refreshDebounceTime = 1000L // 1秒防抖

    /**
     * 刷新项目进度状态（带防抖机制）
     */
    fun refreshProjectProgress() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastRefreshTime < refreshDebounceTime) {
            Log.d("MyGalleryViewModel", "刷新请求被防抖机制拦截")
            return
        }
        lastRefreshTime = currentTime

        viewModelScope.launch(Dispatchers.IO) {
            try {
                withContext(Dispatchers.Main) {
                    _isLoading.value = true
                }

                val manager = saveManager
                if (manager != null) {
                    // 在IO线程中执行文件操作
                    val savedProjects = manager.getAllSavedProjects()

                    // 在IO线程中处理数据
                    val updatedProjects = allProjects.map { project ->
                        val savedProject = savedProjects.find { it.projectName == project.projectName }
                        if (savedProject != null) {
                            project.copy(
                                progressPercentage = savedProject.progressPercentage,
                                isCompleted = savedProject.isCompleted,
                                lastModified = savedProject.lastModified,
                                filledRegions = savedProject.filledRegions,
                                totalRegions = savedProject.totalRegions,
                                previewImagePath = savedProject.previewImagePath
                            )
                        } else {
                            project
                        }
                    }

                    // 切换到主线程更新UI
                    withContext(Dispatchers.Main) {
                        allProjects = updatedProjects
                        applyFilter(_selectedFilter.value ?: GalleryFilter.ALL)
                        _isLoading.value = false
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        _isLoading.value = false
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    _isLoading.value = false
                    setError("刷新进度失败: ${e.message}")
                }
                Log.e("MyGalleryViewModel", "刷新项目进度失败", e)
            }
        }
    }

    /**
     * 更新单个项目的进度
     */
    fun updateProjectProgress(projectName: String, progressPercentage: Int, isCompleted: Boolean) {
        allProjects = allProjects.map { project ->
            if (project.projectName == projectName) {
                project.copy(
                    progressPercentage = progressPercentage,
                    isCompleted = isCompleted,
                    lastModified = System.currentTimeMillis(),
                    filledRegions = project.filledRegions,
                    totalRegions = project.totalRegions,
                    previewImagePath = project.previewImagePath
                )
            } else {
                project
            }
        }
        applyFilter(_selectedFilter.value ?: GalleryFilter.ALL)
    }

    /**
     * 获取所有项目（未筛选）
     */
    fun getAllProjects(): List<ProjectProgress> {
        return allProjects
    }
    
    /**
     * 设置加载状态
     */
    fun setLoading(isLoading: Boolean) {
        _isLoading.value = isLoading
    }
    
    /**
     * 设置错误信息
     */
    fun setError(error: String?) {
        _error.value = error
        _isLoading.value = false
    }
    
    /**
     * 应用筛选器
     */
    fun applyFilter(filter: GalleryFilter) {
        _selectedFilter.value = filter
        
        val filteredProjects = when (filter) {
            GalleryFilter.ALL -> allProjects
            GalleryFilter.IN_PROGRESS -> allProjects.filter { !it.isCompleted }
            GalleryFilter.COMPLETED -> allProjects.filter { it.isCompleted }
            GalleryFilter.RECENT -> allProjects.sortedByDescending { it.lastModified }
        }
        
        _projects.value = filteredProjects
    }
    
    /**
     * 删除项目
     */
    fun deleteProject(project: ProjectProgress) {
        allProjects = allProjects.filter { it.projectName != project.projectName }
        applyFilter(_selectedFilter.value ?: GalleryFilter.ALL)
    }
    
    /**
     * 获取图库统计信息
     */
    fun getGalleryStats(): GalleryStats {
        val totalProjects = allProjects.size
        val completedProjects = allProjects.count { it.isCompleted }
        val inProgressProjects = totalProjects - completedProjects
        
        val totalProgress = if (allProjects.isNotEmpty()) {
            allProjects.map { it.progressPercentage }.average()
        } else {
            0.0
        }
        
        val recentActivity = allProjects.maxByOrNull { it.lastModified }
        
        return GalleryStats(
            totalProjects = totalProjects,
            completedProjects = completedProjects,
            inProgressProjects = inProgressProjects,
            averageProgress = totalProgress.toInt(),
            lastActivity = recentActivity?.lastModified ?: 0L
        )
    }
    
    /**
     * 搜索项目
     */
    fun searchProjects(query: String) {
        if (query.isEmpty()) {
            applyFilter(_selectedFilter.value ?: GalleryFilter.ALL)
            return
        }
        
        val searchResults = allProjects.filter { project ->
            project.projectName.lowercase().contains(query.lowercase())
        }
        
        _projects.value = searchResults
    }
    
    /**
     * 按难度筛选（暂时禁用，因为轻量级ProjectProgress不包含metadata）
     */
    fun filterByDifficulty(difficulty: String) {
        // 由于ProjectProgress不再包含coloringData，暂时返回所有项目
        // 如果需要按难度筛选，需要从完整数据中获取metadata
        _projects.value = allProjects
    }
    
    /**
     * 获取完成度分布
     */
    fun getProgressDistribution(): Map<String, Int> {
        val distribution = mutableMapOf<String, Int>()
        
        allProjects.forEach { project ->
            val progressRange = when (project.progressPercentage) {
                in 0..25 -> "0-25%"
                in 26..50 -> "26-50%"
                in 51..75 -> "51-75%"
                in 76..99 -> "76-99%"
                100 -> "100%"
                else -> "未知"
            }
            
            distribution[progressRange] = distribution.getOrDefault(progressRange, 0) + 1
        }
        
        return distribution
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = null
    }
}

/**
 * 图库筛选器
 */
enum class GalleryFilter {
    ALL,         // 全部
    IN_PROGRESS, // 进行中
    COMPLETED,   // 已完成
    RECENT       // 最近修改
}

/**
 * 图库统计信息
 */
data class GalleryStats(
    val totalProjects: Int,
    val completedProjects: Int,
    val inProgressProjects: Int,
    val averageProgress: Int,
    val lastActivity: Long
)
