package com.example.coloringproject.viewmodel

import android.app.Application
import android.graphics.Bitmap
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.coloringproject.data.*
import com.example.coloringproject.utils.SimpleAssetManager
import com.example.coloringproject.utils.ColoringProject
import com.example.coloringproject.utils.LoadingState
import com.example.coloringproject.utils.toLoadingState
import com.example.coloringproject.utils.EnhancedAssetManager
import kotlinx.coroutines.launch

/**
 * 填色应用的ViewModel
 */
class ColoringViewModel(application: Application) : AndroidViewModel(application) {

    private val assetManager = SimpleAssetManager(application)

    // 可用项目列表
    private val _projects = MutableLiveData<LoadingState<List<ColoringProject>>>()
    val projects: LiveData<LoadingState<List<ColoringProject>>> = _projects

    // 当前项目
    private val _currentProject = MutableLiveData<ColoringProject?>()
    val currentProject: LiveData<ColoringProject?> = _currentProject

    // 填色数据
    private val _coloringData = MutableLiveData<LoadingState<ColoringData>>()
    val coloringData: LiveData<LoadingState<ColoringData>> = _coloringData

    // 线稿图片
    private val _outlineBitmap = MutableLiveData<LoadingState<Bitmap>>()
    val outlineBitmap: LiveData<LoadingState<Bitmap>> = _outlineBitmap

    // 当前选中的颜色
    private val _selectedColor = MutableLiveData<ColorPalette?>()
    val selectedColor: LiveData<ColorPalette?> = _selectedColor

    // 填色进度
    private val _progress = MutableLiveData<ColoringProgress>()
    val progress: LiveData<ColoringProgress> = _progress

    // 填色状态
    private val _coloringState = MutableLiveData<ColoringState>(ColoringState.LOADING)
    val coloringState: LiveData<ColoringState> = _coloringState

    // 触摸结果
    private val _touchResult = MutableLiveData<TouchResult>()
    val touchResult: LiveData<TouchResult> = _touchResult

    init {
        Log.d("ColoringViewModel", "ViewModel initialized")
        loadAvailableProjects()
    }

    /**
     * 加载可用的填色项目
     */
    fun loadAvailableProjects() {
        Log.d("ColoringViewModel", "loadAvailableProjects started")
        _projects.value = LoadingState.Loading
        viewModelScope.launch {
            try {
                Log.d("ColoringViewModel", "Calling assetManager.getAvailableProjects()")
                val result = assetManager.getAvailableProjects()
                Log.d("ColoringViewModel", "getAvailableProjects result: $result")
                _projects.value = result.toLoadingState()
            } catch (e: Exception) {
                Log.e("ColoringViewModel", "Error in loadAvailableProjects", e)
                _projects.value = LoadingState.Error(e)
            }
        }
    }

    /**
     * 选择填色项目
     */
    fun selectProject(project: ColoringProject) {
        _currentProject.value = project
        _coloringState.value = ColoringState.LOADING

        // 重置状态
        _selectedColor.value = null
        _progress.value = ColoringProgress(project.totalRegions)

        // 加载项目数据
        loadProjectData(project)
    }

    /**
     * 从图库加载项目（EnhancedAssetManager.ValidatedProject）
     */
    fun loadProject(validatedProject: EnhancedAssetManager.ValidatedProject) {
        Log.d("ColoringViewModel", "Loading project from gallery: ${validatedProject.name}")

        // 转换为ColoringProject格式
        val coloringProject = ColoringProject(
            id = validatedProject.id,
            name = validatedProject.name,
            jsonFile = validatedProject.jsonFile,
            outlineFile = validatedProject.outlineFile,
            difficulty = validatedProject.difficulty,
            totalRegions = validatedProject.totalRegions,
            totalColors = validatedProject.totalColors,
            estimatedTime = validatedProject.estimatedTime
        )

        // 使用现有的selectProject方法
        selectProject(coloringProject)
    }

    /**
     * 加载项目数据
     */
    private fun loadProjectData(project: ColoringProject) {
        viewModelScope.launch {
            // 并行加载JSON数据和图片
            val dataResult = assetManager.loadColoringData(project.jsonFile)
            val bitmapResult = assetManager.loadOutlineBitmap(project.outlineFile)

            _coloringData.value = dataResult.toLoadingState()
            _outlineBitmap.value = bitmapResult.toLoadingState()

            // 检查是否都加载成功
            if (dataResult.isSuccess && bitmapResult.isSuccess) {
                _coloringState.value = ColoringState.READY
                
                // 自动选择第一个颜色
                val firstColor = dataResult.getOrNull()?.colorPalette?.firstOrNull()
                if (firstColor != null) {
                    selectColor(firstColor)
                }
            } else {
                _coloringState.value = ColoringState.ERROR
            }
        }
    }

    /**
     * 选择颜色
     */
    fun selectColor(color: ColorPalette) {
        _selectedColor.value = color
        if (_coloringState.value == ColoringState.READY) {
            _coloringState.value = ColoringState.COLORING
        }
    }

    /**
     * 处理区域触摸
     */
    fun onRegionTouched(result: TouchResult) {
        _touchResult.value = result
        
        if (result.isValidTouch) {
            // 更新进度
            val currentProgress = _progress.value ?: return
            val newFilledRegions = currentProgress.filledRegions + (result.regionId ?: return)
            
            val newProgress = currentProgress.copy(
                filledRegions = newFilledRegions,
                isCompleted = newFilledRegions.size >= currentProgress.totalRegions
            )
            
            _progress.value = newProgress
            
            // 检查是否完成
            if (newProgress.isCompleted) {
                _coloringState.value = ColoringState.COMPLETED
            }
        }
    }

    /**
     * 重置当前项目
     */
    fun resetCurrentProject() {
        val project = _currentProject.value ?: return
        _progress.value = ColoringProgress(project.totalRegions)
        _coloringState.value = ColoringState.READY
        
        // 重新选择第一个颜色
        val firstColor = _coloringData.value?.let { state ->
            if (state is LoadingState.Success) {
                state.data.colorPalette.firstOrNull()
            } else null
        }
        
        if (firstColor != null) {
            selectColor(firstColor)
        }
    }

    /**
     * 获取下一个推荐颜色
     */
    fun getNextRecommendedColor(): ColorPalette? {
        val data = _coloringData.value
        val currentColor = _selectedColor.value
        
        if (data is LoadingState.Success && currentColor != null) {
            val colors = data.data.colorPalette
            val currentIndex = colors.indexOf(currentColor)

            // 返回下一个颜色，如果是最后一个则返回第一个
            return if (currentIndex != -1 && currentIndex < colors.size - 1) {
                colors[currentIndex + 1]
            } else {
                colors.firstOrNull()
            }
        }
        
        return null
    }

    /**
     * 获取填色统计信息
     */
    fun getColoringStats(): ColoringStats? {
        val progress = _progress.value ?: return null
        val project = _currentProject.value ?: return null
        
        return ColoringStats(
            totalRegions = progress.totalRegions,
            filledRegions = progress.filledRegions.size,
            progressPercentage = progress.progressPercentage,
            estimatedTimeRemaining = calculateRemainingTime(progress, project.estimatedTime),
            isCompleted = progress.isCompleted
        )
    }

    /**
     * 计算剩余时间
     */
    private fun calculateRemainingTime(progress: ColoringProgress, totalEstimatedTime: Int): Int {
        if (progress.totalRegions == 0) return 0
        
        val completionRatio = progress.progress
        return if (completionRatio > 0) {
            ((1 - completionRatio) * totalEstimatedTime).toInt()
        } else {
            totalEstimatedTime
        }
    }
}

/**
 * 填色统计信息
 */
data class ColoringStats(
    val totalRegions: Int,
    val filledRegions: Int,
    val progressPercentage: Int,
    val estimatedTimeRemaining: Int,
    val isCompleted: Boolean
)
