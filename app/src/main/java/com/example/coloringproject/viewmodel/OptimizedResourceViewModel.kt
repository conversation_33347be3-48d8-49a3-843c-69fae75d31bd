package com.example.coloringproject.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.OptimizedResourceLoader
import com.example.coloringproject.utils.ThumbnailManager
import com.example.coloringproject.utils.OptimizedCacheStats
import kotlinx.coroutines.launch

/**
 * 优化的资源视图模型
 * 支持图片优先加载和JSON延迟加载策略
 */
class OptimizedResourceViewModel(application: Application) : AndroidViewModel(application) {
    
    private val optimizedLoader = OptimizedResourceLoader(application)
    private val hybridResourceManager = HybridResourceManager(application)
    private val thumbnailManager = ThumbnailManager(application)
    
    // 项目列表
    private val _projects = MutableLiveData<List<HybridResourceManager.HybridProject>>()
    val projects: LiveData<List<HybridResourceManager.HybridProject>> = _projects
    
    // 缩略图加载状态
    private val _thumbnailLoadingStates = MutableLiveData<Map<String, Boolean>>()
    val thumbnailLoadingStates: LiveData<Map<String, Boolean>> = _thumbnailLoadingStates
    
    // 数据加载状态
    private val _dataLoadingStates = MutableLiveData<Map<String, Boolean>>()
    val dataLoadingStates: LiveData<Map<String, Boolean>> = _dataLoadingStates
    
    // 加载进度
    private val _loadingProgress = MutableLiveData<LoadingProgress>()
    val loadingProgress: LiveData<LoadingProgress> = _loadingProgress
    
    // 错误信息
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    // 缓存统计
    private val _cacheStats = MutableLiveData<OptimizedCacheStats>()
    val cacheStats: LiveData<OptimizedCacheStats> = _cacheStats
    
    /**
     * 加载项目列表（仅缩略图）
     */
    fun loadProjectsWithThumbnails(
        includeRemote: Boolean = true,
        forceRefresh: Boolean = false
    ) {
        viewModelScope.launch {
            try {
                _loadingProgress.value = LoadingProgress(0, 0, "正在获取项目列表...")
                
                // 1. 获取项目列表
                val result = hybridResourceManager.getAllAvailableProjects(includeRemote, forceRefresh)
                if (result.isFailure) {
                    _errorMessage.value = "获取项目列表失败: ${result.exceptionOrNull()?.message}"
                    return@launch
                }
                
                val projectList = result.getOrThrow()
                _projects.value = projectList
                
                // 2. 预加载缩略图
                if (projectList.isNotEmpty()) {
                    preloadThumbnails(projectList)
                }
                
            } catch (e: Exception) {
                _errorMessage.value = "加载项目失败: ${e.message}"
            }
        }
    }
    
    /**
     * 预加载缩略图
     */
    private suspend fun preloadThumbnails(projects: List<HybridResourceManager.HybridProject>) {
        _loadingProgress.value = LoadingProgress(0, projects.size, "正在加载缩略图...")
        
        val result = optimizedLoader.preloadThumbnails(projects) { current, total ->
            _loadingProgress.value = LoadingProgress(current, total, "正在加载缩略图... ($current/$total)")
        }
        
        if (result.isSuccess) {
            val loadedCount = result.getOrThrow()
            _loadingProgress.value = LoadingProgress(loadedCount, projects.size, "缩略图加载完成")
        } else {
            _errorMessage.value = "缩略图预加载失败: ${result.exceptionOrNull()?.message}"
        }
    }
    
    /**
     * 按需加载项目数据
     */
    fun loadProjectData(
        project: HybridResourceManager.HybridProject,
        onDataReady: (ColoringData, String?) -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            // 更新数据加载状态
            val currentStates = _dataLoadingStates.value?.toMutableMap() ?: mutableMapOf()
            currentStates[project.id] = true
            _dataLoadingStates.value = currentStates
            
            optimizedLoader.loadDataOnDemand(
                project = project,
                onDataReady = { result ->
                    // 更新加载状态
                    val updatedStates = _dataLoadingStates.value?.toMutableMap() ?: mutableMapOf()
                    updatedStates[project.id] = false
                    _dataLoadingStates.value = updatedStates
                    
                    onDataReady(result.coloringData, result.outlinePath)
                },
                onError = { error ->
                    // 更新加载状态
                    val updatedStates = _dataLoadingStates.value?.toMutableMap() ?: mutableMapOf()
                    updatedStates[project.id] = false
                    _dataLoadingStates.value = updatedStates
                    
                    onError(error.message)
                }
            )
        }
    }
    
    /**
     * 获取项目缩略图路径
     */
    fun getThumbnailPath(projectId: String): String? {
        return if (thumbnailManager.hasCachedThumbnail(projectId)) {
            thumbnailManager.getCachedThumbnailPath(projectId)
        } else {
            null
        }
    }
    
    /**
     * 检查项目是否有缓存的缩略图
     */
    fun hasThumbnail(projectId: String): Boolean {
        return thumbnailManager.hasCachedThumbnail(projectId)
    }
    
    /**
     * 获取项目加载状态
     */
    fun getProjectLoadingState(projectId: String): OptimizedResourceLoader.LoadingState {
        return optimizedLoader.getLoadingState(projectId)
    }
    
    /**
     * 刷新缓存统计
     */
    fun refreshCacheStats() {
        viewModelScope.launch {
            _cacheStats.value = optimizedLoader.getCacheStats()
        }
    }
    
    /**
     * 清理缓存
     */
    fun clearCache() {
        viewModelScope.launch {
            optimizedLoader.clearCache()
            refreshCacheStats()
        }
    }
    
    /**
     * 清理过期缩略图
     */
    fun cleanupExpiredThumbnails(onComplete: (Int) -> Unit = {}) {
        viewModelScope.launch {
            val deletedCount = thumbnailManager.cleanupExpiredThumbnails()
            refreshCacheStats()
            onComplete(deletedCount)
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 搜索项目
     */
    fun searchProjects(query: String) {
        val allProjects = _projects.value ?: return
        
        if (query.isBlank()) {
            _projects.value = allProjects
            return
        }
        
        val filteredProjects = allProjects.filter { project ->
            project.displayName.contains(query, ignoreCase = true) ||
            project.description.contains(query, ignoreCase = true) ||
            project.category.contains(query, ignoreCase = true) ||
            project.difficulty.contains(query, ignoreCase = true) ||
            project.tags.any { it.contains(query, ignoreCase = true) }
        }
        
        _projects.value = filteredProjects
    }
    
    /**
     * 按分类筛选项目
     */
    fun filterByCategory(category: String) {
        val allProjects = _projects.value ?: return
        
        if (category == "全部" || category.isBlank()) {
            _projects.value = allProjects
            return
        }
        
        val filteredProjects = allProjects.filter { project ->
            project.category.equals(category, ignoreCase = true)
        }
        
        _projects.value = filteredProjects
    }
    
    /**
     * 按难度筛选项目
     */
    fun filterByDifficulty(difficulty: String) {
        val allProjects = _projects.value ?: return
        
        if (difficulty == "全部" || difficulty.isBlank()) {
            _projects.value = allProjects
            return
        }
        
        val filteredProjects = allProjects.filter { project ->
            project.difficulty.equals(difficulty, ignoreCase = true)
        }
        
        _projects.value = filteredProjects
    }
    
    /**
     * 获取项目统计信息
     */
    fun getProjectStats(): OptimizedProjectStats {
        val allProjects = _projects.value ?: emptyList()
        
        return OptimizedProjectStats(
            totalProjects = allProjects.size,
            builtInProjects = allProjects.count { it.isBuiltIn },
            downloadedProjects = allProjects.count { it.isDownloaded && !it.isBuiltIn },
            remoteProjects = allProjects.count { !it.isDownloaded },
            categoriesCount = allProjects.map { it.category }.distinct().size,
            averageRating = if (allProjects.isNotEmpty()) {
                allProjects.map { it.rating }.average().toFloat()
            } else 0f
        )
    }
}

/**
 * 加载进度信息
 */
data class LoadingProgress(
    val current: Int,
    val total: Int,
    val message: String
) {
    val percentage: Int
        get() = if (total > 0) (current * 100 / total) else 0
}

/**
 * 优化资源管理器的项目统计信息
 */
data class OptimizedProjectStats(
    val totalProjects: Int,
    val builtInProjects: Int,
    val downloadedProjects: Int,
    val remoteProjects: Int,
    val categoriesCount: Int,
    val averageRating: Float
)
