package com.example.coloringproject.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.example.coloringproject.utils.EnhancedAssetManager

/**
 * NewProjectsViewModel - 管理新项目页面的数据
 */
class NewProjectsViewModel : ViewModel() {
    
    private val _projects = MutableLiveData<List<EnhancedAssetManager.ValidatedProject>>()
    val projects: LiveData<List<EnhancedAssetManager.ValidatedProject>> = _projects
    
    private val _filteredProjects = MutableLiveData<List<EnhancedAssetManager.ValidatedProject>>()
    val filteredProjects: LiveData<List<EnhancedAssetManager.ValidatedProject>> = _filteredProjects
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    private val _searchQuery = MutableLiveData<String>()
    val searchQuery: LiveData<String> = _searchQuery
    
    private val _selectedCategory = MutableLiveData<String>()
    val selectedCategory: LiveData<String> = _selectedCategory
    
    private var allProjects: List<EnhancedAssetManager.ValidatedProject> = emptyList()
    
    init {
        _selectedCategory.value = "全部"
        _searchQuery.value = ""
    }
    
    /**
     * 设置项目列表
     */
    fun setProjects(projects: List<EnhancedAssetManager.ValidatedProject>) {
        allProjects = projects
        _projects.value = projects
        applyFilters()
    }
    
    /**
     * 设置加载状态
     */
    fun setLoading(isLoading: Boolean) {
        _isLoading.value = isLoading
    }
    
    /**
     * 设置错误信息
     */
    fun setError(error: String?) {
        _error.value = error
        _isLoading.value = false
    }
    
    /**
     * 搜索项目
     */
    fun searchProjects(query: String) {
        _searchQuery.value = query
        applyFilters()
    }
    
    /**
     * 按分类筛选
     */
    fun filterByCategory(category: String) {
        _selectedCategory.value = category
        applyFilters()
    }
    
    /**
     * 应用筛选条件
     */
    private fun applyFilters() {
        val query = _searchQuery.value?.lowercase() ?: ""
        val category = _selectedCategory.value ?: "全部"
        
        var filtered = allProjects
        
        // 按分类筛选
        if (category != "全部") {
            filtered = when (category) {
                "简单" -> filtered.filter { it.difficulty == "easy" }
                "中等" -> filtered.filter { it.difficulty == "medium" }
                "困难" -> filtered.filter { it.difficulty == "hard" }
                "动物" -> filtered.filter { it.name.contains("Animals", ignoreCase = true) }
                "自然" -> filtered.filter { it.name.contains("nature", ignoreCase = true) }
                "人物" -> filtered.filter { it.name.contains("character", ignoreCase = true) }
                "风景" -> filtered.filter { it.name.contains("landscape", ignoreCase = true) }
                else -> filtered
            }
        }
        
        // 按搜索关键词筛选
        if (query.isNotEmpty()) {
            filtered = filtered.filter { project ->
                project.name.lowercase().contains(query) ||
                project.difficulty.lowercase().contains(query)
            }
        }
        
        _filteredProjects.value = filtered
    }
    
    /**
     * 获取项目统计信息
     */
    fun getProjectStats(): ProjectStats {
        val projects = allProjects
        
        return ProjectStats(
            totalProjects = projects.size,
            easyProjects = projects.count { it.difficulty == "easy" },
            mediumProjects = projects.count { it.difficulty == "medium" },
            hardProjects = projects.count { it.difficulty == "hard" },
            averageRegions = if (projects.isNotEmpty()) projects.map { it.totalRegions }.average().toInt() else 0,
            averageColors = if (projects.isNotEmpty()) projects.map { it.totalColors }.average().toInt() else 0
        )
    }
    
    /**
     * 获取随机项目
     */
    fun getRandomProject(): EnhancedAssetManager.ValidatedProject? {
        val availableProjects = _filteredProjects.value ?: allProjects
        return if (availableProjects.isNotEmpty()) {
            availableProjects.random()
        } else {
            null
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = null
    }
}

/**
 * 项目统计信息
 */
data class ProjectStats(
    val totalProjects: Int,
    val easyProjects: Int,
    val mediumProjects: Int,
    val hardProjects: Int,
    val averageRegions: Int,
    val averageColors: Int
)
