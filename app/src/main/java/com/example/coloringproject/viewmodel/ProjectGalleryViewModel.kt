package com.example.coloringproject.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.example.coloringproject.utils.EnhancedAssetManager

/**
 * 项目图库ViewModel
 */
class ProjectGalleryViewModel : ViewModel() {
    
    private val _projects = MutableLiveData<List<EnhancedAssetManager.ValidatedProject>>()
    val projects: LiveData<List<EnhancedAssetManager.ValidatedProject>> = _projects
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    private val _selectedCategory = MutableLiveData<ProjectCategory>()
    val selectedCategory: LiveData<ProjectCategory> = _selectedCategory
    
    init {
        _selectedCategory.value = ProjectCategory.ALL
        _isLoading.value = false
    }
    
    /**
     * 设置项目列表
     */
    fun setProjects(projects: List<EnhancedAssetManager.ValidatedProject>) {
        _projects.value = projects
        _isLoading.value = false
        _error.value = null
    }
    
    /**
     * 设置错误信息
     */
    fun setError(message: String) {
        _error.value = message
        _isLoading.value = false
    }
    
    /**
     * 设置加载状态
     */
    fun setLoading(loading: Boolean) {
        _isLoading.value = loading
        if (loading) {
            _error.value = null
        }
    }
    
    /**
     * 选择分类
     */
    fun selectCategory(category: ProjectCategory) {
        _selectedCategory.value = category
        filterProjects()
    }
    
    /**
     * 根据分类过滤项目
     */
    private fun filterProjects() {
        val allProjects = _projects.value ?: return
        val category = _selectedCategory.value ?: ProjectCategory.ALL
        
        val filteredProjects = when (category) {
            ProjectCategory.ALL -> allProjects
            ProjectCategory.EASY -> allProjects.filter { it.difficulty == "easy" }
            ProjectCategory.MEDIUM -> allProjects.filter { it.difficulty == "medium" }
            ProjectCategory.HARD -> allProjects.filter { it.difficulty == "hard" }
            ProjectCategory.ANIMALS -> allProjects.filter { it.name.contains("动物", ignoreCase = true) }
            ProjectCategory.NATURE -> allProjects.filter { it.name.contains("自然", ignoreCase = true) }
            ProjectCategory.CHARACTERS -> allProjects.filter { it.name.contains("人物", ignoreCase = true) }
        }
        
        _projects.value = filteredProjects
    }
    
    /**
     * 搜索项目
     */
    fun searchProjects(query: String) {
        val allProjects = _projects.value ?: return
        
        if (query.isBlank()) {
            filterProjects() // 重新应用分类过滤
            return
        }
        
        val searchResults = allProjects.filter { project ->
            project.name.contains(query, ignoreCase = true) ||
            project.difficulty.contains(query, ignoreCase = true)
        }
        
        _projects.value = searchResults
    }
    
    /**
     * 获取项目统计信息
     */
    fun getProjectStats(): ProjectStats {
        val projects = _projects.value ?: emptyList()
        
        return ProjectStats(
            totalProjects = projects.size,
            easyProjects = projects.count { it.difficulty == "easy" },
            mediumProjects = projects.count { it.difficulty == "medium" },
            hardProjects = projects.count { it.difficulty == "hard" },
            averageRegions = projects.map { it.totalRegions }.average().toInt(),
            averageColors = projects.map { it.totalColors }.average().toInt()
        )
    }
}

/**
 * 项目分类
 */
enum class ProjectCategory {
    ALL,        // 全部
    EASY,       // 简单
    MEDIUM,     // 中等
    HARD,       // 困难
    ANIMALS,    // 动物
    NATURE,     // 自然
    CHARACTERS  // 人物
}


