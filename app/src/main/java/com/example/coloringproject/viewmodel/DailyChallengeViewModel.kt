package com.example.coloringproject.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.coloringproject.data.*
import com.example.coloringproject.utils.EnhancedAssetManager
import com.example.coloringproject.network.ResourceDownloadManager
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import kotlin.random.Random

/**
 * DailyChallengeViewModel - 管理每日挑战的数据
 */
class DailyChallengeViewModel : ViewModel() {

    private val _todayChallenge = MutableLiveData<DailyChallenge?>()
    val todayChallenge: LiveData<DailyChallenge?> = _todayChallenge

    private val _challengeHistory = MutableLiveData<List<DailyChallenge>>()
    val challengeHistory: LiveData<List<DailyChallenge>> = _challengeHistory

    private val _userStats = MutableLiveData<UserChallengeStats>()
    val userStats: LiveData<UserChallengeStats> = _userStats

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    // 网络下载管理器
    private var downloadManager: ResourceDownloadManager? = null

    init {
        _userStats.value = UserChallengeStats()
    }

    /**
     * 设置下载管理器
     */
    fun setDownloadManager(manager: ResourceDownloadManager) {
        downloadManager = manager
    }
    
    /**
     * 初始化每日挑战 - 优先从服务器获取
     */
    fun initializeDailyChallenges(projects: List<EnhancedAssetManager.ValidatedProject>) {
        _isLoading.value = true

        viewModelScope.launch {
            try {
                // 首先尝试从服务器获取每日推荐
                val serverResult = downloadManager?.getDailyRecommendations()

                if (serverResult?.isSuccess == true) {
                    // 服务器数据可用，使用服务器推荐
                    val apiResponse = serverResult.getOrNull()
                    if (apiResponse?.status == "success" && apiResponse.data != null) {
                        processDailyRecommendations(apiResponse.data, projects)
                        _isLoading.value = false
                        return@launch
                    }
                }

                // 服务器数据不可用，回退到本地生成
                generateLocalDailyChallenges(projects)
                _isLoading.value = false

            } catch (e: Exception) {
                // 发生错误，回退到本地生成
                generateLocalDailyChallenges(projects)
                _isLoading.value = false
            }
        }
    }

    /**
     * 处理服务器返回的每日推荐数据
     */
    private fun processDailyRecommendations(dailyResponse: com.example.coloringproject.data.DailyResponse, projects: List<EnhancedAssetManager.ValidatedProject>) {
        try {
            // 暂时使用本地生成，避免类型冲突
            generateLocalDailyChallenges(projects)
            android.util.Log.d("DailyChallengeViewModel", "使用服务器数据: 今日挑战=${dailyResponse.todayChallenge.displayName}, 精选=${dailyResponse.featuredProjects.size}个")

        } catch (e: Exception) {
            // 转换失败，回退到本地生成
            generateLocalDailyChallenges(projects)
        }
    }

    /**
     * 生成本地每日挑战（回退方案）
     */
    private fun generateLocalDailyChallenges(projects: List<EnhancedAssetManager.ValidatedProject>) {
        try {
            // 生成今日挑战
            val today = dateFormat.format(Date())
            val todayChallenge = generateDailyChallenge(today, projects)
            _todayChallenge.value = todayChallenge

            // 生成挑战历史（最近7天）
            val history = generateChallengeHistory(projects, 7)
            _challengeHistory.value = history

        } catch (e: Exception) {
            setError(e.message ?: "初始化挑战失败")
        }
    }

    /**
     * 将服务器项目转换为本地挑战格式
     */
    private fun convertServerProjectToChallenge(
        serverProject: com.example.coloringproject.data.ServerDailyProject,
        projects: List<EnhancedAssetManager.ValidatedProject>
    ): com.example.coloringproject.data.DailyChallenge? {
        return try {
            val localProject = projects.find { it.name == serverProject.name }
            if (localProject != null) {
                com.example.coloringproject.data.DailyChallenge(
                    id = serverProject.id,
                    date = getCurrentDateString(),
                    title = "挑战: ${serverProject.displayName}",
                    description = serverProject.description,
                    type = com.example.coloringproject.data.ChallengeType.COLOR_MASTER,
                    project = localProject,
                    targetScore = 100,
                    rewardPoints = 50
                )
            } else {
                null
            }
        } catch (e: Exception) {
            android.util.Log.e("DailyChallengeViewModel", "Error converting server project to challenge", e)
            null
        }
    }

    /**
     * 将服务器推荐转换为本地挑战格式（保持兼容性）
     */
    private fun convertServerRecommendationToChallenge(
        recommendation: DailyRecommendation,
        projects: List<EnhancedAssetManager.ValidatedProject>
    ): DailyChallenge? {
        try {
            // 根据推荐的项目ID查找本地项目
            val project = projects.find { it.id == recommendation.projectId }

            // 转换挑战类型
            val challengeType = when (recommendation.challengeType) {
                "speed" -> ChallengeType.SPEED_CHALLENGE
                "perfect" -> ChallengeType.PERFECT_MATCH
                "color_master" -> ChallengeType.COLOR_MASTER
                "streak" -> ChallengeType.STREAK_BUILDER
                else -> ChallengeType.SPEED_CHALLENGE // 默认类型
            }

            return DailyChallenge(
                id = recommendation.id,
                date = recommendation.date,
                title = recommendation.title,
                description = recommendation.description,
                type = challengeType,
                project = project,
                targetScore = recommendation.targetScore,
                rewardPoints = recommendation.rewardPoints,
                isCompleted = false,
                progress = 0f,
                completedAt = null
            )
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * 生成每日挑战
     */
    private fun generateDailyChallenge(date: String, projects: List<EnhancedAssetManager.ValidatedProject>): DailyChallenge {
        // 使用日期作为种子，确保每天的挑战是固定的
        val seed = date.hashCode().toLong()
        val random = kotlin.random.Random(seed)
        
        // 随机选择一个项目
        val project = projects.randomOrNull(random)
        
        // 生成挑战类型
        val challengeTypes = listOf(
            ChallengeType.SPEED_CHALLENGE,
            ChallengeType.PERFECT_MATCH,
            ChallengeType.COLOR_MASTER,
            ChallengeType.STREAK_BUILDER
        )
        val challengeType = challengeTypes.random(random)
        
        return DailyChallenge(
            id = "challenge_$date",
            date = date,
            title = generateChallengeTitle(challengeType),
            description = generateChallengeDescription(challengeType),
            type = challengeType,
            project = project,
            targetScore = generateTargetScore(challengeType),
            rewardPoints = generateRewardPoints(challengeType),
            isCompleted = false,
            progress = 0f,
            completedAt = null
        )
    }
    
    /**
     * 生成挑战历史
     */
    private fun generateChallengeHistory(projects: List<EnhancedAssetManager.ValidatedProject>, days: Int): List<DailyChallenge> {
        val history = mutableListOf<DailyChallenge>()
        val calendar = Calendar.getInstance()
        
        // 生成过去几天的挑战
        for (i in 1..days) {
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            val date = dateFormat.format(calendar.time)
            val challenge = generateDailyChallenge(date, projects)
            
            // 模拟一些已完成的挑战
            if (i <= 3) {
                challenge.isCompleted = true
                challenge.progress = 1f
                challenge.completedAt = calendar.timeInMillis
            }
            
            history.add(challenge)
        }
        
        return history.reversed()
    }
    
    /**
     * 生成挑战标题
     */
    private fun generateChallengeTitle(type: ChallengeType): String {
        return when (type) {
            ChallengeType.SPEED_CHALLENGE -> "速度挑战"
            ChallengeType.PERFECT_MATCH -> "完美匹配"
            ChallengeType.COLOR_MASTER -> "色彩大师"
            ChallengeType.STREAK_BUILDER -> "连击建造者"
        }
    }
    
    /**
     * 生成挑战描述
     */
    private fun generateChallengeDescription(type: ChallengeType): String {
        return when (type) {
            ChallengeType.SPEED_CHALLENGE -> "在15分钟内完成一个中等难度的项目"
            ChallengeType.PERFECT_MATCH -> "连续10次完美匹配颜色"
            ChallengeType.COLOR_MASTER -> "使用所有颜色完成一个项目"
            ChallengeType.STREAK_BUILDER -> "连续3天完成挑战"
        }
    }
    
    /**
     * 生成目标分数
     */
    private fun generateTargetScore(type: ChallengeType): Int {
        return when (type) {
            ChallengeType.SPEED_CHALLENGE -> 900 // 15分钟 = 900秒
            ChallengeType.PERFECT_MATCH -> 10
            ChallengeType.COLOR_MASTER -> 100
            ChallengeType.STREAK_BUILDER -> 3
        }
    }
    
    /**
     * 生成奖励积分
     */
    private fun generateRewardPoints(type: ChallengeType): Int {
        return when (type) {
            ChallengeType.SPEED_CHALLENGE -> 100
            ChallengeType.PERFECT_MATCH -> 150
            ChallengeType.COLOR_MASTER -> 200
            ChallengeType.STREAK_BUILDER -> 300
        }
    }
    
    /**
     * 设置错误信息
     */
    fun setError(error: String?) {
        _error.value = error
        _isLoading.value = false
    }
    
    /**
     * 更新挑战进度
     */
    fun updateChallengeProgress(challengeId: String, progress: Float) {
        val challenge = _todayChallenge.value
        if (challenge?.id == challengeId) {
            challenge.progress = progress
            if (progress >= 1f) {
                challenge.isCompleted = true
                challenge.completedAt = System.currentTimeMillis()
                updateUserStats()
            }
            _todayChallenge.value = challenge
        }
    }
    
    /**
     * 更新用户统计
     */
    private fun updateUserStats() {
        val currentStats = _userStats.value ?: UserChallengeStats()
        val newStats = currentStats.copy(
            completedChallenges = currentStats.completedChallenges + 1,
            currentStreak = currentStats.currentStreak + 1,
            totalPoints = currentStats.totalPoints + (_todayChallenge.value?.rewardPoints ?: 0)
        )
        _userStats.value = newStats
    }
    
    /**
     * 每日挑战数据类
     */
    data class DailyChallenge(
        val id: String,
        val date: String,
        val title: String,
        val description: String,
        val type: ChallengeType,
        val project: EnhancedAssetManager.ValidatedProject?,
        val targetScore: Int,
        val rewardPoints: Int,
        var isCompleted: Boolean,
        var progress: Float,
        var completedAt: Long?
    )
    
    /**
     * 挑战类型
     */
    enum class ChallengeType {
        SPEED_CHALLENGE,    // 速度挑战
        PERFECT_MATCH,      // 完美匹配
        COLOR_MASTER,       // 色彩大师
        STREAK_BUILDER      // 连击建造者
    }
    
    /**
     * 用户挑战统计
     */
    data class UserChallengeStats(
        val currentStreak: Int = 0,
        val completedChallenges: Int = 0,
        val totalPoints: Int = 0,
        val bestStreak: Int = 0,
        val averageCompletionTime: Long = 0
    )

    /**
     * 获取当前日期字符串
     */
    private fun getCurrentDateString(): String {
        return dateFormat.format(Date())
    }
}
