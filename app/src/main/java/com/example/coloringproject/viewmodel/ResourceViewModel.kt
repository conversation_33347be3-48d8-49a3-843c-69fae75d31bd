package com.example.coloringproject.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.coloringproject.utils.HybridResourceManager
import com.example.coloringproject.utils.ResourceCacheManager
// import com.example.coloringproject.network.CategoryInfo // 暂时注释避免冲突
import com.example.coloringproject.data.DailyResponse
import com.example.coloringproject.network.ResourceDownloadManager
import kotlinx.coroutines.launch

/**
 * 资源管理ViewModel
 * 管理本地和远程资源的加载、下载、缓存等操作
 */
class ResourceViewModel(application: Application) : AndroidViewModel(application) {
    
    private val hybridResourceManager = HybridResourceManager(application)
    
    // 项目列表状态
    private val _projects = MutableLiveData<List<HybridResourceManager.HybridProject>>()
    val projects: LiveData<List<HybridResourceManager.HybridProject>> = _projects
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    // 下载进度
    private val _downloadProgress = MutableLiveData<Map<String, Float>>()
    val downloadProgress: LiveData<Map<String, Float>> = _downloadProgress
    
    // 缓存统计
    private val _cacheStats = MutableLiveData<com.example.coloringproject.utils.ResourceCacheStats>()
    val cacheStats: LiveData<com.example.coloringproject.utils.ResourceCacheStats> = _cacheStats

    // 分类列表 - 暂时注释避免编译错误
    // private val _categories = MutableLiveData<List<CategoryInfo>>()
    // val categories: LiveData<List<CategoryInfo>> = _categories

    // 每日推荐
    private val _dailyRecommendations = MutableLiveData<DailyResponse>()
    val dailyRecommendations: LiveData<DailyResponse> = _dailyRecommendations

    // 更新信息
    private val _updateInfo = MutableLiveData<ResourceDownloadManager.UpdateResponse>()
    val updateInfo: LiveData<ResourceDownloadManager.UpdateResponse> = _updateInfo

    // 当前下载的项目
    private val currentDownloads = mutableMapOf<String, Float>()
    
    init {
        loadProjects()
        loadCategories()
        loadDailyRecommendations()
        loadCacheStats()
    }
    
    /**
     * 加载所有项目
     */
    fun loadProjects(includeRemote: Boolean = true, forceRefresh: Boolean = false) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null
            
            try {
                val result = hybridResourceManager.getAllAvailableProjects(includeRemote, forceRefresh)
                if (result.isSuccess) {
                    _projects.value = result.getOrNull() ?: emptyList()
                } else {
                    _errorMessage.value = result.exceptionOrNull()?.message ?: "加载项目失败"
                }
            } catch (e: Exception) {
                _errorMessage.value = "加载项目时发生错误: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 按类别筛选项目
     */
    fun filterProjectsByCategory(category: String?) {
        val allProjects = _projects.value ?: return
        val filteredProjects = if (category.isNullOrEmpty()) {
            allProjects
        } else {
            allProjects.filter { it.category == category }
        }
        _projects.value = filteredProjects
    }
    
    /**
     * 按资源来源筛选项目
     */
    fun filterProjectsBySource(source: HybridResourceManager.Companion.ResourceSource?) {
        val allProjects = _projects.value ?: return
        val filteredProjects = if (source == null) {
            allProjects
        } else {
            allProjects.filter { it.resourceSource == source }
        }
        _projects.value = filteredProjects
    }
    
    /**
     * 搜索项目
     */
    fun searchProjects(query: String) {
        val allProjects = _projects.value ?: return
        val searchResults = if (query.isBlank()) {
            allProjects
        } else {
            allProjects.filter { project ->
                project.name.contains(query, ignoreCase = true) ||
                project.displayName.contains(query, ignoreCase = true) ||
                project.description.contains(query, ignoreCase = true) ||
                project.tags.any { it.contains(query, ignoreCase = true) }
            }
        }
        _projects.value = searchResults
    }
    
    /**
     * 加载项目资源
     */
    fun loadProjectResource(
        projectId: String,
        onSuccess: (HybridResourceManager.ResourceLoadResult.Success) -> Unit,
        onRequiresDownload: (HybridResourceManager.ResourceLoadResult.RequiresDownload) -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            try {
                when (val result = hybridResourceManager.loadProjectResource(projectId)) {
                    is HybridResourceManager.ResourceLoadResult.Success -> {
                        onSuccess(result)
                    }
                    is HybridResourceManager.ResourceLoadResult.RequiresDownload -> {
                        onRequiresDownload(result)
                    }
                    is HybridResourceManager.ResourceLoadResult.Error -> {
                        onError(result.message)
                    }
                }
            } catch (e: Exception) {
                onError("加载项目资源失败: ${e.message}")
            }
        }
    }
    
    /**
     * 下载项目
     */
    fun downloadProject(
        projectId: String,
        onComplete: (Boolean) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                val result = hybridResourceManager.downloadProject(projectId) { progress ->
                    // 更新下载进度
                    currentDownloads[projectId] = progress
                    _downloadProgress.value = currentDownloads.toMap()
                }
                
                // 移除下载进度
                currentDownloads.remove(projectId)
                _downloadProgress.value = currentDownloads.toMap()
                
                if (result.isSuccess) {
                    // 刷新项目列表
                    loadProjects(includeRemote = false)
                    loadCacheStats()
                    onComplete(true)
                } else {
                    _errorMessage.value = "下载失败: ${result.exceptionOrNull()?.message}"
                    onComplete(false)
                }
            } catch (e: Exception) {
                currentDownloads.remove(projectId)
                _downloadProgress.value = currentDownloads.toMap()
                _errorMessage.value = "下载时发生错误: ${e.message}"
                onComplete(false)
            }
        }
    }
    
    /**
     * 删除已下载的项目
     */
    fun deleteDownloadedProject(
        projectId: String,
        onComplete: (Boolean) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                val result = hybridResourceManager.deleteDownloadedProject(projectId)
                if (result.isSuccess) {
                    // 刷新项目列表
                    loadProjects(includeRemote = false)
                    loadCacheStats()
                    onComplete(true)
                } else {
                    _errorMessage.value = "删除失败: ${result.exceptionOrNull()?.message}"
                    onComplete(false)
                }
            } catch (e: Exception) {
                _errorMessage.value = "删除时发生错误: ${e.message}"
                onComplete(false)
            }
        }
    }
    
    /**
     * 加载分类列表
     */
    fun loadCategories() {
        viewModelScope.launch {
            try {
                val result = hybridResourceManager.getCategories()
                if (result.isSuccess) {
                    // _categories.value = result.getOrNull() ?: emptyList() // 暂时注释避免编译错误
                } else {
//                    _errorMessage.value = "加载分类失败: ${result.exceptionOrNull()?.message}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "加载分类时发生错误: ${e.message}"
            }
        }
    }

    /**
     * 加载每日推荐
     */
    fun loadDailyRecommendations() {
        viewModelScope.launch {
            try {
                val result = hybridResourceManager.getDailyRecommendations()
                if (result.isSuccess) {
                    _dailyRecommendations.value = result.getOrNull()
                } else {
//                    _errorMessage.value = "加载每日推荐失败: ${result.exceptionOrNull()?.message}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "加载每日推荐时发生错误: ${e.message}"
            }
        }
    }

    /**
     * 检查更新
     */
    fun checkForUpdates(clientVersion: String = "1.0.0") {
        viewModelScope.launch {
            try {
                val result = hybridResourceManager.checkForUpdates(clientVersion)
                if (result.isSuccess) {
                    _updateInfo.value = result.getOrNull()
                } else {
                    _errorMessage.value = "检查更新失败: ${result.exceptionOrNull()?.message}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "检查更新时发生错误: ${e.message}"
            }
        }
    }
    
    /**
     * 清理过期缓存
     */
    fun cleanupExpiredCache(onComplete: (Int) -> Unit = {}) {
        viewModelScope.launch {
            try {
                val cacheManager = ResourceCacheManager(getApplication())
                val cleanedCount = cacheManager.cleanupExpiredCache()
                loadCacheStats()
                onComplete(cleanedCount)
            } catch (e: Exception) {
                _errorMessage.value = "清理缓存时发生错误: ${e.message}"
                onComplete(0)
            }
        }
    }
    
    /**
     * 加载缓存统计
     */
    private fun loadCacheStats() {
        viewModelScope.launch {
            try {
                val cacheManager = ResourceCacheManager(getApplication())
                val stats = cacheManager.getCacheStats()
                _cacheStats.value = stats
            } catch (e: Exception) {
                // 忽略缓存统计错误
            }
        }
    }
    
    /**
     * 获取项目信息
     */
    fun getProjectInfo(projectId: String): HybridResourceManager.HybridProject? {
        return _projects.value?.find { it.id == projectId }
    }
    
    /**
     * 获取下载进度
     */
    fun getDownloadProgress(projectId: String): Float {
        return currentDownloads[projectId] ?: 0f
    }
    
    /**
     * 是否正在下载
     */
    fun isDownloading(projectId: String): Boolean {
        return currentDownloads.containsKey(projectId)
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 获取分类列表
     */
    fun getCategories(): List<String> {
        val allProjects = _projects.value ?: return emptyList()
        return allProjects.map { it.category }.distinct().sorted()
    }
    
    /**
     * 获取统计信息
     */
    fun getProjectStats(): ResourceStats {
        val allProjects = _projects.value ?: emptyList()
        return ResourceStats(
            totalProjects = allProjects.size,
            builtInProjects = allProjects.count { it.isBuiltIn },
            downloadedProjects = allProjects.count { it.isDownloaded && !it.isBuiltIn },
            remoteProjects = allProjects.count { !it.isDownloaded }
        )
    }
}

/**
 * 资源统计信息
 */
data class ResourceStats(
    val totalProjects: Int,
    val builtInProjects: Int,
    val downloadedProjects: Int,
    val remoteProjects: Int
)
