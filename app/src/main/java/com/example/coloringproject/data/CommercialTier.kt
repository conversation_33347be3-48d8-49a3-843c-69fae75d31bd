package com.example.coloringproject.data

import com.google.gson.annotations.SerializedName

/**
 * 商业化等级数据模型
 */
data class CommercialTier(
    @SerializedName("name")
    val name: String,
    
    @SerializedName("description")
    val description: String,
    
    @SerializedName("monetization")
    val monetization: String,
    
    @SerializedName("generator")
    val generator: String,
    
    @SerializedName("max_colors")
    val maxColors: Int,
    
    @SerializedName("min_region_size")
    val minRegionSize: Int,
    
    @SerializedName("max_regions")
    val maxRegions: Int,
    
    @SerializedName("target_time_minutes")
    val targetTimeMinutes: Int,
    
    @SerializedName("quality_features")
    val qualityFeatures: QualityFeatures,
    
    @SerializedName("user_experience")
    val userExperience: UserExperience
)

/**
 * 质量特性
 */
data class QualityFeatures(
    @SerializedName("edge_detection")
    val edgeDetection: Boolean = false,
    
    @SerializedName("boundary_alignment")
    val boundaryAlignment: Boolean = false,
    
    @SerializedName("small_region_merge")
    val smallRegionMerge: Boolean = true,
    
    @SerializedName("adaptive_algorithm")
    val adaptiveAlgorithm: Boolean = false,
    
    @SerializedName("texture_preservation")
    val texturePreservation: Boolean = false,
    
    @SerializedName("gradient_support")
    val gradientSupport: Boolean = false,
    
    @SerializedName("micro_detail_preservation")
    val microDetailPreservation: Boolean = false
)

/**
 * 用户体验特性
 */
data class UserExperience(
    @SerializedName("hints_enabled")
    val hintsEnabled: Boolean = true,
    
    @SerializedName("auto_progression")
    val autoProgression: Boolean = false,
    
    @SerializedName("save_to_gallery")
    val saveToGallery: Boolean = false,
    
    @SerializedName("watermark")
    val watermark: Boolean = true,
    
    @SerializedName("ai_suggestions")
    val aiSuggestions: Boolean = false,
    
    @SerializedName("style_recommendations")
    val styleRecommendations: Boolean = false,
    
    @SerializedName("export_high_res")
    val exportHighRes: Boolean = false,
    
    @SerializedName("custom_palettes")
    val customPalettes: Boolean = false
)

/**
 * 广告解锁选项
 */
data class AdUnlockOption(
    @SerializedName("name")
    val name: String,
    
    @SerializedName("description")
    val description: String,
    
    @SerializedName("unlock_duration_hours")
    val unlockDurationHours: Int,
    
    @SerializedName("unlocked_tier")
    val unlockedTier: String,
    
    @SerializedName("ad_duration_seconds")
    val adDurationSeconds: Int
)

/**
 * 商业化配置
 */
data class CommercialConfig(
    @SerializedName("commercial_tiers")
    val commercialTiers: Map<String, CommercialTier>,
    
    @SerializedName("ad_unlock_tiers")
    val adUnlockTiers: Map<String, AdUnlockOption>
)

/**
 * 用户商业化状态
 */
data class UserCommercialStatus(
    val currentTier: String = "free",
    val subscriptionType: SubscriptionType = SubscriptionType.NONE,
    val subscriptionExpiry: Long = 0L,
    val adUnlockExpiry: Long = 0L,
    val adUnlockedTier: String? = null,
    val totalAdsWatched: Int = 0,
    val premiumPurchases: Set<String> = emptySet()
) {
    /**
     * 获取当前有效的等级
     */
    fun getEffectiveTier(): String {
        val currentTime = System.currentTimeMillis()
        
        // 检查广告解锁是否有效
        if (adUnlockExpiry > currentTime && !adUnlockedTier.isNullOrEmpty()) {
            return adUnlockedTier
        }
        
        // 检查订阅是否有效
        if (subscriptionExpiry > currentTime) {
            return when (subscriptionType) {
                SubscriptionType.BASIC -> "basic"
                SubscriptionType.PREMIUM -> "premium"
                SubscriptionType.PROFESSIONAL -> "professional"
                else -> currentTier
            }
        }
        
        return currentTier
    }
    
    /**
     * 是否可以观看广告解锁
     */
    fun canWatchAdForUnlock(): Boolean {
        val currentTime = System.currentTimeMillis()
        return adUnlockExpiry <= currentTime
    }
    
    /**
     * 是否有有效订阅
     */
    fun hasActiveSubscription(): Boolean {
        return subscriptionExpiry > System.currentTimeMillis()
    }
    
    /**
     * 获取剩余订阅时间（小时）
     */
    fun getRemainingSubscriptionHours(): Long {
        val currentTime = System.currentTimeMillis()
        return if (subscriptionExpiry > currentTime) {
            (subscriptionExpiry - currentTime) / (1000 * 60 * 60)
        } else {
            0L
        }
    }
    
    /**
     * 获取剩余广告解锁时间（小时）
     */
    fun getRemainingAdUnlockHours(): Long {
        val currentTime = System.currentTimeMillis()
        return if (adUnlockExpiry > currentTime) {
            (adUnlockExpiry - currentTime) / (1000 * 60 * 60)
        } else {
            0L
        }
    }
}

/**
 * 订阅类型
 */
enum class SubscriptionType {
    NONE,
    BASIC,
    PREMIUM,
    PROFESSIONAL
}

/**
 * 商业化事件类型
 */
enum class CommercialEventType {
    AD_WATCHED,
    SUBSCRIPTION_PURCHASED,
    PREMIUM_CONTENT_UNLOCKED,
    TIER_UPGRADED,
    FEATURE_ACCESSED
}

/**
 * 商业化事件
 */
data class CommercialEvent(
    val type: CommercialEventType,
    val tier: String,
    val timestamp: Long = System.currentTimeMillis(),
    val metadata: Map<String, Any> = emptyMap()
)
