package com.example.coloringproject.data

import com.google.gson.*
import java.lang.reflect.Type

/**
 * 自定义的轮廓数据反序列化器
 * 处理不同格式的contour数据
 */
class ContourDeserializer : JsonDeserializer<List<List<Int>>?> {
    
    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): List<List<Int>>? {
        
        if (json == null || json.isJsonNull) {
            return null
        }
        
        try {
            if (json.isJsonArray) {
                val jsonArray = json.asJsonArray
                
                if (jsonArray.size() == 0) {
                    return emptyList()
                }
                
                // 检查第一个元素的类型
                val firstElement = jsonArray.get(0)
                
                if (firstElement.isJsonArray) {
                    // 标准格式：[[x1,y1], [x2,y2], ...]
                    val result = mutableListOf<List<Int>>()
                    
                    for (element in jsonArray) {
                        if (element.isJsonArray) {
                            val pointArray = element.asJsonArray
                            if (pointArray.size() >= 2) {
                                val point = listOf(
                                    pointArray.get(0).asInt,
                                    pointArray.get(1).asInt
                                )
                                result.add(point)
                            }
                        }
                    }
                    
                    return result
                    
                } else if (firstElement.isJsonPrimitive) {
                    // 扁平格式：[x1, y1, x2, y2, ...]
                    val flatArray = mutableListOf<Int>()
                    for (element in jsonArray) {
                        if (element.isJsonPrimitive) {
                            flatArray.add(element.asInt)
                        }
                    }
                    
                    // 转换为点对格式
                    val result = mutableListOf<List<Int>>()
                    for (i in 0 until flatArray.size step 2) {
                        if (i + 1 < flatArray.size) {
                            result.add(listOf(flatArray[i], flatArray[i + 1]))
                        }
                    }
                    
                    return result
                    
                } else {
                    // 其他格式，尝试递归处理
                    return handleNestedArray(jsonArray)
                }
            }
            
        } catch (e: Exception) {
            // 如果解析失败，返回null而不是抛出异常
            android.util.Log.w("ContourDeserializer", "Failed to parse contour data: ${e.message}")
            return null
        }
        
        return null
    }
    
    /**
     * 处理嵌套数组格式
     */
    private fun handleNestedArray(jsonArray: JsonArray): List<List<Int>>? {
        try {
            val result = mutableListOf<List<Int>>()
            
            for (element in jsonArray) {
                when {
                    element.isJsonArray -> {
                        val subArray = element.asJsonArray
                        if (subArray.size() >= 2) {
                            // 尝试提取前两个元素作为坐标
                            val x = extractInt(subArray.get(0))
                            val y = extractInt(subArray.get(1))
                            if (x != null && y != null) {
                                result.add(listOf(x, y))
                            }
                        }
                    }
                    element.isJsonObject -> {
                        // 如果是对象格式，尝试提取x, y字段
                        val obj = element.asJsonObject
                        val x = obj.get("x")?.asInt
                        val y = obj.get("y")?.asInt
                        if (x != null && y != null) {
                            result.add(listOf(x, y))
                        }
                    }
                }
            }
            
            return if (result.isNotEmpty()) result else null
            
        } catch (e: Exception) {
            android.util.Log.w("ContourDeserializer", "Failed to handle nested array: ${e.message}")
            return null
        }
    }
    
    /**
     * 安全地提取整数值
     */
    private fun extractInt(element: JsonElement): Int? {
        return try {
            when {
                element.isJsonPrimitive && element.asJsonPrimitive.isNumber -> {
                    element.asInt
                }
                element.isJsonArray && element.asJsonArray.size() > 0 -> {
                    // 如果是数组，取第一个元素
                    extractInt(element.asJsonArray.get(0))
                }
                else -> null
            }
        } catch (e: Exception) {
            null
        }
    }
}
