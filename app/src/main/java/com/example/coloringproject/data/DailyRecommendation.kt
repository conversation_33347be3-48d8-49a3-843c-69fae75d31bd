package com.example.coloringproject.data

/**
 * 每日推荐数据类
 */
data class DailyRecommendation(
    val id: String,
    val date: String,
    val title: String,
    val description: String,
    val projectId: String,
    val challengeType: String, // "speed", "perfect", "color_master", "streak"
    val targetScore: Int,
    val rewardPoints: Int,
    val thumbnailUrl: String? = null,
    val difficulty: String = "medium",
    val estimatedTime: Int = 30 // 预估完成时间（分钟）
)

/**
 * 每日推荐响应数据类（匹配后端API）
 */
data class DailyResponse(
    val todayChallenge: ServerDailyProject,
    val featuredProjects: List<ServerDailyProject>,
    val trendingProjects: List<ServerDailyProject>
)

/**
 * 服务器返回的每日项目数据
 */
data class ServerDailyProject(
    val id: String,
    val name: String,
    val displayName: String,
    val description: String,
    val category: String,
    val difficulty: String,
    val totalRegions: Int,
    val estimatedTimeMinutes: Int?,
    val thumbnailUrl: String?,
    val previewUrl: String?,
    val tags: List<String>,
    val challengeReason: String? = null,
    val featuredReason: String? = null,
    val trendingReason: String? = null,
    val downloadCount: Int? = null,
    val rating: Double? = null,
    val releaseDate: String? = null
)

/**
 * 原有的每日推荐响应数据类（保持兼容性）
 */
data class LegacyDailyResponse(
    val todayRecommendation: DailyRecommendation,
    val recentRecommendations: List<DailyRecommendation>,
    val userStats: DailyUserStats? = null
)

/**
 * 用户每日统计数据
 */
data class DailyUserStats(
    val totalChallengesCompleted: Int = 0,
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val totalPoints: Int = 0,
    val averageCompletionTime: Int = 0 // 平均完成时间（分钟）
)

/**
 * 挑战类型枚举
 */
enum class ChallengeType {
    SPEED_CHALLENGE,    // 速度挑战
    PERFECT_MATCH,      // 完美匹配
    COLOR_MASTER,       // 色彩大师
    STREAK_BUILDER      // 连击建造者
}

/**
 * 每日挑战数据类
 */
data class DailyChallenge(
    val id: String,
    val date: String,
    val title: String,
    val description: String,
    val type: ChallengeType,
    val project: com.example.coloringproject.utils.EnhancedAssetManager.ValidatedProject?,
    val targetScore: Int,
    val rewardPoints: Int,
    var isCompleted: Boolean = false,
    var progress: Float = 0f,
    var completedAt: Long? = null
)

/**
 * 用户挑战统计
 */
data class UserChallengeStats(
    val totalChallengesCompleted: Int = 0,
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val totalPoints: Int = 0,
    val challengesThisWeek: Int = 0,
    val challengesThisMonth: Int = 0
)

/**
 * Gallery筛选器
 */
enum class GalleryFilter {
    ALL,            // 全部
    IN_PROGRESS,    // 进行中
    COMPLETED,      // 已完成
    RECENT          // 最近使用
}
