package com.example.coloringproject.data

import com.google.gson.annotations.SerializedName

/**
 * 填色数据模型
 * 对应物料生产系统生成的JSON格式
 */
data class ColoringData(
    @SerializedName("metadata")
    val metadata: Metadata,
    
    @SerializedName("regions")
    val regions: List<Region>,
    
    @SerializedName("color_palette")
    val colorPalette: List<ColorPalette>,
    
    @SerializedName("config")
    val config: Config? = null
)

/**
 * 元数据
 */
data class Metadata(
    @SerializedName("version")
    val version: String,
    
    @SerializedName("source_type")
    val sourceType: String,
    
    @SerializedName("image_size")
    val imageSize: ImageSize,
    
    @SerializedName("total_regions")
    val totalRegions: Int,
    
    @SerializedName("total_colors")
    val totalColors: Int,
    
    @SerializedName("difficulty")
    val difficulty: String,
    
    @SerializedName("estimated_time_minutes")
    val estimatedTimeMinutes: Int
)

/**
 * 图片尺寸
 */
data class ImageSize(
    @SerializedName("width")
    val width: Int,
    
    @SerializedName("height")
    val height: Int
)

/**
 * 填色区域
 */
data class Region(
    @SerializedName("id")
    val id: Int,

    @SerializedName("cluster_id")
    val clusterId: Int? = null,

    @SerializedName("color")
    val color: List<Int>,

    @SerializedName("color_hex")
    val colorHex: String,

    @SerializedName("area")
    val area: Int,

    @SerializedName("pixel_count")
    val pixelCount: Int,

    @SerializedName("pixels")
    val pixels: List<List<Int>>,

    @SerializedName("contour")
    val contour: Any? = null, // 使用Any类型来处理不同格式

    @SerializedName("bounding_box")
    val boundingBox: List<Int>? = null,

    @SerializedName("fill_order")
    val fillOrder: Int? = null,

    // 马赛克预计算数据 - 长期优化
    @SerializedName("edge_pixels")
    val edgePixels: List<List<Int>>? = null,

    @SerializedName("mosaic_blocks")
    val mosaicBlocks: List<MosaicBlock>? = null,

    @SerializedName("mosaic_strategy")
    val mosaicStrategy: String? = null,

    @SerializedName("mosaic_stats")
    val mosaicStats: MosaicStats? = null
)

/**
 * 马赛克块数据
 */
data class MosaicBlock(
    @SerializedName("x")
    val x: Int,

    @SerializedName("y")
    val y: Int,

    @SerializedName("size")
    val size: Int,

    @SerializedName("alpha_factor")
    val alphaFactor: Float = 1.0f
)

/**
 * 马赛克统计信息
 */
data class MosaicStats(
    @SerializedName("total_pixels")
    val totalPixels: Int,

    @SerializedName("edge_pixels")
    val edgePixels: Int,

    @SerializedName("mosaic_blocks")
    val mosaicBlocks: Int,

    @SerializedName("block_size")
    val blockSize: Int
)

/**
 * 调色板
 */
data class ColorPalette(
    @SerializedName("id")
    val id: Int,

    @SerializedName("color_hex")
    val colorHex: String,

    @SerializedName("color_rgb")
    val colorRgb: List<Int>,

    @SerializedName("name")
    val name: String,

    @SerializedName("usage_count")
    val usageCount: Int,

    // 运行时计算的进度信息
    var filledCount: Int = 0,
    var totalCount: Int = 0
) {
    val progressText: String
        get() = if (totalCount > 0) "$filledCount/$totalCount" else "0/0"

    val isCompleted: Boolean
        get() = totalCount > 0 && filledCount >= totalCount

    val progressPercentage: Int
        get() = if (totalCount > 0) (filledCount * 100 / totalCount) else 0
}

/**
 * 配置信息
 */
data class Config(
    @SerializedName("max_colors")
    val maxColors: Int? = null,
    
    @SerializedName("min_region_size")
    val minRegionSize: Int? = null,
    
    @SerializedName("generate_preview")
    val generatePreview: Boolean? = null
)

/**
 * 填色进度状态
 */
data class ColoringProgress(
    val totalRegions: Int,
    val filledRegions: Set<Int> = emptySet(),
    val currentColorIndex: Int = 0,
    val isCompleted: Boolean = false
) {
    val progress: Float
        get() = if (totalRegions > 0) filledRegions.size.toFloat() / totalRegions else 0f
    
    val progressPercentage: Int
        get() = (progress * 100).toInt()
}

/**
 * 填色状态
 */
enum class ColoringState {
    LOADING,
    READY,
    COLORING,
    COMPLETED,
    ERROR
}

/**
 * 触摸事件结果
 */
data class TouchResult(
    val regionId: Int?,
    val colorHex: String?,
    val isValidTouch: Boolean,
    val message: String? = null
)

/**
 * 填色操作记录（用于撤销/重做）
 */
data class ColoringAction(
    val type: ActionType,
    val regionId: Int,
    val colorHex: String,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 操作类型
 */
enum class ActionType {
    FILL,   // 填色
    CLEAR   // 清除
}

/**
 * 填色统计信息
 */
data class ColoringStats(
    val totalRegions: Int,
    val filledRegions: Int,
    val progressPercentage: Int,
    val estimatedTimeRemaining: Int,
    val isCompleted: Boolean,
    val currentStreak: Int = 0, // 连续填色数
    val perfectMatches: Int = 0 // 完美匹配数
)
