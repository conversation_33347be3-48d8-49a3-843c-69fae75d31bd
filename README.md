# 数字填色应用物料生产工具

这是一个用于将彩色设计图转换为数字填色应用可用数据格式的工具。

## 功能特性

### 🎨 核心功能
- **彩色图片输入**：支持PNG、JPG等常见图片格式
- **智能颜色分析**：使用K-means聚类算法自动提取主要颜色
- **区域自动分割**：基于颜色相似性自动识别填色区域
- **线稿生成**：自动生成对应的黑白线稿图
- **数据结构化**：输出标准JSON格式的区域和颜色数据

### 📊 输出内容
- **区域数据**：每个区域的像素坐标、颜色、面积等信息
- **调色板**：提取的颜色列表及使用统计
- **线稿图**：用于显示的黑白轮廓图
- **预览图**：包含原图、线稿、区域分割、调色板的综合预览

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基本使用
```bash
# 处理单张图片
python material_generator.py input.png -o output.json --preview

# 使用自定义参数
python material_generator.py input.png -o output.json --max-colors 15 --min-region-size 200
```

### 3. 测试示例
```bash
# 运行测试脚本
python test_generator.py

# 运行优化测试
python optimize_test.py

# 查看结果分析
python view_results.py
```

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `max_colors` | 20 | 最大颜色数量 |
| `min_region_size` | 100 | 最小区域大小（像素） |
| `edge_threshold_low` | 50 | Canny边缘检测低阈值 |
| `edge_threshold_high` | 150 | Canny边缘检测高阈值 |
| `blur_kernel_size` | 3 | 高斯模糊核大小 |
| `generate_preview` | True | 是否生成预览图 |

## 输出数据格式

### JSON数据结构
```json
{
  "metadata": {
    "version": "1.0",
    "source_type": "colored_image",
    "image_size": {"width": 596, "height": 679},
    "total_regions": 111,
    "total_colors": 12,
    "difficulty": "medium",
    "estimated_time_minutes": 70
  },
  "regions": [
    {
      "id": 1,
      "color": [43, 36, 44],
      "color_hex": "#2b242c",
      "area": 55556,
      "pixel_count": 58234,
      "pixels": [[x1, y1], [x2, y2], ...],
      "fill_order": 1
    }
  ],
  "color_palette": [
    {
      "id": 1,
      "color_hex": "#2b242c",
      "color_rgb": [43, 36, 44],
      "name": "颜色1",
      "usage_count": 58234
    }
  ]
}
```

## 处理流程

```mermaid
graph TD
    A[彩色输入图片] --> B[图像预处理]
    B --> C[K-means颜色聚类]
    C --> D[区域连通性分析]
    D --> E[区域数据提取]
    E --> F[线稿生成]
    F --> G[调色板生成]
    G --> H[数据导出]
    H --> I[JSON数据文件]
    H --> J[线稿PNG文件]
    H --> K[预览PNG文件]
```

## 使用建议

### 参数调优
- **区域过多**：增加 `min_region_size` 参数
- **区域过少**：减少 `min_region_size` 参数
- **颜色过多**：减少 `max_colors` 参数
- **颜色过少**：增加 `max_colors` 参数

### 最佳实践
1. **输入图片**：建议使用色彩鲜明、对比度高的图片
2. **图片尺寸**：建议500-1000像素宽度，过大会影响处理速度
3. **颜色数量**：建议控制在8-20种颜色之间
4. **区域数量**：建议控制在50-150个区域之间

## 测试结果

使用 `origin.png` 测试图片的处理结果：

### 原始参数结果
- 区域数量：493个
- 颜色数量：15种
- 难度等级：困难
- 预估时间：266分钟

### 优化参数结果
- 区域数量：111个 ✅
- 颜色数量：12种 ✅
- 难度等级：中等
- 预估时间：70分钟

## 文件说明

| 文件 | 说明 |
|------|------|
| `material_generator.py` | 主要的物料生成工具 |
| `test_generator.py` | 基础测试脚本 |
| `optimize_test.py` | 优化参数测试脚本 |
| `view_results.py` | 结果分析查看器 |
| `requirements.txt` | Python依赖包列表 |

## 技术架构

### 核心算法
- **K-means聚类**：用于颜色量化和区域分割
- **连通组件分析**：识别独立的填色区域
- **Canny边缘检测**：生成线稿轮廓
- **轮廓检测**：提取区域边界信息

### 依赖库
- `opencv-python`：图像处理
- `scikit-learn`：机器学习算法
- `numpy`：数值计算
- `matplotlib`：图像可视化
- `Pillow`：图像格式支持

## 扩展功能

### 未来可能的改进
1. **GUI界面**：提供可视化的参数调整界面
2. **批量处理**：支持批量处理多张图片
3. **高级算法**：使用更先进的图像分割算法
4. **格式支持**：支持更多输出格式（SVG、XML等）
5. **质量评估**：自动评估生成数据的质量

### 集成建议
- 可以集成到设计师工作流程中
- 可以作为Android应用的后端服务
- 可以开发成Web应用提供在线服务

## 许可证

本项目使用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
