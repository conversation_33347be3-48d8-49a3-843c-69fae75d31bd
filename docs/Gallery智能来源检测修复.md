# Gallery智能来源检测修复

## 问题描述

在之前的修复中，我们为Gallery添加了项目来源信息，但对于没有来源信息的旧保存文件，统一默认为BUILT_IN来源。这导致了一个新问题：

- 项目ID"11"是一个网络项目，但被错误地当作assets项目加载
- 因为assets中没有"11.json"文件，所以加载失败

## 根本原因

旧的保存文件没有projectSource信息，我们的默认策略过于简单，没有考虑项目ID的特征。

## 解决方案

### 智能来源检测逻辑

为Gallery添加了`detectProjectSource`方法，通过以下策略智能检测项目来源：

1. **Assets项目检测**：检查assets文件夹中是否存在对应的JSON文件
2. **下载项目检测**：检查downloaded_projects目录中是否存在项目文件
3. **数字ID规则**：纯数字ID（如"11"）通常是网络项目，使用STREAMING来源
4. **默认策略**：无法确定时默认使用STREAMING来源

### 检测流程

```kotlin
private fun detectProjectSource(projectId: String): ResourceSource {
    // 1. 检查assets项目
    if (isAssetsProject(projectId)) {
        return ResourceSource.BUILT_IN
    }
    
    // 2. 检查下载项目
    if (isDownloadedProject(projectId)) {
        return ResourceSource.REMOTE_DOWNLOADED
    }
    
    // 3. 数字ID检测
    if (projectId.matches(Regex("^\\d+$"))) {
        return ResourceSource.STREAMING
    }
    
    // 4. 默认策略
    return ResourceSource.STREAMING
}
```

### Assets项目检测

```kotlin
private fun isAssetsProject(projectId: String): Boolean {
    val categories = listOf("Animals", "Castles", "Houses", "Plants", "Treehouses")
    
    for (category in categories) {
        val files = assetManager.list(category) ?: continue
        if (files.contains("$projectId.json")) {
            return true
        }
    }
    
    return false
}
```

### 下载项目检测

```kotlin
private fun isDownloadedProject(projectId: String): Boolean {
    val downloadDir = File(context.filesDir, "downloaded_projects/$projectId")
    val jsonFile = File(downloadDir, "$projectId.json")
    val pngFile = File(downloadDir, "$projectId.png")
    
    return jsonFile.exists() && pngFile.exists()
}
```

## 修复效果

### 对于项目ID"11"
- 检测逻辑：纯数字ID → 判定为网络项目
- 来源设置：STREAMING
- 加载策略：ProjectLoadManager使用网络加载逻辑

### 对于其他项目
- "animal-2" → 检测到assets中存在 → BUILT_IN
- "castle-1" → 检测到assets中存在 → BUILT_IN
- 下载的项目 → 检测到downloaded_projects中存在 → REMOTE_DOWNLOADED

## 日志输出

修复后的日志应该显示：
```
MyGalleryFragment: 项目 11 没有来源信息，智能检测结果: STREAMING
MyGalleryFragment: 转换Gallery项目: 11, 来源: null -> STREAMING
```

## 向后兼容性

- ✅ 新保存的项目：有明确的来源信息，直接使用
- ✅ 旧的assets项目：通过文件检测正确识别为BUILT_IN
- ✅ 旧的网络项目：通过ID特征正确识别为STREAMING
- ✅ 旧的下载项目：通过文件检测正确识别为REMOTE_DOWNLOADED

## 测试验证

### 测试项目ID"11"
1. 在Gallery中点击项目ID"11"
2. 预期：智能检测为STREAMING来源
3. 预期：使用网络加载策略，不再报"找不到11.json"错误

### 测试其他项目
1. assets项目（如animal-2）：应该检测为BUILT_IN
2. 下载项目：应该检测为REMOTE_DOWNLOADED
3. 其他数字ID项目：应该检测为STREAMING

## 错误处理

- 检测过程中的异常会被捕获并记录
- 检测失败时默认使用STREAMING来源
- 不会因为检测失败而阻止项目加载

## 性能考虑

- 检测逻辑只在没有来源信息的旧项目上执行
- 文件检测使用简单的exists()调用，性能开销很小
- 检测结果会被缓存在转换后的HybridProject中