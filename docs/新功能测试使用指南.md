# 新功能测试使用指南

## 功能概述

已成功集成两个新功能到ColoringView：

1. **SmartNumberDisplayManager** - 智能数字标记系统
2. **AlignedMosaicRenderer** - 对齐马赛克渲染器

## 快速测试

### 1. 在Activity中添加测试代码

```kotlin
// 在你的Activity中（如EnhancedMainActivity）
class EnhancedMainActivity : AppCompatActivity() {
    
    private lateinit var coloringView: ColoringView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // ... 现有代码 ...
        
        // 在ColoringView数据加载完成后测试新功能
        coloringView.onDataSetupComplete = {
            testNewFeatures()
        }
    }
    
    private fun testNewFeatures() {
        // 延迟执行测试，确保View完全初始化
        coloringView.postDelayed({
            ColoringViewTestHelper.runAllTests(coloringView)
        }, 1000)
    }
}
```

### 2. 查看测试日志

运行应用后，在Logcat中搜索 "ColoringViewTest" 标签，你会看到类似输出：

```
D/ColoringViewTest: === 测试智能数字显示功能 ===
D/ColoringViewTest: 项目总区域数: 156
D/ColoringViewTest: 颜色统计:
D/ColoringViewTest:   颜色 #ff0000: 12 个区域
D/ColoringViewTest:   颜色 #00ff00: 8 个区域
D/ColoringViewTest: 测试缩放级别 2.0x:
D/ColoringViewTest:     颜色 #ff0000: 显示 8 个数字
D/ColoringViewTest: === 测试对齐马赛克功能 ===
D/ColoringViewTest: === 测试性能 ===
D/ColoringViewTest: 数字计算性能: 10次计算耗时 45ms
```

## 功能特点验证

### 智能数字显示
- ✅ 根据区域大小智能显示数字
- ✅ 缩放级别自适应
- ✅ 只显示选中颜色的区域
- ✅ 避免在过小区域显示数字

### 对齐马赛克渲染
- ✅ 马赛克块保持XY轴对齐
- ✅ 边缘与涂色区域精确匹配
- ✅ 支持不同块大小配置
- ✅ 性能优化的批量渲染

## 手动测试步骤

1. **启动应用**并打开一个涂色项目
2. **选择一种颜色**，观察马赛克提醒效果
3. **缩放画布**，观察数字显示的变化：
   - 小缩放：数字较少，只显示大区域
   - 大缩放：数字增多，小区域也显示
4. **切换不同颜色**，验证数字和马赛克的更新
5. **观察马赛克对齐**，确保网格垂直对齐

## 性能监控

新功能已集成性能监控：
- 数字计算时间应 < 50ms
- 马赛克渲染不应影响滑动流畅度
- 内存使用增长应 < 10MB

## 问题排查

### 如果看不到效果：
1. 确认ColoringView已加载数据
2. 确认已选择颜色
3. 确认showHints为true
4. 检查Logcat是否有错误信息

### 如果性能有问题：
1. 检查区域数量是否过多（>500）
2. 调整马赛克块大小
3. 启用缓存优化

## 配置选项

可以通过以下方式调整功能：

```kotlin
// 调整马赛克样式
val extensions = ColoringViewExtensions(coloringView)
extensions.setMosaicStyle(
    blockSize = 8,        // 马赛克块大小
    borderWidth = 1f,     // 边框宽度
    borderColor = Color.BLUE  // 边框颜色
)

// 清除缓存（如果需要）
extensions.clearCache()
```

## 下一步优化

基于测试结果，可以考虑：
1. 调整数字显示阈值
2. 优化马赛克渲染性能
3. 添加更多视觉效果
4. 集成高分辨率支持