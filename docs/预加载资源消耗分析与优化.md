# 预加载资源消耗分析与优化

## 📊 预加载主要占用的资源

### 1. 内存资源（主要消耗）

#### 每个项目的内存占用
```kotlin
// 每个预加载项目包含：
data class Success(
    val coloringData: ColoringData,  // JSON数据解析后的对象
    val outlineBitmap: Bitmap        // PNG位图数据
)
```

#### 具体内存计算
以一个典型项目为例：
- **ColoringData**: 约50-200KB
  - 区域数据（200个区域 × 平均500像素）
  - 调色板数据
  - 元数据
- **outlineBitmap**: 约2-8MB
  - 768×894像素 × 4字节(ARGB) = 2.7MB
  - 实际可能更大，取决于图片复杂度

**单个项目总计**: 约3-10MB内存

### 2. 其他资源消耗

#### CPU资源
- **JSON解析**: 轻量级，几十毫秒
- **PNG解码**: 中等，100-300ms
- **后台线程**: 使用IO线程池，不影响主线程

#### 存储资源
- **无额外存储**: 预加载只在内存中缓存
- **原始文件**: 仍在assets中，不重复占用

## ⚠️ 预加载的上限和风险

### 内存上限分析

#### 设备内存限制
```kotlin
// 典型Android设备的应用内存限制
低端设备: 64-128MB
中端设备: 128-256MB  
高端设备: 256-512MB+
```

#### 预加载项目数量上限
```kotlin
// 当前预加载列表：9个项目
val commonProjects = listOf(
    "animal-1", "animal-2", "animal-3",
    "building-1", "building-2", "building-3",
    "flower-1", "flower-2", "flower-3",
    "mandala-1", "mandala-2", "mandala-3"
)

// 内存占用估算：
9个项目 × 平均5MB = 45MB
```

#### 安全上限建议
- **低端设备**: 最多5-8个项目 (25-40MB)
- **中端设备**: 最多8-12个项目 (40-60MB)
- **高端设备**: 最多12-20个项目 (60-100MB)

### 潜在风险

#### 1. 内存不足 (OOM)
```
风险: 预加载过多项目导致应用崩溃
影响: 应用无法启动或运行中崩溃
概率: 低端设备上较高
```

#### 2. 启动时间延长
```
风险: 预加载过程影响应用启动
影响: 用户感知应用启动变慢
概率: 预加载项目过多时
```

#### 3. 电池消耗
```
风险: 后台预加载消耗电池
影响: 用户体验下降
概率: 预加载频繁时
```

## 🛡️ 智能预加载优化方案

### 1. 动态内存管理

<function_calls>
<invoke name="strReplace">
<parameter name="oldStr">    /**
     * 预加载常用项目列表
     */
    fun preloadCommonProjects() {
        val commonProjects = listOf(
            "animal-1", "animal-2", "animal-3",
            "building-1", "building-2", "building-3", // 添加building系列
            "flower-1", "flower-2", "flower-3",
            "mandala-1", "mandala-2", "mandala-3"
        )
        
        Log.d(TAG, "开始预加载常用项目: ${commonProjects.size}个")
        
        commonProjects.forEach { projectId ->
            // 延迟预加载，避免同时加载太多项目
            preloadScope.launch {
                delay(100) // 每个项目间隔100ms
                preloadProject(projectId)
            }
        }
    }