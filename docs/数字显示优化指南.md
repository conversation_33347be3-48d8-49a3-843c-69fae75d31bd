# 数字显示优化指南

## 🎯 优化目标

专注优化数字显示系统，实现：
1. ✅ **数字大小与区域大小正相关**
2. ✅ **数字按颜色顺序显示（1,2,3...）**
3. ✅ **显示与缩放级别相关**
4. ✅ **只显示当前选中颜色的未填色区域**

## 📁 新增文件

- `NumberDisplayManager.kt` - 核心数字显示管理器
- `NumberDisplayHelper.kt` - ColoringView集成辅助类

## 🔧 集成方法

### 方法1：替换图像坐标系绘制（推荐）

在ColoringView中找到绘制数字的方法，替换为：

```kotlin
// 在drawImportantRegionNumbers方法中替换现有逻辑
private fun drawImportantRegionNumbers(canvas: Canvas) {
    NumberDisplayHelper.drawOptimizedNumbers(
        canvas = canvas,
        regions = hintRegions,
        filledRegions = filledRegions,
        scaleFactor = scaleFactor,
        currentColorHex = currentColorHex,
        visibleRect = calculateVisibleRect()
    )
}
```

### 方法2：替换屏幕坐标系绘制

在ColoringView中找到`drawImportantRegionNumbersInScreenCoords`方法，替换为：

```kotlin
private fun drawImportantRegionNumbersInScreenCoords(canvas: Canvas) {
    NumberDisplayHelper.drawOptimizedNumbersInScreenCoords(
        canvas = canvas,
        regions = hintRegions,
        filledRegions = filledRegions,
        scaleFactor = scaleFactor,
        currentColorHex = currentColorHex,
        matrix = matrix,
        viewWidth = width,
        viewHeight = height
    )
}
```

## 🎨 功能特点

### 数字大小与区域相关
- 大区域显示大数字（最大28px）
- 小区域显示小数字（最小8px）
- 使用平方根缩放，避免数字过大

### 颜色顺序编号
- 按颜色值排序，确保一致性
- 第一种颜色显示"1"，第二种显示"2"，以此类推
- 颜色映射会缓存，避免重复计算

### 缩放级别控制
- 缩放 < 1.2x：不显示数字
- 缩放 >= 1.2x：开始显示数字
- 区域显示尺寸 < 25px：不显示数字

### 智能过滤
- 只显示当前选中颜色
- 只显示未填色区域
- 只显示可见区域内的数字
- 按区域大小排序，大区域优先

## 📊 配置参数

可以在`NumberDisplayManager.kt`中调整：

```kotlin
private const val MIN_SCALE_FOR_NUMBERS = 1.2f // 最小缩放级别
private const val MIN_REGION_SIZE_FOR_NUMBER = 25f // 最小区域尺寸
private const val BASE_NUMBER_SIZE = 14f // 基础数字大小
private const val MAX_NUMBER_SIZE = 28f // 最大数字大小
private const val MIN_NUMBER_SIZE = 8f // 最小数字大小
```

## 🧪 测试验证

### 快速测试步骤：
1. 启动应用，选择一种颜色
2. 观察数字是否按1,2,3...顺序显示
3. 缩放画布，观察数字大小变化
4. 切换颜色，验证数字编号一致性
5. 涂色后确认数字消失

### 调试信息：
```kotlin
// 查看颜色映射
val colorMapping = NumberDisplayHelper.getColorMappingInfo(regions)
Log.d("ColorMapping", "颜色映射: $colorMapping")
```

## 🎯 预期效果

### 数字显示逻辑：
- 红色区域 → 显示"1"
- 蓝色区域 → 显示"2"  
- 绿色区域 → 显示"3"
- ...以此类推

### 大小关系：
- 100px区域 → 14px数字
- 200px区域 → 20px数字
- 400px区域 → 28px数字（最大）

### 缩放响应：
- 0.8x缩放 → 不显示数字
- 1.5x缩放 → 显示中等数字
- 3.0x缩放 → 显示大数字

## 🔍 验证要点

- [ ] 数字按颜色顺序显示（1,2,3...）
- [ ] 数字大小与区域大小成正比
- [ ] 小缩放级别不显示数字
- [ ] 只显示当前选中颜色
- [ ] 已填色区域不显示数字
- [ ] 过小区域不显示数字

## 🐛 故障排除

### 数字不显示：
1. 检查缩放级别是否 >= 1.2
2. 确认已选择颜色
3. 确认有未填色的匹配区域
4. 检查区域大小是否足够

### 数字顺序错误：
1. 确认颜色映射是否正确
2. 检查颜色标准化是否一致
3. 查看调试日志中的颜色映射

### 数字大小异常：
1. 调整BASE_NUMBER_SIZE参数
2. 检查区域边界框数据
3. 验证缩放计算是否正确

## 📈 性能特点

- 颜色映射缓存，避免重复计算
- 优先使用边界框，提升性能
- 按区域大小排序，优先显示重要数字
- 可见区域裁剪，减少绘制数量

这个优化版本专注于数字显示，不会影响现有的马赛克绘制逻辑。