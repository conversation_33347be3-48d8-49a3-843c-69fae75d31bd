*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
/.idea/AugmentWebviewStateStore.xml
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
local.properties
.md
/.idea/

/advanced_monkey_monitor.py
/app/src/main/java/com/example/coloringproject/BenchmarkActivity.kt
/app/src/main/assets/castle/castle-6.json
/app/src/main/assets/castle/castle-6.png
/app/src/androidTest/java/com/example/coloringproject/ColoringAppCoreFlowTest.kt
/app/src/androidTest/java/com/example/coloringproject/ColoringAppFunctionalTest.kt
/app/src/androidTest/java/com/example/coloringproject/ColoringFeatureTest.kt
/app/src/androidTest/java/com/example/coloringproject/ColoringUIAutomationTest.kt
/app/src/androidTest/java/com/example/coloringproject/ComprehensiveTestRunner.kt
/app/src/main/java/com/example/coloringproject/utils/CrashHandler.kt
/app/src/androidTest/java/com/example/coloringproject/CrashTestSuite.kt
/app/src/main/java/com/example/coloringproject/MemoryAnalysisActivity.kt
/app/src/main/java/com/example/coloringproject/utils/MemoryManager.kt
/app/src/main/java/com/example/coloringproject/utils/MemoryPressureManager.kt
/NetworkFunctionTest.kt
/app/src/androidTest/java/com/example/coloringproject/PerformanceTestSuite.kt
/.vscode/settings.json
/app/src/main/res/drawable/splash_gradient_background.xml
/app/src/androidTest/java/com/example/coloringproject/StressTestSuite.kt
/app/src/main/java/com/example/coloringproject/TestLauncherActivity.kt
/app/src/main/java/com/example/coloringproject/TestRunnerActivity.kt
